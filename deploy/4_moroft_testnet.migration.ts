import { Deployer, Reporter } from '@solarity/hardhat-migrate';

import { MOROFT__factory } from '@/generated-types/ethers';

const lzEndpointV2 = '******************************************';

module.exports = async function (deployer: Deployer) {
  const mor = await deployer.deploy(MOROFT__factory, [
    lzEndpointV2,
    '******************************************',
    '******************************************',
  ]);
  Reporter.reportContracts(['MOROFT', await mor.getAddress()]);
};

// npx hardhat migrate --only 4
// npx hardhat migrate --network arbitrum_sepolia --only 4 --verify
// npx hardhat migrate --network sepolia --only 4 --verify
// npx hardhat migrate --network mumbai --only 4 --verify
// npx hardhat migrate --network base_sepolia --only 4 --verify
