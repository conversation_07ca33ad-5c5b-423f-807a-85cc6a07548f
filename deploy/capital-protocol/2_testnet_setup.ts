import { Deployer } from '@solarity/hardhat-migrate';
import { ethers } from 'hardhat';

import {
  ChainLinkDataConsumer__factory,
  DepositPool__factory,
  Distributor__factory,
  ERC20Mock__factory,
  L1SenderV2__factory,
} from '@/generated-types/ethers';
import { ZERO_ADDR } from '@/scripts/utils/constants';
import { wei } from '@/scripts/utils/utils';

// Ethereum Sepolia
const config = {
  cldkAddress: '******************************************',
  rewardPoolAddress: '******************************************',
  l1SenderV2Address: '******************************************',
  distributorAddress: '******************************************',
  depositPoolStEthAddress: '******************************************',
  depositPoolLinkAddress: '******************************************',
  layerZeroConfig: {
    gateway: '******************************************',
    receiver: '******************************************',
    receiverChainId: '10231',
    zroPaymentAddress: '******************************************',
    adapterParams: '0x',
  },
};

module.exports = async function (deployer: Deployer) {
  const block = await ethers.provider.getBlock('latest');
  const now = block ? block.timestamp : 1;

  const chainLinkDataConsumer = await deployer.deployed(ChainLinkDataConsumer__factory, config.cldkAddress);
  const l1SenderV2 = await deployer.deployed(L1SenderV2__factory, config.l1SenderV2Address);
  const distributor = await deployer.deployed(Distributor__factory, config.distributorAddress);
  const depositPoolStEth = await deployer.deployed(DepositPool__factory, config.depositPoolStEthAddress);
  const depositPoolLink = await deployer.deployed(DepositPool__factory, config.depositPoolLinkAddress);
  const stETH = await deployer.deployed(ERC20Mock__factory, await depositPoolStEth.depositToken());
  const stETHAddress = await stETH.getAddress();
  const link = await deployer.deployed(ERC20Mock__factory, await depositPoolLink.depositToken());
  const linkAddress = await link.getAddress();

  //// SETUP ChainLink Data Consumer
  await chainLinkDataConsumer.updateDataFeeds(['ETH/USD'], [['******************************************']]);
  await chainLinkDataConsumer.updateDataFeeds(['LINK/USD'], [['******************************************']]);

  //// SETUP L1SenderV2
  await l1SenderV2.setDistributor(await distributor.getAddress());
  await l1SenderV2.setStETh(stETHAddress);
  await l1SenderV2.setLayerZeroConfig(config.layerZeroConfig);

  //// SETUP Distributor
  await distributor.setMinRewardsDistributePeriod(60 * 60 * 10); // 10 minutes
  for (let i = 0; i < 5; i++) {
    await distributor.setRewardPoolLastCalculatedTimestamp(i, now - 300);
  }
  // Add deposit pools
  await distributor.addDepositPool(0, config.depositPoolStEthAddress, stETHAddress, 'ETH/USD', 0);
  for (let i = 1; i < 5; i++) {
    await distributor.addDepositPool(i, config.depositPoolStEthAddress, ZERO_ADDR, '', 1);
  }
  await distributor.addDepositPool(0, config.depositPoolLinkAddress, linkAddress, 'LINK/USD', 2);

  //// SETUP Deposit Pools
  await depositPoolStEth.setRewardPoolProtocolDetails(0, 600, 300, 120, wei(0.0001));
  await depositPoolLink.setRewardPoolProtocolDetails(0, 1200, 600, 240, wei(0.0002, 6));

  await depositPoolStEth.migrate(0);
  await depositPoolLink.migrate(0);

  //// CHECK, comment unused lines
  // await stETH.approve(config.depositPoolStEthAddress, wei(9999));
  // await depositPoolStEth.stake(0, wei(0.02), 0, ZERO_ADDR);
  // await stETH.transfer(config.distributorAddress, wei(1));
  // await depositPoolStEth.withdraw(0, wei(0.01));
  // await stETH.transfer(config.distributorAddress, wei(1));
  // await depositPoolStEth.claim(0, (await deployer.getSigner()).getAddress(), { value: wei(0.02) });

  // await link.approve(config.depositPoolLinkAddress, wei(9999));
  // await depositPoolLink.stake(0, wei(0.02), 0, ZERO_ADDR);
  // await depositPoolLink.withdraw(0, wei(0.01));
  await depositPoolLink.claim(0, (await deployer.getSigner()).getAddress(), { value: wei(0.1) });
};

// npx hardhat migrate --path-to-migrations ./deploy/capital-protocol --only 2
// npx hardhat migrate --path-to-migrations ./deploy/capital-protocol --network sepolia --only 2
