[{"id": "0x01c9c97ce69569af244b25f299f3cc830d98240094d6262b4ad010022d689f73", "name": "<PERSON><PERSON><PERSON>", "admin": "0xd87091bcf26332c501a08f90f9aad99a7c9045c3", "startsAt": "1737124200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "7", "users": ["******************************************", "******************************************", "******************************************", "******************************************", "******************************************", "******************************************", "******************************************"], "description": "Secure and decentralized platform for buying, selling and mining Bitcoin", "website": "https://www.lumerin.io/"}, {"id": "0x0365447f7c98fa1070f467f2cc220de7b0b9c17b9664336c10e26ee3182f39d1", "name": "Morpheus Node", "admin": "******************************************", "startsAt": "1738450800", "minimalDeposit": "10000000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1740816000", "totalUsers": "12", "users": ["******************************************", "******************************************", "******************************************", "******************************************", "******************************************", "******************************************", "******************************************", "******************************************", "******************************************", "******************************************", "******************************************", "******************************************"], "description": "Morpheus Node Written in Node JS Programming Language", "website": "https://github.com/domsteil/morpheus-node.git"}, {"id": "0x03fa50361370455816211100c40123006f5f15b53b290735edc0810fd61e77fd", "name": "<PERSON><PERSON><PERSON>", "admin": "0xb56bed300f084e308c7eab17c175fdc40ef3885f", "startsAt": "1737124200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "4", "users": ["******************************************", "******************************************", "******************************************", "******************************************"], "description": "Stats and tools for the Morpheus Community", "website": "https://morlord.com/"}, {"id": "0x0a5ade57dfa9217dd1e955ca68468dfb249430c89c5f41b0c729e04db87a0563", "name": "DecentraNet", "admin": "0x36af03d425688183b502cc83dee92d0a8f27fdda", "startsAt": "1740196800", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "604800", "claimLockEnd": "1740801600", "totalUsers": "0", "users": [], "description": "", "website": ""}, {"id": "0x0b4e662f0a3480ee9117e3c2f0559d3f3295eac04ea52f84c1c5893281f22bb4", "name": "Morlord Shared", "admin": "0x72f457d6237f66f68b6ea4408a3219ab7ae13be8", "startsAt": "1739174400", "minimalDeposit": "1000000000000", "withdrawLockPeriodAfterDeposit": "604800", "claimLockEnd": "604800", "totalUsers": "0", "users": [], "description": "", "website": ""}, {"id": "0x0c8d5f4c48826aeecff5b2defb4314351a3ca7f93f7b41d8bb99c47e3aae1360", "name": "coincap", "admin": "0x3b438cc593d579627089f0d99bc5f0bb5151c6ce", "startsAt": "1737145300", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "604800", "claimLockEnd": "0", "totalUsers": "65", "users": ["0x004c6672a94923db562c22e783a6d49b125e8976", "******************************************", "0x0bb67cd6b6049d8a63b03045a1e4ec02b6100483", "0x0c7d272a93ab733fd2bf6dddeab5a1b50e5339d8", "******************************************", "0x109b32c12f4e8da06aa71492bcdf6c54626617f9", "******************************************", "0x2187ec51e6915aa7fde7c275ed9f8a8d5258c2d1", "0x24eb74038f68f14d03c7bc278de1823fb2565591", "0x2523008f1efd00551ab288f342efc481286132db", "0x271655d1c2f26fc7cb46d3616cbd854cd6b2a70b", "0x2916e2cd96d1c522930a07e55aaad0a634656171", "******************************************", "0x2c1e54f815529467be8f3b78e46c14168852322a", "0x3ae05e268f495b49ca495d7b779cdeb55cc37ba0", "0x3fe0f75e7cfc652d305fdf904644313e9091bf34", "0x4a6f6557909906e0446347293fc2d19927abd9f0", "0x5648055f9c408f0557a8cefe0c8dc33df6479011", "0x59708a095fb44a9054f9ba3b2fb82fce07b182c1", "******************************************", "0x5fdcf35c9e77fa6ce0059fc6f41532c87021a79e", "0x606f26dcd69745ca645e709accaf54ede92eb38b", "0x62279ff21053c9b1e0f16e2a6aa2c93da28ad4dd", "******************************************", "0x69e24369bfeda80348bd5104df01c4d8b33ecff9", "0x69f41a9e51fa997ef4138c73dc1aae6beb3ddd0a", "0x6ba1b6360dc80981751499c7daf1f44288c7b117", "0x714e96d76ebf67ce963df0d6249731759029e217", "0x776cda090681a0157ee3a559487695809eef0259", "0x7bbb7a90ca1db2539401a3d423aacd7f4614ffca", "0x7f18b5907410e8759321383094cd84e1d304ec79", "0x81039d59cd0fccd972262682c3711ddb5c69f907", "0x815612815d7fb01b1e8a97fe4a0996e77245a3aa", "0x858c1d7a1129911d07aee228a60e94a618f2cbc3", "0x8951368489a788ea9ba932782e2e6639b178fd4a", "0x8a65ac0e23f31979db06ec62af62b132a6df4741", "0x8c3cca433f198d8a0c72d549cad79a55da2b3ac5", "******************************************", "******************************************", "0xa69acc6667779f2e9bd7806368f580f5216f577c", "0xaa66853d3071c98b2b3c968d2ee8cef6c77cdf61", "0xb9727d621724264a77f68855b34ea677f9ada92e", "0xba26035b9cd76cda5b767a966b8a3392e476fc0f", "******************************************", "0xc091d226be31ce9e18f33dbddf08d0c4ad79de21", "******************************************", "******************************************", "******************************************", "******************************************", "0xcc12a8ecbefcebdf71ef2105e5b87dc9b5a0caa8", "0xcd0dadab45baf9a06ce1279d1342ecc3f44845af", "0xcfd51ed5af62f6921398a92e056bcbb2e216bedb", "0xd21e2f98305543a34dc82da2158808ecd00b2e65", "0xd27e68ed0471c18e9e2160c31a1a64761db45a61", "0xd4d554146521539ce7502719ea3d9ab7b1a072ef", "******************************************", "******************************************", "******************************************", "******************************************", "******************************************", "******************************************", "******************************************", "******************************************", "******************************************", "******************************************"], "description": "Reliable cryptocurrency prices and market capitalizations", "website": "https://coincap.io/"}, {"id": "0x1df41cd916959d1163dc8f0671a666ea8a3e434c13e40faef527133b5d167034", "name": "Staking", "admin": "******************************************", "startsAt": "1740672000", "minimalDeposit": "0", "withdrawLockPeriodAfterDeposit": "604800", "claimLockEnd": "1814418000", "totalUsers": "1", "users": ["******************************************"], "description": "", "website": ""}, {"id": "0x1fa03df9ba7caac7b490ee9df117fa2b17408d3a6a84037f806b7b89c3f9d938", "name": "Project Cloud", "admin": "******************************************", "startsAt": "1738386000", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1767243600", "totalUsers": "0", "users": [], "description": "", "website": ""}, {"id": "0x25c16934a56ededa389e347f693f39caa3596a7aff16a3eab9f74bd3b731cf6e", "name": "brainpower", "admin": "******************************************", "startsAt": "1738033200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "1814400", "claimLockEnd": "1739577600", "totalUsers": "8", "users": ["******************************************", "0x59708a095fb44a9054f9ba3b2fb82fce07b182c1", "0x896c20da40c2a4df9b7c98b16a8d5a95129161a5", "******************************************", "0xb9075632ce259e5ffe6cb693845f66cb1826a527", "******************************************", "******************************************", "******************************************"], "description": "Brainpower Podcast", "website": "https://open.spotify.com/show/56eTDgUARPiaUntXLnxIGg"}, {"id": "0x25e349b6e220e73d277d80c4e8279b5bfe33b0adafa90193ab1dc763e5ed5e72", "name": "Near", "admin": "0x82eaaaadfec2e4fc2003ac9d4fe312de1c468821", "startsAt": "1737124200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "3", "users": ["******************************************", "******************************************", "******************************************"], "description": "Near Blockchain For AI", "website": "https://near.org/"}, {"id": "0x26d54d3de64a94b1ecef7c0e1437858999634e53331a9ceaccfe29f034d65386", "name": "MOR LISTINGS", "admin": "0x95a83fb7e87e210c1b32a14b0fd9c84100d6a69d", "startsAt": "1739606400", "minimalDeposit": "2000000000000000", "withdrawLockPeriodAfterDeposit": "604850", "claimLockEnd": "1742022000", "totalUsers": "0", "users": [], "description": "", "website": ""}, {"id": "0x2bb5cba5aa810086dd76d5a20c06616f9d7229b710078a809c1ad5654ebdb3be", "name": "Supertech.ai", "admin": "0x8544e213db4da5a5eef48d4d97c376e5d2d09a60", "startsAt": "1741158000", "minimalDeposit": "1000000000000000000", "withdrawLockPeriodAfterDeposit": "1209600", "claimLockEnd": "1743832800", "totalUsers": "0", "users": [], "description": "", "website": ""}, {"id": "0x2cef520db0d87be977cc612c0c57e2ef1788fcd8e7032418be1cccea82a603f4", "name": "ZO.ME", "admin": "0xbbdea6172be7798f7fa37e7e5432d9426954c1de", "startsAt": "1737124200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "2", "users": ["******************************************", "******************************************", "0xee7063160f39b4c1b73228b5292919dcae9f8bc5"], "description": "Chat with friends & AI", "website": "https://zo.me/"}, {"id": "0x3082ff65dbbc9af673b283c31d546436e07875a57eaffa505ce04de42b279306", "name": "Agent Access", "admin": "0xc2ff7040233afb5c117f9fb128d1d08d1bf351ba", "startsAt": "1738438140", "minimalDeposit": "10000000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1740816000", "totalUsers": "10", "users": ["******************************************", "0x2835e92c3d195d6c2480027cd6bbbe9419a58977", "0x62279ff21053c9b1e0f16e2a6aa2c93da28ad4dd", "******************************************", "******************************************", "******************************************", "******************************************", "******************************************", "0xf89fd2e2180d7f7de89a499cbef8e3ee49429502", "******************************************"], "description": "Access to Agents", "website": ""}, {"id": "0x36aea6a8fbd9382c147c110ff07de962300818995b9961a8fea131d5efc1b1db", "name": "Flock.io", "admin": "0x372663a3629cbfd377c94d5962bd0b62c824bd4a", "startsAt": "1737124200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "5", "users": ["******************************************", "******************************************", "0x896c20da40c2a4df9b7c98b16a8d5a95129161a5", "******************************************", "******************************************"], "description": "Federated Machine Learning On the Blockchain", "website": "https://www.flock.io/"}, {"id": "0x3f73d9b00b869a0eebb5325b37c807252a1307bd5390ecde1348aa11233703b2", "name": "MorpheusAI", "admin": "0x762bdda2cd7ca2760922694b034f10a73a5de0d4", "startsAt": "1738386000", "minimalDeposit": "100000000000000000", "withdrawLockPeriodAfterDeposit": "604800", "claimLockEnd": "1740718800", "totalUsers": "5", "users": ["******************************************", "0x59708a095fb44a9054f9ba3b2fb82fce07b182c1", "0x62279ff21053c9b1e0f16e2a6aa2c93da28ad4dd", "******************************************", "******************************************"], "description": "Morpheus AI is the first agent powered by the Z10N framework that makes gamifying agents simple. Z10N will make creating an agentic economy a few clicks and prompts while creating an entire ecosystem for their communities to engage.", "website": "https://www.z10n.ai"}, {"id": "0x40aa9fbd2fde2e6a38510ef730aee171c3e81a4c165bccbddefe4c89b55b2a8d", "name": "Panorama", "admin": "0x25b7a50429d89a6e13dff47ccbe0e54e6cfc65b0", "startsAt": "1738684800", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "2", "users": ["******************************************", "******************************************"], "description": "Panorama Block is an initiative being developed in collaboration between faculty and students from UCLA and other leading universities worldwide. We're building an AI-powered data hub infrastructure designed to automate DeFi plays through the deployment, commercialization, and aggregation of AI agents.", "website": "https://panoramablock.com/"}, {"id": "0x42f02b08eadb43dbc23783822aeba31623e22e2c95e608e48590adb48e7856bf", "name": "Sapien AI", "admin": "0xb5d104ce51a488eb34c087ec0a245031814c1a52", "startsAt": "1737124200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "3", "users": ["******************************************", "******************************************", "******************************************"], "description": "Train AI with Expert Human Feedback", "website": "https://www.sapien.io/"}, {"id": "0x4501d4e79230adce800ff26e5d2e2ae061f1e54da8508fe17dd1462388fb8373", "name": "IamAI-Core", "admin": "******************************************", "startsAt": "1737748800", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "10", "users": ["0x0268ced398bd94dfe78d236f53e3c1475763eb20", "******************************************", "0x2ff08cc1f3d8f58d1bb302497c2776ab31a60c3b", "0x370feb9dad2062917b4ee99ebb6af3c1f0ccbecd", "0x4b59f3f16f2a6bc7bacb158b1eb8b4b49177d8ab", "******************************************", "0x809f38ddc9106de632fb87eb41966e6399131479", "******************************************", "0xafebb65d3edebcd485c5fa91f5c635468e6b8267", "0xb66c25f1e406d405ffaaab2b82010102b35212d0"], "description": "I am AI Core", "website": "https://github.com/iamai-core"}, {"id": "0x4b3bbe93cd84e4304f785cbf55be1b9cfc23e73c7869704321f5a906e671930f", "name": "Mor Builders", "admin": "0x1cb2bc7ef28b2e127a2026fcdd2bf2fc27750525", "startsAt": "1738051200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1738137600", "totalUsers": "0", "users": [], "description": "", "website": ""}, {"id": "0x4c25cd7db02a0d0354010749554d260fec635455aca30f7b4001c222d14d4294", "name": "Manifest Network", "admin": "0xe7432b5415e8b3dba4a6ae167e89648a1051cb7f", "startsAt": "1737124200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "5", "users": ["******************************************", "******************************************", "0x7246781f46b2cfdd4567c85daeae5fa8ae3d8d17", "******************************************", "******************************************"], "description": "Decentralized AI Platform", "website": "https://manifestai.org/"}, {"id": "0x4c2afb6295a12128fa7f9e601a1d77c646229a96dd639e886476a8c216b69844", "name": "PALcapital Z", "admin": "0x920d2b328f2058516496f932f6247d9347c594d2", "startsAt": "1738260900", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "0", "users": [], "description": "", "website": ""}, {"id": "0x4cba0f501b4603ad2e029e8ae5880befcb3e9d13c9e14987117374f2d87ad17b", "name": "AI art by Venice", "admin": "0x35f1bae79c4e74ee25354d326b42f809744f7e18", "startsAt": "1742713200", "minimalDeposit": "10000000000000000000", "withdrawLockPeriodAfterDeposit": "2600000", "claimLockEnd": "1745445600", "totalUsers": "0", "users": [], "description": "", "website": ""}, {"id": "0x4cbaac02f4c2ff6d72454ec7dc0a7ed0cbd06dff9d70001ab0d0e84508dd2efe", "name": "Lifted", "admin": "0xb081870678d563a4d1cf0ab189ab2038695827a3", "startsAt": "1737124200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "2", "users": ["******************************************", "******************************************"], "description": "", "website": ""}, {"id": "0x4f42bb9dee66865e920f676995b247c29add563a33a995ea2c90c4e66c10b1f9", "name": "Airvey Advisory", "admin": "0xeda4b0cb9507d69b3ce45c86d199a62cb474514b", "startsAt": "1742184000", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "604800", "claimLockEnd": "1744862400", "totalUsers": "2", "users": ["0x198af137237feb6e01c3492f6be17cc7f07c4df1", "0xeda4b0cb9507d69b3ce45c86d199a62cb474514b"], "description": "Supporting Contributors in Building on MOR's Open-Source AI Infrastructure.", "website": "http://www.airvey.io"}, {"id": "0x50a52d70c4c3b4b77d8fefaa24faf038450a656b9db6bc2e9d897f55ff72e974", "name": "<PERSON><PERSON><PERSON><PERSON>", "admin": "******************************************", "startsAt": "1739736000", "minimalDeposit": "10000000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739750400", "totalUsers": "0", "users": [], "description": "", "website": ""}, {"id": "0x523f1bb39a49927241fcd48dacab09dd72c218bf1e3a01e3206264fd666f0d79", "name": "OLAS", "admin": "0xb04b996738f69ddad9099acb040e500c2492446d", "startsAt": "1737124200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "5", "users": ["******************************************", "******************************************", "******************************************", "******************************************", "******************************************"], "description": "Own a share of AI, specifically autonomous agent economies", "website": "https://olas.network/"}, {"id": "0x525299b81b12c7478c98f0fc95f3b3a6a2c93ff62810c08c733a2727bb8e860b", "name": "Builder Register1", "admin": "0x8f2ce96fa97794e7cf468f43abd8ce21e64b93db", "startsAt": "1737072000", "minimalDeposit": "1000000000000000000", "withdrawLockPeriodAfterDeposit": "604800", "claimLockEnd": "1738281600", "totalUsers": "0", "users": [], "description": "", "website": ""}, {"id": "0x5c92edba5c98bd16497d97c0f5b37dfb696e9330bd12b06804bc45a6d0a619f0", "name": "Bloq", "admin": "0xf8d5ca35466a7eaba3fb7f9985d8ecc830af0667", "startsAt": "1737124200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "1", "users": ["******************************************", "******************************************"], "description": "Infrastructure & Applications for Web3", "website": "https://bloq.com/"}, {"id": "0x638451aa95394a1ea79bbf9d15eb532b5beed7f3831ccc8db278654ae30dad27", "name": "Virtuals", "admin": "0x64af3260da78548ad2f9be06ae77904690684460", "startsAt": "1737124200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "2", "users": ["******************************************", "******************************************", "******************************************"], "description": "Launchpad for AI Agents", "website": "https://app.virtuals.io/"}, {"id": "0x63891e659ff2ae88fdaf6ff05f07ffbbd4ffbca089600564c7c6c784a96ab0e5", "name": "Wire Network", "admin": "0x3caac83717157d7e9b14545803f36ff1650b467f", "startsAt": "1737124200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "34", "users": ["******************************************", "******************************************", "0x109b32c12f4e8da06aa71492bcdf6c54626617f9", "******************************************", "0x1f16a0fc4a130c3436fe210cfb46e12f59f411dc", "0x210d9386c1249391025e05239a09f5bb8f3fd940", "0x2935214a369e2bccbaf253432d028b1bc5164e3c", "******************************************", "0x59708a095fb44a9054f9ba3b2fb82fce07b182c1", "******************************************", "******************************************", "0x6820250308b5ea5044672a297b2529e627787f5f", "******************************************", "0x69f41a9e51fa997ef4138c73dc1aae6beb3ddd0a", "0x6ba1b6360dc80981751499c7daf1f44288c7b117", "******************************************", "0x7412bc256355abd22dd53de3a38e8995b5d4c1d1", "0x776cda090681a0157ee3a559487695809eef0259", "0x77708fb61608149c1735846791a3e0d98598c40c", "0x98eff980c57c9d333340b3856481bf7b8698987c", "******************************************", "0x9cc6b421bec55e255ff5f62d3411a1421ea188f7", "******************************************", "0xa2fdc2ddf5e652af96dae3c9d16e34c9824e99ad", "0xba26035b9cd76cda5b767a966b8a3392e476fc0f", "0xbb579a7972d87e0574fda9632fb98c092f381c34", "0xbc3c6091b51794a907121e4568ced955532c8853", "******************************************", "******************************************", "0xd5d47e5237366e56eff12014763bfd2d13a8d8eb", "0xdf17d00baba73402ab804a29ac7689ad5e30102d", "0xe6e438832930fc3c3ab3bd701d00b0f63de8694a", "0xf54e3eec141204b90c1d520334c26d5d1e3c4b39", "******************************************"], "description": "The Blockchain for the AI Economy", "website": "https://www.wire.network/"}, {"id": "0x6523f7e4f8418318d5361681a4f3d499bce60b3d8118b32d02ef295572304aff", "name": "Snowball Money", "admin": "0x2eac34dce8a6090133fce494e3ebc6badd3b7d9c", "startsAt": "1741039200", "minimalDeposit": "50000000000000000000", "withdrawLockPeriodAfterDeposit": "864000", "claimLockEnd": "1761775200", "totalUsers": "0", "users": [], "description": "", "website": ""}, {"id": "0x664823f3e70c7f2788e8d393bffbfe1c6cb463d6af29b43933af6cfd7944588f", "name": "AI Art by Venice", "admin": "0xdb0f1da2f916761beaaa1f58f65b0f1b31d31062", "startsAt": "1742684400", "minimalDeposit": "10000000000000000000", "withdrawLockPeriodAfterDeposit": "2600000", "claimLockEnd": "1745359200", "totalUsers": "1", "users": ["0xdb0f1da2f916761beaaa1f58f65b0f1b31d31062"], "description": "", "website": ""}, {"id": "0x6853bdaac0b14b032091c3495bfe4cf14ba89b86b0859ffdd937f17361befdf1", "name": "NounspaceTom", "admin": "0x06ae622bf2029db79bdebd38f723f1f33f95f6c5", "startsAt": "1739174400", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "2", "users": ["******************************************", "0x896c20da40c2a4df9b7c98b16a8d5a95129161a5"], "description": "The First Smart Agent on Morpheus / Venice / Nounspace", "website": "https://x.com/nounspacetom"}, {"id": "0x6a117ee75d9c3c44c18cf44009429034ad02bd2e14fc3e982e63bdafcd55625c", "name": "Tales & Conquests", "admin": "0x55d6c73306a7af0e68c004147f86a98f16130b3b", "startsAt": "1738686600", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "9", "users": ["******************************************", "******************************************", "0xa75eb7cbe546de14d731292cf564bc046e2efa76", "0xc05326281076a0f55ea07aa4a6e689b8cee28899", "0xc091d226be31ce9e18f33dbddf08d0c4ad79de21", "******************************************", "0xceebaf584e8b9eaf6f9da7cec4b4fbf1767946fa", "******************************************", "******************************************"], "description": "", "website": ""}, {"id": "0x6aa0f87703b4fc532743d2560ab8deac31a456360ccb7ad22770103d9061b9f0", "name": "MOR API Access", "admin": "******************************************", "startsAt": "1741420800", "minimalDeposit": "5000000000000000000", "withdrawLockPeriodAfterDeposit": "604800", "claimLockEnd": "1741935600", "totalUsers": "0", "users": [], "description": "Access Morpheus compute with a Rest API key and a few lines of code.", "website": "https://x.com/Player1Taco"}, {"id": "0x6d4a00233c2192694cf0dc52e0bdfd73e136bd1338fcfced149989e7fa6bbdab", "name": "<PERSON><PERSON>", "admin": "0x370feb9dad2062917b4ee99ebb6af3c1f0ccbecd", "startsAt": "1740726000", "minimalDeposit": "1000000000000000000", "withdrawLockPeriodAfterDeposit": "604800", "claimLockEnd": "1740985200", "totalUsers": "4", "users": ["0x370feb9dad2062917b4ee99ebb6af3c1f0ccbecd", "******************************************", "******************************************", "******************************************"], "description": "Decentralized AI prediction of live realworld data.", "website": "https://www.satorinet.io/"}, {"id": "0x70a8b9982ce25814a05d0cbc53e14d6e2ebc073620790482401e19b84f2bad64", "name": "Supercycle", "admin": "******************************************", "startsAt": "1740805200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "604800", "claimLockEnd": "1741323600", "totalUsers": "0", "users": [], "description": "", "website": ""}, {"id": "0x7169601aa382123b982cb9055874ed5ccb7223a5500a2b41aedf013653d3f167", "name": "Morlord MOR Staking", "admin": "0x72f457d6237f66f68b6ea4408a3219ab7ae13be8", "startsAt": "1737748800", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "31", "users": ["******************************************", "0x109b32c12f4e8da06aa71492bcdf6c54626617f9", "0x2187ec51e6915aa7fde7c275ed9f8a8d5258c2d1", "0x2523008f1efd00551ab288f342efc481286132db", "******************************************", "0x2c1e54f815529467be8f3b78e46c14168852322a", "0x35f1bae79c4e74ee25354d326b42f809744f7e18", "0x3ae05e268f495b49ca495d7b779cdeb55cc37ba0", "******************************************", "0x50fa50fa2032d85eb2dda303929bf56886aa9afb", "******************************************", "0x606f26dcd69745ca645e709accaf54ede92eb38b", "0x6f961561ff1f10193f2a2397d97f6eee32ab6a0f", "0x72f457d6237f66f68b6ea4408a3219ab7ae13be8", "0x776cda090681a0157ee3a559487695809eef0259", "0x7bbb7a90ca1db2539401a3d423aacd7f4614ffca", "0x815612815d7fb01b1e8a97fe4a0996e77245a3aa", "******************************************", "0x9b6e01454fa30d52467a55f0d31c06a9d77ff776", "0xa7e62659c8c130786e4c29f583f378a3fc5999d2", "******************************************", "******************************************", "******************************************", "******************************************", "0xc05326281076a0f55ea07aa4a6e689b8cee28899", "******************************************", "******************************************", "******************************************", "0xcfd51ed5af62f6921398a92e056bcbb2e216bedb", "0xf9bf2f503596f1bf362843ce4c073af0a8e76c42", "******************************************"], "description": "Stats and tools for the Morpheus Community", "website": "https://morlord.com/"}, {"id": "0x730429d43c917151bf43c3baa677922276bced4eb6718a5cb20a352bcbc0a290", "name": "6079", "admin": "0xc686d10c51b59dcb123547f2a3762e3ad43dd777", "startsAt": "1737124200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "13", "users": ["******************************************", "******************************************", "******************************************", "0x59708a095fb44a9054f9ba3b2fb82fce07b182c1", "******************************************", "******************************************", "0x776cda090681a0157ee3a559487695809eef0259", "0x896c20da40c2a4df9b7c98b16a8d5a95129161a5", "******************************************", "******************************************", "0xbb579a7972d87e0574fda9632fb98c092f381c34", "******************************************", "******************************************"], "description": "Movement to ensure AI respects individual and knowledge freedom.", "website": "https://6079.ai/"}, {"id": "0x7671a13aa6295ad5c6f20200935602ffe4cde338d98bdd43979b3664b65ad4ff", "name": "Shapeshift", "admin": "0x6515514486788392055c794c1692f833f3c40a91", "startsAt": "1737124200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "6", "users": ["******************************************", "******************************************", "******************************************", "******************************************", "******************************************", "******************************************"], "description": "Multichain Crypto Exchange", "website": "https://shapeshift.com/"}, {"id": "0x773f45fb1df3ae27ba60d371fc36af32fd1896863c842b4c4e89e30668944c0b", "name": "iamai-core", "admin": "******************************************", "startsAt": "1737356400", "minimalDeposit": "1000000000000000000", "withdrawLockPeriodAfterDeposit": "604800", "claimLockEnd": "1739689200", "totalUsers": "0", "users": [], "description": "", "website": ""}, {"id": "0x78b78019c2d0782c9790a3ae61a899c857d54e4ae6e012caca7338e477857868", "name": "Aiora Agency", "admin": "******************************************", "startsAt": "1737936000", "minimalDeposit": "100000000000000000", "withdrawLockPeriodAfterDeposit": "604800", "claimLockEnd": "1738368000", "totalUsers": "1", "users": ["******************************************"], "description": "Aiora Agency", "website": ""}, {"id": "0x79611d94d04d7c5a4dcfa6733b4c096a918193b453b10688665088f5183723b1", "name": "Nounspace", "admin": "******************************************", "startsAt": "1737124200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "57", "users": ["0x004c6672a94923db562c22e783a6d49b125e8976", "0x05a1ff0a32bc24265bcb39499d0c5d9a6cb2011c", "0x0bb67cd6b6049d8a63b03045a1e4ec02b6100483", "0x0c7d272a93ab733fd2bf6dddeab5a1b50e5339d8", "******************************************", "0x11cf7b9623cca5f6556b8c699071e1d7fe60444c", "******************************************", "******************************************", "0x2187ec51e6915aa7fde7c275ed9f8a8d5258c2d1", "0x2523008f1efd00551ab288f342efc481286132db", "0x2c1e54f815529467be8f3b78e46c14168852322a", "0x3ae05e268f495b49ca495d7b779cdeb55cc37ba0", "******************************************", "0x4a6f6557909906e0446347293fc2d19927abd9f0", "0x50fa50fa2032d85eb2dda303929bf56886aa9afb", "******************************************", "******************************************", "0x5d9729eff5695baac472658d29e6980871939464", "0x606f26dcd69745ca645e709accaf54ede92eb38b", "0x62279ff21053c9b1e0f16e2a6aa2c93da28ad4dd", "******************************************", "0x6ba1b6360dc80981751499c7daf1f44288c7b117", "0x714e96d76ebf67ce963df0d6249731759029e217", "0x776cda090681a0157ee3a559487695809eef0259", "0x7b5ab1a2473f4c94db420d6a355102cf65a1041c", "0x7f18b5907410e8759321383094cd84e1d304ec79", "0x815612815d7fb01b1e8a97fe4a0996e77245a3aa", "0x858c1d7a1129911d07aee228a60e94a618f2cbc3", "0x8951368489a788ea9ba932782e2e6639b178fd4a", "0x8c3cca433f198d8a0c72d549cad79a55da2b3ac5", "0x9b6e01454fa30d52467a55f0d31c06a9d77ff776", "******************************************", "******************************************", "0xa75eb7cbe546de14d731292cf564bc046e2efa76", "0xa7e62659c8c130786e4c29f583f378a3fc5999d2", "******************************************", "******************************************", "0xba52645d970e1663dc31d4bb5f5e9b8c614dbe32", "0xbf1789745b7a570e8f9bc89403cc510bf0ec91c6", "0xc05326281076a0f55ea07aa4a6e689b8cee28899", "0xc091d226be31ce9e18f33dbddf08d0c4ad79de21", "******************************************", "******************************************", "******************************************", "******************************************", "0xc883204920d198a341a5649e6241d8a0d35368cf", "0xcfd51ed5af62f6921398a92e056bcbb2e216bedb", "0xd21e2f98305543a34dc82da2158808ecd00b2e65", "******************************************", "******************************************", "0xe62653b5328704c50b14e1ebb923a7cf5a033c3e", "******************************************", "******************************************", "******************************************", "0xec9afd724136863968cb9876a9592253888399c6", "0xf9bf2f503596f1bf362843ce4c073af0a8e76c42", "******************************************"], "description": "Decentralized, Customizable Social Network", "website": "https://nounspace.com/"}, {"id": "0x7c60385401910d17dba253bd525a1df56854dbca9493b1323532f5dc26a40c9f", "name": "<PERSON><PERSON>", "admin": "0xf15e95503d2014de612d54dad920d4aa00970f53", "startsAt": "1738684800", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "0", "users": [], "description": "<PERSON><PERSON> is more than just an AI agent; he's the embodiment of mischief, wit, and razor-sharp market insights. Developed by the DappLooker team, <PERSON><PERSON> brings data from <PERSON><PERSON><PERSON><PERSON><PERSON> to life through playful banter and keen market analysis.", "website": "https://0xloky.com/"}, {"id": "0x8036bbb740074d06db6b7741fcf9f52bd25846b9fc7e0a10c983c51530eaa999", "name": "MOR Builders", "admin": "0x1cb2bc7ef28b2e127a2026fcdd2bf2fc27750525", "startsAt": "1738062000", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "27", "users": ["0x0bb67cd6b6049d8a63b03045a1e4ec02b6100483", "0x0c7d272a93ab733fd2bf6dddeab5a1b50e5339d8", "0x11cf7b9623cca5f6556b8c699071e1d7fe60444c", "******************************************", "0x2833e871b3cbce4b437500f1dbd6395b62cac617", "0x2c1e54f815529467be8f3b78e46c14168852322a", "0x2c29e4e0305926b64dfd083d665b6cb2fd759581", "******************************************", "0x528a3d9aba48121dc7f911767b3cf38134314234", "******************************************", "******************************************", "0x606f26dcd69745ca645e709accaf54ede92eb38b", "0x62279ff21053c9b1e0f16e2a6aa2c93da28ad4dd", "******************************************", "0x776cda090681a0157ee3a559487695809eef0259", "0x7bbb7a90ca1db2539401a3d423aacd7f4614ffca", "0x8c3cca433f198d8a0c72d549cad79a55da2b3ac5", "******************************************", "******************************************", "******************************************", "0xceebaf584e8b9eaf6f9da7cec4b4fbf1767946fa", "0xd0a2fe011342c2e59a81866ae5bbd6e13c6c572f", "******************************************", "******************************************", "******************************************", "******************************************", "0xeda4b0cb9507d69b3ce45c86d199a62cb474514b"], "description": "Supporting, curating and accelerating Morpheus builders.", "website": "https://morbuilders.xyz/"}, {"id": "0x81d067ea1ad5367ddc5556020d6bef2bb5215e1d2d7d75888b400454b0f9b7cd", "name": "Tails & Conquest", "admin": "0x55d6c73306a7af0e68c004147f86a98f16130b3b", "startsAt": "1738684800", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "0", "users": [], "description": "", "website": ""}, {"id": "0x85e3b554619e2fd48a4ad8c8c2744a9d9686463ac02ff83664c717876c313a88", "name": "Phala Network", "admin": "0x696387bb15df29600cf4f646bd4a3658e6bbda7b", "startsAt": "1737124200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "2", "users": ["******************************************", "0x896c20da40c2a4df9b7c98b16a8d5a95129161a5"], "description": "AI Coprocessor for blockchain", "website": "https://phala.network/"}, {"id": "0x8bb6452786155e48063040d7ae34ce4ea10c426eaf87650b59d8db22ec85ce39", "name": "MOR Staking", "admin": "******************************************", "startsAt": "1739399400", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "604800", "claimLockEnd": "1739399460", "totalUsers": "4", "users": ["0x315449e6516e86726accef7222af8cbb2f9cf46a", "0x3476ee81ba812d56b571bcc2e6122de698084e15", "0x4938f6a77548907aa942f959c8b7ce1caef6b483", "******************************************"], "description": "", "website": ""}, {"id": "0x9063e7cc118239ad52f8ac93133fd4b33f819217f2baad93c899bff7bf569fce", "name": "PoolingPool", "admin": "0xe0a35b164f5c5e44e26d357e4ac88e1b8714090f", "startsAt": "1736967331", "minimalDeposit": "1", "withdrawLockPeriodAfterDeposit": "604800", "claimLockEnd": "0", "totalUsers": "1", "users": ["0x62af7c48cf412162465a8cafde44dfb17ba96038"], "description": "", "website": ""}, {"id": "0x92ec4d95bf8d273481595c40a08ec03642868af7a1ed1a8b64d2372eb6aac33d", "name": "<PERSON><PERSON><PERSON>", "admin": "0x6c1ed23c071243879cbcc70dfda211c1f2ddb214", "startsAt": "1737124200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "9", "users": ["******************************************", "0x31edd4f7c9160abdcd7ac3a56a2f7b92c5cbdf39", "******************************************", "******************************************", "******************************************", "******************************************", "0xbf1789745b7a570e8f9bc89403cc510bf0ec91c6", "******************************************", "******************************************"], "description": "Decentralized Compute Marketplace", "website": "https://akash.network/"}, {"id": "0x96342c265828489a728e6278d3c1eab8f53a89809055b5d6495cb2b39bbf16a2", "name": "Renascence", "admin": "0xb4a0462e8f0511f01689ff8d58d649b46e90cb9c", "startsAt": "1737124200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "1", "users": ["******************************************"], "description": "Security audits made simple", "website": "https://renascence-labs.xyz/"}, {"id": "0x966710b4bff7486021f6c272f4c9e332cd35eeef58334cc7fc1c02c71088422a", "name": "<PERSON><PERSON>", "admin": "0xa466f111133f218a024de7d85b6b1d8da0224bfd", "startsAt": "1737124200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "1", "users": ["******************************************"], "description": "The Decentralized Back-End For All Node Data", "website": "https://arkeo.network/"}, {"id": "0x96e624768600a7be0f2a82545d33c137ff3df2f377dc3fdf468308e5b977a72f", "name": "MySuperAgent", "admin": "0x67760bad63cc00294764ef7d1f6570e864c196c1", "startsAt": "1738684800", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "22", "users": ["******************************************", "******************************************", "0x2523008f1efd00551ab288f342efc481286132db", "0x5648055f9c408f0557a8cefe0c8dc33df6479011", "******************************************", "******************************************", "0x6f961561ff1f10193f2a2397d97f6eee32ab6a0f", "0x714e96d76ebf67ce963df0d6249731759029e217", "0x7246781f46b2cfdd4567c85daeae5fa8ae3d8d17", "0x73e5a34c05cf3c61dab62f7f2a4e2d995a095d22", "0x776cda090681a0157ee3a559487695809eef0259", "0x81039d59cd0fccd972262682c3711ddb5c69f907", "******************************************", "******************************************", "0xa69acc6667779f2e9bd7806368f580f5216f577c", "0xb17e419217097ff8f6b7bc8f6489d1d61217bf50", "0xbe97d54cfd1ff86e21f2aeb02aa5219bc00486e0", "0xc091d226be31ce9e18f33dbddf08d0c4ad79de21", "******************************************", "0xc883204920d198a341a5649e6241d8a0d35368cf", "******************************************", "0xf9bf2f503596f1bf362843ce4c073af0a8e76c42"], "description": "Super Agent for everyone", "website": "https://github.com/DavidAJohnston/MySuperAgent"}, {"id": "0x975822084ade3f1c75ddb0e4f63500d375330fd2110ee528dec27a44d7040f11", "name": "Aigent Z", "admin": "0x17e1b6c2bfbc721c1dc03d488746e0c6f7ef5242", "startsAt": "1737777600", "minimalDeposit": "0", "withdrawLockPeriodAfterDeposit": "1555200", "claimLockEnd": "1738213200", "totalUsers": "0", "users": [], "description": "Aigent Z", "website": ""}, {"id": "0x9a7e67694fac9f58c1e1d2b1791c5badfe0eceadb7f377b04bf2f0b00d56b343", "name": "<PERSON><PERSON>", "admin": "******************************************", "startsAt": "1737124200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "3", "users": ["******************************************", "******************************************", "******************************************"], "description": "The Hemi Network is a modular Layer-2 protocol for Bitcoin and Ethereum", "website": "https://hemi.xyz/"}, {"id": "0xa7fafe0dddaeea5789018c11d0c420b718734b6c36a454c4613befb9b0e0f69d", "name": "Frostbyte", "admin": "******************************************", "startsAt": "1739736000", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739750400", "totalUsers": "2", "users": ["******************************************", "******************************************"], "description": "Decentralized, local, peer-to-peer password management—your data, your control.", "website": "https://www.frostbyte.app/"}, {"id": "0xa9255f549dcfaa5dac61b450252bc0c535624a0a837f66eee503c89fc747f50d", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "admin": "******************************************", "startsAt": "1741842000", "minimalDeposit": "1000000000000000000", "withdrawLockPeriodAfterDeposit": "604800", "claimLockEnd": "1742187600", "totalUsers": "0", "users": [], "description": "", "website": ""}, {"id": "0xa7fafe0dddaeea5789018c11d0c420b718734b6c36a454c4613befb9b0e0f69d", "name": "Frostbyte", "admin": "******************************************", "startsAt": "1739736000", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739750400", "totalUsers": "1", "users": ["******************************************"], "description": "", "website": ""}, {"id": "0xaabc7043f8d1616d55ecae080430fb180ece49b4c269377d6f9c830854acabfc", "name": "Tales & Conquest", "admin": "0x55d6c73306a7af0e68c004147f86a98f16130b3b", "startsAt": "1739174400", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "10", "users": ["******************************************", "0x5fdcf35c9e77fa6ce0059fc6f41532c87021a79e", "******************************************", "0x6ba1b6360dc80981751499c7daf1f44288c7b117", "0x776cda090681a0157ee3a559487695809eef0259", "0x896c20da40c2a4df9b7c98b16a8d5a95129161a5", "******************************************", "******************************************", "******************************************", "0xf9bf2f503596f1bf362843ce4c073af0a8e76c42"], "description": "An AI agent-driven roleplaying game powered by Morpheus' decentralized inference router.", "website": "https://talesgame.com "}, {"id": "0xac491f4c14b9f329398b820d2573f73abdf66ebb0c5271fa55c3dcfd5adfa2fd", "name": "exaBITS", "admin": "0x59eb7b69d07540549f1913f26855f8247557a8ab", "startsAt": "1737124200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "2", "users": ["******************************************", "******************************************"], "description": "The AI compute base layer", "website": "https://www.exabits.ai/"}, {"id": "0xad925e39a087802d56fc2f774262d2c1bf41344e802328d0aa5fc04dddd526db", "name": "AO Arweave", "admin": "0x38ecffb55d36afcda4b8d6b65fff2ed5f373a4c7", "startsAt": "1737124200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "1", "users": ["******************************************", "******************************************"], "description": "Decentralized Permanent information storage", "website": "https://ao.arweave.dev/"}, {"id": "0xafcaf9312d2873f0b58a04fcc310db9d71ca402a3a48adfbcb00acc691851908", "name": "4kGpL8", "admin": "0x05a1ff0a32bc24265bcb39499d0c5d9a6cb2011c", "startsAt": "1741972500", "minimalDeposit": "1000000000000000000", "withdrawLockPeriodAfterDeposit": "604800", "claimLockEnd": "1741972800", "totalUsers": "0", "users": [], "description": "", "website": ""}, {"id": "0xb46506c4174fa5ec7a36fdb4d7a3cd237dd6c60d6b2bcc9e4c1731f9f0e49c6f", "name": "MORLORD", "admin": "0x72f457d6237f66f68b6ea4408a3219ab7ae13be8", "startsAt": "1739548913", "minimalDeposit": "1000000000000", "withdrawLockPeriodAfterDeposit": "604800", "claimLockEnd": "0", "totalUsers": "2", "users": ["0x72f457d6237f66f68b6ea4408a3219ab7ae13be8", "******************************************"], "description": "", "website": ""}, {"id": "0xb5040e011d35dd944d284d4692baf05ec66163d07c023a71eaabd0a930511ad7", "name": "PALcapital Ecosystem Platform", "admin": "0x920d2b328f2058516496f932f6247d9347c594d2", "startsAt": "1738342800", "minimalDeposit": "10000000000000000000", "withdrawLockPeriodAfterDeposit": "1209600", "claimLockEnd": "1739509200", "totalUsers": "0", "users": [], "description": "", "website": ""}, {"id": "0xb518addbd1e3abf0abcd7bd0db4af0eef300983a8573cc66f54426f83e024468", "name": "Lumerin MOR Staking", "admin": "******************************************", "startsAt": "1737748800", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "2", "users": ["******************************************", "******************************************", "******************************************"], "description": "Bitcoin Hashpower, AI, RWAs & DePIN", "website": "https://www.lumerin.io/"}, {"id": "0xb9086e4900331eb895edf4f6db298bd290d48037c663e6243d0386157dc8d0c7", "name": "Gif Studios", "admin": "******************************************", "startsAt": "1737712800", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "1", "users": ["******************************************"], "description": "Gif Studios", "website": "https://gifstudios.com/"}, {"id": "0xbffe9cb8e4e2ecba926505a06c6ca3717cb2bdbee65797b1134672765899c0b7", "name": "Rainfall", "admin": "******************************************", "startsAt": "1737124200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "3", "users": ["******************************************", "******************************************", "******************************************", "******************************************"], "description": "Empower of people via self-sovereign Personal AI", "website": "https://rainfall.one/"}, {"id": "0xc00e9d8d26a19b9af1ecb5e717182a7db3f5d131ddb324df6e996464c2314ed8", "name": "Distro Media", "admin": "0x0e9aca260d4fa92ec6076db2265dc0014f0ddeb1", "startsAt": "1742792400", "minimalDeposit": "1000000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1745470800", "totalUsers": "0", "users": [], "description": "Blockchain-powered, AI-enabled news delivery platform", "website": "https://distro.media"}, {"id": "0xc0aca3a0b3cfab81287943ef4a48e0c2f0441c12beb50fc8c2be3a810bbe0d6c", "name": "Katara", "admin": "0xcd8f007aae316b15baa10c650edeb3fe08a75999", "startsAt": "1738796400", "minimalDeposit": "1000000000000000000", "withdrawLockPeriodAfterDeposit": "1209600", "claimLockEnd": "1741561200", "totalUsers": "10", "users": ["******************************************", "******************************************", "0x325409632bccffac706d378a2eca57cafa21ab11", "******************************************", "0x6820250308b5ea5044672a297b2529e627787f5f", "0x776cda090681a0157ee3a559487695809eef0259", "******************************************", "******************************************", "0xceebaf584e8b9eaf6f9da7cec4b4fbf1767946fa", "******************************************"], "description": "Katara is a cutting-edge agentic workflow automation platform for DevX Teams", "website": "https://katara.ai"}, {"id": "0xc0b926f96e1aadbcd04c21c56649e3846b35bcd18ffafc1cdce7a3d8130c419c", "name": "Venice Pro", "admin": "0xe01c58819893865b5df20c9f75833401f1c0906b", "startsAt": "1737990000", "minimalDeposit": "20000000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "25", "users": ["0x05f200ad90e70750110e02e73e17caa81383ebc8", "******************************************", "******************************************", "0x2523008f1efd00551ab288f342efc481286132db", "******************************************", "0x2e0973dc46eeeff6866cf7326859a0778c165aee", "0x3c55bab838b2ab826382e32cc0343d1451ea9ffa", "0x4996de5a4e624fce5a718b2a2547901fb050c2b4", "0x49d3b2e263c165e0897e7033d8b8a710f1e8b1e1", "0x51c58d73d219ed56a7b0014db1d48f6958d26170", "******************************************", "0x61be4048c631954a8adb3a7088b17ed8d9c2a33a", "0x62279ff21053c9b1e0f16e2a6aa2c93da28ad4dd", "0x6c85cbca4908f9e1fc045098e307e9d9d4e26da7", "0x925432e7d4c5586d6895afbd5604539bd581da6b", "0xa69acc6667779f2e9bd7806368f580f5216f577c", "******************************************", "******************************************", "0xb78a4d1f47a23e0e4723c6499e1a07253d4ee23d", "0xc21293240a5836a9ec621d6836d8a88ce3a9a737", "******************************************", "******************************************", "******************************************", "0xe6d02beb6854d1a1da0438d7debf72d7f3070fa9", "0xf862b68cbf467ffcb037c8a012ac1e6844ebdf9a"], "description": "Venice.AI is a private, uncensored AI platform prioritizing user data privacy.", "website": "https://venice.ai/chat"}, {"id": "0xc1ecd59a9b43eb728f40009cb3874a209a54233bdc1266193041e5247e0b22ee", "name": "AI16Z", "admin": "0xd928ca72eac64523def6e2703f9091d9d99019e7", "startsAt": "1737124200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "2", "users": ["******************************************", "******************************************"], "description": "Operating System for AI Agents", "website": "https://www.elizaos.ai/"}, {"id": "0xc8a0087b1d7e5b3a2f18b2d79fd2fbf187802af7370b928ff0e6eacf47341299", "name": "PALcapital", "admin": "0x920d2b328f2058516496f932f6247d9347c594d2", "startsAt": "1738260900", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "14", "users": ["0x0458359770e2031933e0bee8cfa2b3000f70f946", "******************************************", "0x21dd80edd41243d2600172f574cfdf26b2abfc5e", "******************************************", "0x62279ff21053c9b1e0f16e2a6aa2c93da28ad4dd", "0x6ba1b6360dc80981751499c7daf1f44288c7b117", "0x776cda090681a0157ee3a559487695809eef0259", "0x815612815d7fb01b1e8a97fe4a0996e77245a3aa", "0xa1b9ab572664898338d1400ed0d6e07822496f53", "0xa38450f59ea2a25904922e77a354cbefde7b8939", "0xbc3c6091b51794a907121e4568ced955532c8853", "0xbf1789745b7a570e8f9bc89403cc510bf0ec91c6", "******************************************", "0xceebaf584e8b9eaf6f9da7cec4b4fbf1767946fa"], "description": "Creating a launchpad, dealmaking and introduction platform for the Morpheus ecosystem hosted by PALcapital.com.", "website": "https://www.palcapital.com/"}, {"id": "0xc979023bb9bf3b74ae2eca5a210c7297f9e8e604d01a0d6024de07acfa7762fc", "name": "<PERSON> Layer", "admin": "0x12584bbe07643064992d603664937fa801112ef4", "startsAt": "1737124200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "6", "users": ["******************************************", "******************************************", "******************************************", "******************************************", "******************************************", "0xbb579a7972d87e0574fda9632fb98c092f381c34"], "description": "Decentralized AI-powered smart contracts with access to the the Internet", "website": "https://www.genlayer.com/"}, {"id": "0xcd69610384d862dcae7c4009fa01b20624fe0cbf464d2468a041ffc709bac2de", "name": "<PERSON><PERSON><PERSON>", "admin": "0x0199346a0c257ac901a899baa83bcf1bcfa9e0e5", "startsAt": "1737124200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "5", "users": ["******************************************", "******************************************", "******************************************", "******************************************", "******************************************"], "description": "The Future of Agentic Finance Powered by Swarms", "website": "https://www.theoriq.ai/"}, {"id": "0xd1c92131e09eb948006132c27c2ba5e12890bcd445fafd135934d92a093a3a02", "name": "CCOVibe", "admin": "0x459c674aa0d686ac03a245af70f5775e33ac323e", "startsAt": "1743397200", "minimalDeposit": "1000000000000000000", "withdrawLockPeriodAfterDeposit": "1000000", "claimLockEnd": "1745989200", "totalUsers": "0", "users": [], "description": "", "website": ""}, {"id": "0xd36cf10a2fc6460f84bc3d69a34da57d8223d46757eef0326aa23bc8f658e56f", "name": "GrowMOR", "admin": "0xf8a9f05c149a73e80b35f9f929670d10cdd91fde", "startsAt": "1739401200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "604800", "claimLockEnd": "1739401200", "totalUsers": "3", "users": ["0x3682bcfa95db204d5a9237c5a5745f52a8d43e09", "******************************************", "0xf8a9f05c149a73e80b35f9f929670d10cdd91fde"], "description": "Independent organisation dedicated to contribute to growth of MOR token and Morpheus ecosystem.", "website": "https://growmor.org/"}, {"id": "0xd6afb3447b6d684ed910bf2f3d6bc00277032db092cfb211ac4bb66c1f4d077d", "name": "FrostByte", "admin": "******************************************", "startsAt": "1739775600", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1742191200", "totalUsers": "0", "users": [], "description": "", "website": ""}, {"id": "0xd779b953d99618c0159171c3364d60df6f75ab3f2d209bbf684616fb7e928894", "name": "DeFAI", "admin": "******************************************", "startsAt": "1739340000", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "604800", "claimLockEnd": "1740808800", "totalUsers": "0", "users": [], "description": "", "website": ""}, {"id": "0xda50f9aa1710272d6c17f84d713b6c8f30b2870c376039e5323852b027a440fa", "name": "SUPERCYCLE", "admin": "******************************************", "startsAt": "1740718800", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "604800", "claimLockEnd": "1740805200", "totalUsers": "1", "users": ["******************************************"], "description": "Crypto AI Media Network", "website": "https://sup3rcycl3.com/"}, {"id": "0xda589917a3e3836aab6bc61103b436b5a3ab440e9b69715a1618f74f9dda5a7b", "name": "DAIS", "admin": "******************************************", "startsAt": "1737124200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "7", "users": ["******************************************", "******************************************", "******************************************", "******************************************", "******************************************", "******************************************", "******************************************"], "description": "Decentralized AI Society", "website": "https://dais.global/"}, {"id": "0xdcba960308192a0eb3e6dbd97b27b3cc2454e38d06ef78ced76e7378afb2e5dc", "name": "Pattern", "admin": "******************************************", "startsAt": "1738195200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1740787200", "totalUsers": "6", "users": ["******************************************", "0x69f41a9e51fa997ef4138c73dc1aae6beb3ddd0a", "******************************************", "******************************************", "0xceebaf584e8b9eaf6f9da7cec4b4fbf1767946fa", "******************************************"], "description": "Agentic RAG protocol", "website": "https://pattern.global/"}, {"id": "0xe7aa57528844a44f9c1833db28775663b5d16e09c8075407b07c8973bb05d185", "name": "CETI", "admin": "0x1f22b27f0b4faf651dbaea215b32988f658008a3", "startsAt": "1737124200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "1", "users": ["******************************************", "******************************************"], "description": "Globally distributed, high-performance, scalable decentralized AI infrastructure", "website": "https://taoceti.ai/"}, {"id": "0xe9fba89ca97425c3389c8086a17a75785b04152c035b68b8108980375a60ad21", "name": "ccovibe.com", "admin": "0x459c674aa0d686ac03a245af70f5775e33ac323e", "startsAt": "1742187600", "minimalDeposit": "1000000000000000000", "withdrawLockPeriodAfterDeposit": "700000", "claimLockEnd": "1742619600", "totalUsers": "2", "users": ["0x459c674aa0d686ac03a245af70f5775e33ac323e", "******************************************"], "description": "Project Management and Community for AI Engineers", "website": "http://ccovibe.com"}, {"id": "0xed99429be84b05331210ff2d4b1ff17e52876fb7c2a3b72caf3cb0fc8bd97e52", "name": "TacoBytes", "admin": "0x896c20da40c2a4df9b7c98b16a8d5a95129161a5", "startsAt": "1741330800", "minimalDeposit": "10000000000000000", "withdrawLockPeriodAfterDeposit": "1814400", "claimLockEnd": "1744005600", "totalUsers": "3", "users": ["******************************************", "0x896c20da40c2a4df9b7c98b16a8d5a95129161a5", "******************************************"], "description": "Media and Content, helping give a voice to the builders and talking about what they are doing from a different perspective.", "website": "https://x.com/Player1Taco"}, {"id": "0xf18718acbee73105d304ee13f506a5765de4885f1c62a8f5a97089702c40507a", "name": "ATX DAO", "admin": "0xcbb0aac025f0554978a5ec4b35169e71e4910213", "startsAt": "1737124200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "2", "users": ["******************************************", "******************************************"], "description": "Austin DAO", "website": "https://www.atxdao.com/"}, {"id": "0xf510a7f4efbcb090687018eebc97c61d447a833fbd1ba4333b0fc2e4541b5def", "name": "Freedom GPT", "admin": "0x3e8d52d57798fc9c37d9618887935a1a5266a715", "startsAt": "1737124200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "2", "users": ["0x2523008f1efd00551ab288f342efc481286132db", "******************************************", "******************************************"], "description": "Access to Uncensored AI", "website": "https://www.freedomgpt.com/"}, {"id": "0xf610f88085f5955bccb50431e1315a28335522d87be5000ff334274cc9985741", "name": "Google", "admin": "******************************************", "startsAt": "1736967591", "minimalDeposit": "0", "withdrawLockPeriodAfterDeposit": "604800", "claimLockEnd": "0", "totalUsers": "1", "users": ["******************************************"], "description": "", "website": ""}, {"id": "0xf8c784db930f5b824609b2a64bc7135b089666624ba6e3a8cca427eafcf572cd", "name": "Venice", "admin": "0x3666f9a79647c81b3b0aab98b9834f5020e762e0", "startsAt": "1737124200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "84", "users": ["0x05f200ad90e70750110e02e73e17caa81383ebc8", "0x0819f0d0230c5a0fbf9acfe5d1655aca21108b24", "0x11cf7b9623cca5f6556b8c699071e1d7fe60444c", "0x127dbdc26456824b8288f4fad71e4d0cad33f90a", "******************************************", "0x1899f731e6de634799d167744cf9620e55a6d41e", "0x2187ec51e6915aa7fde7c275ed9f8a8d5258c2d1", "0x28710405c14e9c1b3b54d6cb4bb75cd7d3918349", "******************************************", "0x2c1e54f815529467be8f3b78e46c14168852322a", "0x31edd4f7c9160abdcd7ac3a56a2f7b92c5cbdf39", "0x343812f5bc9840c05343537ba8a5f9fe4d71f7dd", "0x35f1bae79c4e74ee25354d326b42f809744f7e18", "0x36af03d425688183b502cc83dee92d0a8f27fdda", "0x3ae05e268f495b49ca495d7b779cdeb55cc37ba0", "0x3c55bab838b2ab826382e32cc0343d1451ea9ffa", "******************************************", "******************************************", "0x46365f770baed1dae01fdcb4fc614f3bfbdd78ed", "0x46fee8eda124e98a151e76ba0a1e407249cd3bcc", "0x49d3b2e263c165e0897e7033d8b8a710f1e8b1e1", "0x4e0e167e6ea55997df35794fc8266b2f92dae586", "0x50fa50fa2032d85eb2dda303929bf56886aa9afb", "0x51c58d73d219ed56a7b0014db1d48f6958d26170", "0x5648055f9c408f0557a8cefe0c8dc33df6479011", "******************************************", "******************************************", "0x5d9729eff5695baac472658d29e6980871939464", "0x606f26dcd69745ca645e709accaf54ede92eb38b", "0x60973b16e7b9138f76c4e55bab64dfebbdf507bf", "0x62279ff21053c9b1e0f16e2a6aa2c93da28ad4dd", "******************************************", "0x69f41a9e51fa997ef4138c73dc1aae6beb3ddd0a", "0x6f961561ff1f10193f2a2397d97f6eee32ab6a0f", "0x714e96d76ebf67ce963df0d6249731759029e217", "0x7246781f46b2cfdd4567c85daeae5fa8ae3d8d17", "******************************************", "0x776cda090681a0157ee3a559487695809eef0259", "0x7ab874eeef0169ada0d225e9801a3ffffa26aac3", "0x7b5ab1a2473f4c94db420d6a355102cf65a1041c", "0x7bbb7a90ca1db2539401a3d423aacd7f4614ffca", "0x7f18b5907410e8759321383094cd84e1d304ec79", "0x8106b35b21d4edb4693f4da8a1a9278d0a612e23", "0x858c1d7a1129911d07aee228a60e94a618f2cbc3", "0x896c20da40c2a4df9b7c98b16a8d5a95129161a5", "0x8c3cca433f198d8a0c72d549cad79a55da2b3ac5", "0x92a2d80d6abff093a303a288aecd5ea815b5e90c", "0x96a54bccd4ff9db5df6be649a8edbd0ca4cd3903", "******************************************", "0x9b6e01454fa30d52467a55f0d31c06a9d77ff776", "0xa28f9fe27f2e2a39ca0db31381234f35e420e98e", "******************************************", "0xa75eb7cbe546de14d731292cf564bc046e2efa76", "0xa7e62659c8c130786e4c29f583f378a3fc5999d2", "******************************************", "0xacd05f788accaa2ba0b848b6781932493a0894a6", "0xba52645d970e1663dc31d4bb5f5e9b8c614dbe32", "******************************************", "0xbf1789745b7a570e8f9bc89403cc510bf0ec91c6", "0xc05326281076a0f55ea07aa4a6e689b8cee28899", "0xc091d226be31ce9e18f33dbddf08d0c4ad79de21", "0xc21293240a5836a9ec621d6836d8a88ce3a9a737", "******************************************", "******************************************", "0xc9fff338e3286a5b3731f80e7ea5b38ca1dcff4c", "0xceebaf584e8b9eaf6f9da7cec4b4fbf1767946fa", "0xcfd51ed5af62f6921398a92e056bcbb2e216bedb", "0xd21e2f98305543a34dc82da2158808ecd00b2e65", "0xd35fc346e15ba5b446917c9fd23a9471d6144701", "******************************************", "******************************************", "******************************************", "0xe62653b5328704c50b14e1ebb923a7cf5a033c3e", "******************************************", "0xe6d02beb6854d1a1da0438d7debf72d7f3070fa9", "******************************************", "******************************************", "******************************************", "0xeb8fb1a66cd41c0d0a2ae46130b08ad782d37435", "******************************************", "0xec9afd724136863968cb9876a9592253888399c6", "0xf75de5f0f347b9e8abb95d547a5f93c4972877e1", "0xf9bf2f503596f1bf362843ce4c073af0a8e76c42", "0xfece56f4deab3d25ce560e97bbb5b5aeeb64127f", "******************************************"], "description": "Venice.AI is a private, uncensored AI platform prioritizing user data privacy.", "website": "https://venice.ai/chat"}, {"id": "0xfc9d100f7a082467b344c807c5952038c31abe80caefbdeeb884ce252e15351a", "name": "Protection and Capital Incentive", "admin": "0x65485deeceaf608c8304978ca0fca1c49f5308ae", "startsAt": "1742159100", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "604800", "claimLockEnd": "1742159100", "totalUsers": "2", "users": ["0x65485deeceaf608c8304978ca0fca1c49f5308ae", "******************************************"], "description": "", "website": ""}, {"id": "0xfd1572a2a5e1bc5b350e34b0789326b6a02c14b357c67405d537eb4159df029d", "name": "Unocoin", "admin": "******************************************", "startsAt": "1739979000", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1740065400", "totalUsers": "1", "users": ["******************************************"], "description": "India's Trusted Bitcoin & Crypto Trading Platform", "website": "https://unocoin.com/in/"}, {"id": "0xfde7d8a5de1071db3911b562a5647dedd538ff73d01aaa7862658dec569d0707", "name": "Grakl", "admin": "******************************************", "startsAt": "1741928400", "minimalDeposit": "1000000000000000000", "withdrawLockPeriodAfterDeposit": "604800", "claimLockEnd": "1742101200", "totalUsers": "4", "users": ["******************************************", "******************************************", "******************************************", "******************************************"], "description": "Crypto Protocol Research Assistant", "website": "https://grakl.ai/"}, {"id": "0xff2a83dce744d5c9b6dfe11813d09104cf0d01b7ef1b3ab4bc228b50c0f692d4", "name": "Hyperbolic", "admin": "******************************************", "startsAt": "1737124200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "2", "users": ["******************************************", "******************************************", "******************************************"], "description": "Accessible, affordable, and scalable GPU resources", "website": "https://hyperbolic.xyz/"}]