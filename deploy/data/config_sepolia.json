{"chainsConfig": {"senderChainId": 10161, "receiverChainId": 10231}, "pools": [{"payoutStart": 1721401800, "decreaseInterval": 86400, "withdrawLockPeriod": 120, "claimLockPeriod": 60, "withdrawLockPeriodAfterStake": 30, "initialReward": "14400000000000000000000", "rewardDecrease": "2468994701000000000", "minimalStake": "10000000000", "isPublic": true}, {"payoutStart": 1721401800, "decreaseInterval": 60, "withdrawLockPeriod": 1, "claimLockPeriod": 1, "withdrawLockPeriodAfterStake": 30, "initialReward": "100000000000000000000", "rewardDecrease": "100000000000000000000", "minimalStake": "10000000000", "isPublic": false, "whitelistedUsers": ["******************************************", "******************************************"], "amounts": ["1", "1"]}], "L1": {"stEth": "******************************************", "wStEth": "******************************************"}, "L2": {"swapRouter": "******************************************", "nonfungiblePositionManager": "******************************************", "wStEth": "******************************************"}, "swapParams": {"fee": 10000, "sqrtPriceLimitX96": 0}, "arbitrumConfig": {"arbitrumBridgeGatewayRouter": "******************************************"}, "lzConfig": {"lzEndpointL1": "******************************************", "lzEndpointL2": "******************************************"}}