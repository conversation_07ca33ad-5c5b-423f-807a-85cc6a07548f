{"chainsConfig": {"senderChainId": 101, "receiverChainId": 110}, "pools": [{"payoutStart": 1714401160, "decreaseInterval": 120, "withdrawLockPeriod": 60, "claimLockPeriod": 60, "withdrawLockPeriodAfterStake": 60, "initialReward": "3456000000000000000000", "rewardDecrease": "592558728240000000", "minimalStake": "10000000000000000", "isPublic": true}], "L1": {"stEth": "******************************************", "wStEth": "******************************************"}, "L2": {"swapRouter": "******************************************", "nonfungiblePositionManager": "******************************************", "wStEth": "******************************************"}, "swapParams": {"fee": 100, "sqrtPriceLimitX96": 0}, "arbitrumConfig": {"arbitrumBridgeGatewayRouter": "******************************************"}, "lzConfig": {"lzEndpointL1": "******************************************", "lzEndpointL2": "******************************************"}}