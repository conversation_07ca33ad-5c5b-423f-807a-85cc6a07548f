import { Deployer, <PERSON> } from '@solarity/hardhat-migrate';

import { MOROFT__factory } from '@/generated-types/ethers';
import { wei } from '@/scripts/utils/utils';

const config = {
  lzEndpoint: '******************************************', // https://sepolia.arbiscan.io/address/******************************************
  minter: '******************************************',
};

module.exports = async function (deployer: Deployer) {
  const signer = await deployer.getSigner();
  const signerAddress = await signer.getAddress();

  const mor = await deployer.deploy(MOROFT__factory, [config.lzEndpoint, signerAddress, signerAddress]);
  await mor.mint(signer, wei(1_000_000));

  Reporter.reportContracts(['MOR', await mor.getAddress()]);
};

// npx hardhat migrate --path-to-migrations ./deploy/mor --network base_sepolia --only 1 --verify
