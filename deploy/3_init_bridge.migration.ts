import { Deployer } from '@solarity/hardhat-migrate';

import { parseConfig } from './helpers/config-parser';

import { L1Sender__factory, L2MessageReceiver__factory, MOROFT__factory } from '@/generated-types/ethers';
import { IL2MessageReceiver } from '@/generated-types/ethers/contracts/L2MessageReceiver';

module.exports = async function (deployer: Deployer) {
  const config = parseConfig();

  const l2MessageReceiver = await deployer.deployed(
    L2MessageReceiver__factory,
    '******************************************',
  );

  // const l1Sender = L1Sender__factory.connect('******************************************', await deployer.getSigner());

  // const mor = MOR__factory.connect('******************************************', await deployer.getSigner());

  const l1Sender = await deployer.deployed(L1Sender__factory, '******************************************');

  const mor = await deployer.deployed(MOROFT__factory, '******************************************');

  const l2MessageReceiverConfig: IL2MessageReceiver.ConfigStruct = {
    gateway: config.lzConfig!.lzEndpointL2,
    sender: l1Sender,
    senderChainId: config.chainsConfig.senderChainId,
  };

  await l2MessageReceiver.setParams(mor, l2MessageReceiverConfig);
};

// npx hardhat migrate --network arbitrum_sepolia --only 3 --verify
// npx hardhat migrate --network arbitrum --only 3 --verify
