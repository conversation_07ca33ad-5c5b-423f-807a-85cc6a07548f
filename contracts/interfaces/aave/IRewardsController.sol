// SPDX-License-Identifier: MIT
pragma solidity ^0.8.10;

// https://github.com/aave-dao/aave-v3-origin/blob/main/src/contracts/rewards/interfaces/IRewardsController.sol

/**
 * @title IRewardsController
 * <AUTHOR>
 * @notice Defines the basic interface for a Rewards Controller.
 */
interface IRewardsController {
    /**
     * @dev Claims reward for a user to the desired address, on all the assets of the pool, accumulating the pending rewards
     * @param assets List of assets to check eligible distributions before claiming rewards
     * @param amount The amount of rewards to claim
     * @param to The address that will be receiving the rewards
     * @param reward The address of the reward token
     * @return The amount of rewards claimed
     **/
    function claimRewards(
        address[] calldata assets,
        uint256 amount,
        address to,
        address reward
    ) external returns (uint256);
}
