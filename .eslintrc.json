{"parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 12, "sourceType": "module", "project": "./tsconfig.json"}, "plugins": ["@typescript-eslint"], "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:prettier/recommended"], "rules": {"@typescript-eslint/no-unused-vars": "error", "@typescript-eslint/no-floating-promises": "error"}, "env": {"browser": true, "es2021": true}}