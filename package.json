{"name": "morpheus-smart-contracts", "version": "1.0.0", "license": "MIT", "author": "Distributed Lab", "description": "", "scripts": {"install": "npx husky install", "compile": "npx hardhat compile", "coverage": "npx hardhat coverage --solcoverjs ./.solcover.ts", "clean": "npx hardhat clean", "test": "npx hardhat test $(find './test' -type f -iname '*test.ts' -not -path './test/fork/*')", "test-fork": "npx hardhat test $(find './test' -type f -iname '*test.ts' -path './test/fork/*')", "test-all": "npm run deploy-localhost && npm run test", "private-network": "npx hardhat node", "private-network-fork": "npx hardhat node --fork https://mainnet.infura.io/v3/$(grep INFURA_KEY .env | cut -d '\"' -f2)", "deploy-localhost": "npx hardhat migrate --network localhost", "deploy-goerli": "npx hardhat migrate --network goerli --verify", "deploy-sepolia": "npx hardhat migrate --network sepolia --verify", "deploy-chapel": "npx hardhat migrate --network chapel --verify", "deploy-mumbai": "npx hardhat migrate --network mumbai --verify", "deploy-amoy": "npx hardhat migrate --network amoy --verify", "deploy-fuji": "npx hardhat migrate --network fuji --verify", "deploy-ethereum": "npx hardhat migrate --network ethereum --verify", "deploy-bsc": "npx hardhat migrate --network bsc --verify", "deploy-polygon": "npx hardhat migrate --network polygon --verify", "deploy-avalanche": "npx hardhat migrate --network avalanche --verify", "deploy-arbitrum": "npx hardhat migrate --network arbitrum --verify", "deploy-arbitrum-goerli": "npx hardhat migrate --network arbitrum_goerli --verify", "deploy-arbitrum-sepolia": "npx hardhat migrate --network arbitrum_sepolia --verify", "deploy-base-sepolia": "npx hardhat migrate --network base_sepolia --verify", "deploy-base": "npx hardhat migrate --network base --verify", "generate-types": "TYPECHAIN_FORCE=true npx hardhat typechain", "generate-docs": "npx hardhat markup", "lint-fix": "npm run lint-sol-fix && npm run lint-ts-fix && npm run lint-json-fix", "lint-json-fix": "prettier --write \"./**/*.json\"", "lint-ts-fix": "prettier --write \"./**/*.ts\"", "lint-sol-fix": "prettier --write \"contracts/**/*.sol\"", "lint": "npm run lint-ts && npm run lint-sol", "lint-ts": "eslint \"./**/*.ts\"", "lint-sol": "solhint \"contracts/**/*.sol\""}, "dependencies": {"@aave/core-v3": "^1.19.3", "@arbitrum/token-bridge-contracts": "^1.1.2", "@chainlink/contracts": "^1.3.0", "@layerzerolabs/lz-evm-protocol-v2": "^2.1.23", "@layerzerolabs/lz-evm-sdk-v1-0.7": "^1.5.16", "@layerzerolabs/solidity-examples": "^1.0.0", "@openzeppelin/contracts": "4.9.2", "@openzeppelin/contracts-upgradeable": "4.9.2", "@solarity/solidity-lib": "2.5.9", "@uniswap/v3-core": "^1.0.1", "@uniswap/v3-periphery": "^1.4.4", "dotenv": "^10.0.0", "hardhat": "2.22.18", "typechain": "8.3.2"}, "devDependencies": {"@metamask/eth-sig-util": "^7.0.0", "@nomicfoundation/hardhat-chai-matchers": "^2.0.2", "@nomicfoundation/hardhat-ethers": "^3.0.8", "@nomicfoundation/hardhat-network-helpers": "^1.0.9", "@nomiclabs/hardhat-truffle5": "^2.0.7", "@nomiclabs/hardhat-web3": "^2.0.0", "@solarity/hardhat-markup": "^1.0.2", "@solarity/hardhat-migrate": "2.1.8", "@trivago/prettier-plugin-sort-imports": "^4.3.0", "@typechain/ethers-v6": "^0.5.1", "@typechain/hardhat": "^9.1.0", "@typechain/truffle-v5": "^8.0.6", "@typechain/web3-v1": "^6.0.6", "@types/chai": "^4.3.6", "@types/mocha": "^10.0.2", "@types/node": "^18.16.0", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "chai": "^4.3.10", "eslint": "^8.54.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "ethers": "^6.13.5", "hardhat-contract-sizer": "^2.10.0", "hardhat-gas-reporter": "^1.0.9", "husky": "^8.0.2", "mocha": "^10.2.0", "prettier": "^3.1.0", "prettier-plugin-solidity": "^1.3.1", "solhint": "^4.0.0", "solhint-plugin-prettier": "^0.1.0", "solidity-coverage": "^0.8.14", "solidity-docgen": "^0.6.0-beta.36", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.2.2", "web3": "^1.7.5"}}