import { formatUnits } from 'ethers';
import { ethers } from 'hardhat';

import { Reverter } from '../../helpers/reverter';

import { ChainLinkDataConsumer } from '@/generated-types/ethers';
import { deployChainLinkDataConsumer } from '@/test/helpers/deployers';

describe('ChainLinkDataConsumer Fork', () => {
  const reverter = new Reverter();

  let consumer: ChainLinkDataConsumer;

  //
  const data = [
    {
      path: 'USDC/USD',
      addresses: ['******************************************'],
    },
    {
      path: 'USDT/USD',
      addresses: ['******************************************'],
    },
    {
      path: 'cbBTC/USD',
      addresses: ['******************************************'],
    },
    {
      path: 'wBTC/BTC,BTC/USD',
      addresses: ['******************************************', '******************************************'],
    },
    {
      path: 'stETH/USD',
      addresses: ['******************************************'],
    },
  ];
  const paths = data.map((e) => e.path);
  const feeds = data.map((e) => e.addresses);

  before(async () => {
    await ethers.provider.send('hardhat_reset', [
      {
        forking: {
          jsonRpcUrl: `https://eth-mainnet.g.alchemy.com/v2/${process.env.ALCHEMY_KEY}`,
          blockNumber: 22093000,
        },
      },
    ]);

    consumer = await deployChainLinkDataConsumer();

    await consumer.updateDataFeeds(paths, feeds);

    await reverter.snapshot();
  });

  beforeEach(async () => {
    await reverter.revert();
  });

  after(async () => {
    await ethers.provider.send('hardhat_reset', []);
  });

  describe('#getChainLinkDataFeedLatestAnswer', () => {
    it('should return correct prices', async () => {
      const decimals = 18;

      for (let i = 0; i < paths.length; i++) {
        const res = await consumer.getChainLinkDataFeedLatestAnswer(await consumer.getPathId(paths[i]));

        const from = paths[i].split(',')[0].split('/')[0];
        const to = paths[i].split(',')[paths[i].split(',').length - 1].split('/')[1];
        console.log(`       ${from}: ${formatUnits(res, decimals)} ${to}`);
      }
    });
  });
});

// npm run generate-types && npx hardhat test "test/fork/capital-protocol/ChainLinkDataConsumer.fork.test.ts"
