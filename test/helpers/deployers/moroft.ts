import { Signer<PERSON><PERSON>Address } from '@nomicfoundation/hardhat-ethers/signers';
import { ethers } from 'hardhat';

import { MOROFT } from '@/generated-types/ethers';

export const deployMOROFT = async (
  chainId: number,
  lzEndpointOwner: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  delegate: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  minter: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
): Promise<MOROFT> => {
  const [moroftFactory, LayerZeroEndpointV2MockFactory] = await Promise.all([
    ethers.getContractFactory('MOROFT'),
    ethers.getContractFactory('LayerZeroEndpointV2Mock'),
  ]);

  const LayerZeroEndpointV2Mock = await LayerZeroEndpointV2MockFactory.deploy(chainId, lzEndpointOwner);

  return await moroftFactory.deploy(LayerZeroEndpointV2Mock, delegate, minter);
};
