INFO:Detectors:
BuilderSubnets.claim(bytes32,address) (contracts/builder-protocol/BuilderSubnets.sol#308-339) uses arbitrary from in transferFrom: IERC20(token).safeTransferFrom(treasury,subnetTreasury_,subnetFee_) (contracts/builder-protocol/BuilderSubnets.sol#329)
BuilderSubnets.claim(bytes32,address) (contracts/builder-protocol/BuilderSubnets.sol#308-339) uses arbitrary from in transferFrom: IERC20(token).safeTransferFrom(treasury,stakerAddress_,toClaim_) (contracts/builder-protocol/BuilderSubnets.sol#335)
BuilderSubnets.claim(bytes32,address) (contracts/builder-protocol/BuilderSubnets.sol#308-339) uses arbitrary from in transferFrom: IERC20(token).safeTransferFrom(treasury,protocolTreasury_,protocolFee_) (contracts/builder-protocol/BuilderSubnets.sol#320)
Reference: https://github.com/crytic/slither/wiki/Detector-Documentation#arbitrary-from-in-transferfrom
INFO:Detectors:
Reentrancy in Distributor.withdrawUndistributedRewards(address,address) (contracts/capital-protocol/Distributor.sol#426-432):
	External calls:
	- IL1SenderV2(l1Sender).sendMintMessage{value: msg.value}(user_,undistributedRewards,refundTo_) (contracts/capital-protocol/Distributor.sol#429)
	State variables written after the call(s):
	- undistributedRewards = 0 (contracts/capital-protocol/Distributor.sol#431)
	Distributor.undistributedRewards (contracts/capital-protocol/Distributor.sol#76) can be used in cross function reentrancies:
	- Distributor.distributeRewards(uint256) (contracts/capital-protocol/Distributor.sol#330-410)
	- Distributor.undistributedRewards (contracts/capital-protocol/Distributor.sol#76)
	- Distributor.withdrawUndistributedRewards(address,address) (contracts/capital-protocol/Distributor.sol#426-432)
Reference: https://github.com/crytic/slither/wiki/Detector-Documentation#reentrancy-vulnerabilities
INFO:Detectors:
BuildersTreasury.sendRewards(address,uint256) (contracts/builder-protocol/BuildersTreasury.sol#48-54) ignores return value by IERC20(rewardToken).transfer(receiver_,amount_) (contracts/builder-protocol/BuildersTreasury.sol#51)
DepositPool.migrate(uint256) (contracts/capital-protocol/DepositPool.sol#137-160) ignores return value by IERC20(depositToken).transfer(distributor,remainder_) (contracts/capital-protocol/DepositPool.sol#153)
Reference: https://github.com/crytic/slither/wiki/Detector-Documentation#unchecked-transfer
INFO:Detectors:
OFTCore._removeDust(uint256) (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oft/OFTCore.sol#302-304) performs a multiplication on the result of a division:
	- (_amountLD / decimalConversionRate) * decimalConversionRate (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oft/OFTCore.sol#303)
BuilderSubnets.collectRewardRate(uint256,uint128,uint128) (contracts/builder-protocol/BuilderSubnets.sol#398-433) performs a multiplication on the result of a division:
	- periodRate_ = (rewardsToPeriodEnd_ * PRECISION) / emissionToPeriodEnd_ (contracts/builder-protocol/BuilderSubnets.sol#424)
	- distributedRewards_ += (periodRate_ * staked_) / PRECISION (contracts/builder-protocol/BuilderSubnets.sol#426)
DistributionV6._getClaimLockPeriodMultiplier(uint128,uint128) (contracts/capital-protocol/DistributionV6.sol#692-717) performs a multiplication on the result of a division:
	- endPower_ = _tanh(2 * (((end_ - periodStart_) * DECIMAL) / distributionPeriod)) (contracts/capital-protocol/DistributionV6.sol#709)
DistributionV6._getClaimLockPeriodMultiplier(uint128,uint128) (contracts/capital-protocol/DistributionV6.sol#692-717) performs a multiplication on the result of a division:
	- startPower_ = _tanh(2 * (((start_ - periodStart_) * DECIMAL) / distributionPeriod)) (contracts/capital-protocol/DistributionV6.sol#710)
DistributionV6._getClaimLockPeriodMultiplier(uint128,uint128) (contracts/capital-protocol/DistributionV6.sol#692-717) performs a multiplication on the result of a division:
	- multiplier_ = (powerMax * (endPower_ - startPower_)) / DECIMAL (contracts/capital-protocol/DistributionV6.sol#711)
	- (multiplier_ * PRECISION) / DECIMAL (contracts/capital-protocol/DistributionV6.sol#716)
	- multiplier_ = multiplier_ (contracts/capital-protocol/DistributionV6.sol#713)
	- multiplier_ = multiplier_ (contracts/capital-protocol/DistributionV6.sol#714)
DistributionV2._getClaimLockPeriodMultiplier(uint128,uint128) (contracts/capital-protocol/old/DistributionV2.sol#435-460) performs a multiplication on the result of a division:
	- endPower_ = _tanh(2 * (((end_ - periodStart_) * DECIMAL) / distributionPeriod)) (contracts/capital-protocol/old/DistributionV2.sol#452)
DistributionV2._getClaimLockPeriodMultiplier(uint128,uint128) (contracts/capital-protocol/old/DistributionV2.sol#435-460) performs a multiplication on the result of a division:
	- startPower_ = _tanh(2 * (((start_ - periodStart_) * DECIMAL) / distributionPeriod)) (contracts/capital-protocol/old/DistributionV2.sol#453)
DistributionV2._getClaimLockPeriodMultiplier(uint128,uint128) (contracts/capital-protocol/old/DistributionV2.sol#435-460) performs a multiplication on the result of a division:
	- multiplier_ = (powerMax * (endPower_ - startPower_)) / DECIMAL (contracts/capital-protocol/old/DistributionV2.sol#454)
	- (multiplier_ * PRECISION) / DECIMAL (contracts/capital-protocol/old/DistributionV2.sol#459)
	- multiplier_ = multiplier_ (contracts/capital-protocol/old/DistributionV2.sol#456)
	- multiplier_ = multiplier_ (contracts/capital-protocol/old/DistributionV2.sol#457)
DistributionV3._getClaimLockPeriodMultiplier(uint128,uint128) (contracts/capital-protocol/old/DistributionV3.sol#435-460) performs a multiplication on the result of a division:
	- endPower_ = _tanh(2 * (((end_ - periodStart_) * DECIMAL) / distributionPeriod)) (contracts/capital-protocol/old/DistributionV3.sol#452)
DistributionV3._getClaimLockPeriodMultiplier(uint128,uint128) (contracts/capital-protocol/old/DistributionV3.sol#435-460) performs a multiplication on the result of a division:
	- startPower_ = _tanh(2 * (((start_ - periodStart_) * DECIMAL) / distributionPeriod)) (contracts/capital-protocol/old/DistributionV3.sol#453)
DistributionV3._getClaimLockPeriodMultiplier(uint128,uint128) (contracts/capital-protocol/old/DistributionV3.sol#435-460) performs a multiplication on the result of a division:
	- multiplier_ = (powerMax * (endPower_ - startPower_)) / DECIMAL (contracts/capital-protocol/old/DistributionV3.sol#454)
	- (multiplier_ * PRECISION) / DECIMAL (contracts/capital-protocol/old/DistributionV3.sol#459)
	- multiplier_ = multiplier_ (contracts/capital-protocol/old/DistributionV3.sol#456)
	- multiplier_ = multiplier_ (contracts/capital-protocol/old/DistributionV3.sol#457)
DistributionV4._getClaimLockPeriodMultiplier(uint128,uint128) (contracts/capital-protocol/old/DistributionV4.sol#454-479) performs a multiplication on the result of a division:
	- endPower_ = _tanh(2 * (((end_ - periodStart_) * DECIMAL) / distributionPeriod)) (contracts/capital-protocol/old/DistributionV4.sol#471)
DistributionV4._getClaimLockPeriodMultiplier(uint128,uint128) (contracts/capital-protocol/old/DistributionV4.sol#454-479) performs a multiplication on the result of a division:
	- startPower_ = _tanh(2 * (((start_ - periodStart_) * DECIMAL) / distributionPeriod)) (contracts/capital-protocol/old/DistributionV4.sol#472)
DistributionV4._getClaimLockPeriodMultiplier(uint128,uint128) (contracts/capital-protocol/old/DistributionV4.sol#454-479) performs a multiplication on the result of a division:
	- multiplier_ = (powerMax * (endPower_ - startPower_)) / DECIMAL (contracts/capital-protocol/old/DistributionV4.sol#473)
	- (multiplier_ * PRECISION) / DECIMAL (contracts/capital-protocol/old/DistributionV4.sol#478)
	- multiplier_ = multiplier_ (contracts/capital-protocol/old/DistributionV4.sol#475)
	- multiplier_ = multiplier_ (contracts/capital-protocol/old/DistributionV4.sol#476)
DistributionV5._getClaimLockPeriodMultiplier(uint128,uint128) (contracts/capital-protocol/old/DistributionV5.sol#638-663) performs a multiplication on the result of a division:
	- endPower_ = _tanh(2 * (((end_ - periodStart_) * DECIMAL) / distributionPeriod)) (contracts/capital-protocol/old/DistributionV5.sol#655)
DistributionV5._getClaimLockPeriodMultiplier(uint128,uint128) (contracts/capital-protocol/old/DistributionV5.sol#638-663) performs a multiplication on the result of a division:
	- startPower_ = _tanh(2 * (((start_ - periodStart_) * DECIMAL) / distributionPeriod)) (contracts/capital-protocol/old/DistributionV5.sol#656)
DistributionV5._getClaimLockPeriodMultiplier(uint128,uint128) (contracts/capital-protocol/old/DistributionV5.sol#638-663) performs a multiplication on the result of a division:
	- multiplier_ = (powerMax * (endPower_ - startPower_)) / DECIMAL (contracts/capital-protocol/old/DistributionV5.sol#657)
	- (multiplier_ * PRECISION) / DECIMAL (contracts/capital-protocol/old/DistributionV5.sol#662)
	- multiplier_ = multiplier_ (contracts/capital-protocol/old/DistributionV5.sol#659)
	- multiplier_ = multiplier_ (contracts/capital-protocol/old/DistributionV5.sol#660)
LinearDistributionIntervalDecrease.getPeriodReward(uint256,uint256,uint128,uint128,uint128,uint128) (contracts/libs/LinearDistributionIntervalDecrease.sol#19-85) performs a multiplication on the result of a division:
	- intervalsPassed_ = timePassedBefore_ / interval_ (contracts/libs/LinearDistributionIntervalDecrease.sol#50)
	- intervalFullReward_ = initialAmount_ - intervalsPassed_ * decreaseAmount_ (contracts/libs/LinearDistributionIntervalDecrease.sol#51)
LinearDistributionIntervalDecrease._calculatePartPeriodReward(uint128,uint128,uint128,uint256,uint256,bool) (contracts/libs/LinearDistributionIntervalDecrease.sol#102-129) performs a multiplication on the result of a division:
	- intervalsPassed_ = (startTime_ - payoutStart_) / interval_ (contracts/libs/LinearDistributionIntervalDecrease.sol#110)
	- decreaseRewardAmount_ = intervalsPassed_ * decreaseAmount_ (contracts/libs/LinearDistributionIntervalDecrease.sol#111)
LinearDistributionIntervalDecrease._calculatePartPeriodReward(uint128,uint128,uint128,uint256,uint256,bool) (contracts/libs/LinearDistributionIntervalDecrease.sol#102-129) performs a multiplication on the result of a division:
	- intervalsPassed_ = (startTime_ - payoutStart_) / interval_ (contracts/libs/LinearDistributionIntervalDecrease.sol#110)
	- intervalPart_ = startTime_ - interval_ * intervalsPassed_ - payoutStart_ (contracts/libs/LinearDistributionIntervalDecrease.sol#121)
LinearDistributionIntervalDecrease._calculateFullPeriodReward(uint128,uint128,uint128,uint128,uint256,uint256) (contracts/libs/LinearDistributionIntervalDecrease.sol#131-159) performs a multiplication on the result of a division:
	- initialReward_ * ip_ - decreaseAmount_ * ((ip_ * (ip_ - 1)) / 2) (contracts/libs/LinearDistributionIntervalDecrease.sol#158)
LockMultiplierMath.getLockPeriodMultiplier(uint128,uint128) (contracts/libs/LockMultiplierMath.sol#14-39) performs a multiplication on the result of a division:
	- endPower_ = _tanh(2 * (((end_ - periodStart_) * DECIMAL) / distributionPeriod)) (contracts/libs/LockMultiplierMath.sol#31)
LockMultiplierMath.getLockPeriodMultiplier(uint128,uint128) (contracts/libs/LockMultiplierMath.sol#14-39) performs a multiplication on the result of a division:
	- startPower_ = _tanh(2 * (((start_ - periodStart_) * DECIMAL) / distributionPeriod)) (contracts/libs/LockMultiplierMath.sol#32)
LockMultiplierMath.getLockPeriodMultiplier(uint128,uint128) (contracts/libs/LockMultiplierMath.sol#14-39) performs a multiplication on the result of a division:
	- multiplier_ = (powerMax * (endPower_ - startPower_)) / DECIMAL (contracts/libs/LockMultiplierMath.sol#33)
	- (multiplier_ * PRECISION) / DECIMAL (contracts/libs/LockMultiplierMath.sol#38)
	- multiplier_ = multiplier_ (contracts/libs/LockMultiplierMath.sol#35)
	- multiplier_ = multiplier_ (contracts/libs/LockMultiplierMath.sol#36)
LogExpMath.pow(uint256,uint256) (contracts/libs/LogExpMath.sol#91-134) performs a multiplication on the result of a division:
	- logx_times_y = ((ln_36_x / ONE_18) * y_int256 + ((ln_36_x % ONE_18) * y_int256) / ONE_18) (contracts/libs/LogExpMath.sol#121)
LogExpMath.exp(int256) (contracts/libs/LogExpMath.sol#141-276) performs a multiplication on the result of a division:
	- term = ((term * x) / ONE_20) / 2 (contracts/libs/LogExpMath.sol#235)
	- term = ((term * x) / ONE_20) / 3 (contracts/libs/LogExpMath.sol#238)
LogExpMath.exp(int256) (contracts/libs/LogExpMath.sol#141-276) performs a multiplication on the result of a division:
	- term = ((term * x) / ONE_20) / 3 (contracts/libs/LogExpMath.sol#238)
	- term = ((term * x) / ONE_20) / 4 (contracts/libs/LogExpMath.sol#241)
LogExpMath.exp(int256) (contracts/libs/LogExpMath.sol#141-276) performs a multiplication on the result of a division:
	- term = ((term * x) / ONE_20) / 4 (contracts/libs/LogExpMath.sol#241)
	- term = ((term * x) / ONE_20) / 5 (contracts/libs/LogExpMath.sol#244)
LogExpMath.exp(int256) (contracts/libs/LogExpMath.sol#141-276) performs a multiplication on the result of a division:
	- term = ((term * x) / ONE_20) / 5 (contracts/libs/LogExpMath.sol#244)
	- term = ((term * x) / ONE_20) / 6 (contracts/libs/LogExpMath.sol#247)
LogExpMath.exp(int256) (contracts/libs/LogExpMath.sol#141-276) performs a multiplication on the result of a division:
	- term = ((term * x) / ONE_20) / 6 (contracts/libs/LogExpMath.sol#247)
	- term = ((term * x) / ONE_20) / 7 (contracts/libs/LogExpMath.sol#250)
LogExpMath.exp(int256) (contracts/libs/LogExpMath.sol#141-276) performs a multiplication on the result of a division:
	- term = ((term * x) / ONE_20) / 7 (contracts/libs/LogExpMath.sol#250)
	- term = ((term * x) / ONE_20) / 8 (contracts/libs/LogExpMath.sol#253)
LogExpMath.exp(int256) (contracts/libs/LogExpMath.sol#141-276) performs a multiplication on the result of a division:
	- term = ((term * x) / ONE_20) / 8 (contracts/libs/LogExpMath.sol#253)
	- term = ((term * x) / ONE_20) / 9 (contracts/libs/LogExpMath.sol#256)
LogExpMath.exp(int256) (contracts/libs/LogExpMath.sol#141-276) performs a multiplication on the result of a division:
	- term = ((term * x) / ONE_20) / 9 (contracts/libs/LogExpMath.sol#256)
	- term = ((term * x) / ONE_20) / 10 (contracts/libs/LogExpMath.sol#259)
LogExpMath.exp(int256) (contracts/libs/LogExpMath.sol#141-276) performs a multiplication on the result of a division:
	- term = ((term * x) / ONE_20) / 10 (contracts/libs/LogExpMath.sol#259)
	- term = ((term * x) / ONE_20) / 11 (contracts/libs/LogExpMath.sol#262)
LogExpMath.exp(int256) (contracts/libs/LogExpMath.sol#141-276) performs a multiplication on the result of a division:
	- term = ((term * x) / ONE_20) / 11 (contracts/libs/LogExpMath.sol#262)
	- term = ((term * x) / ONE_20) / 12 (contracts/libs/LogExpMath.sol#265)
LogExpMath.exp(int256) (contracts/libs/LogExpMath.sol#141-276) performs a multiplication on the result of a division:
	- (((product * seriesSum) / ONE_20) * firstAN) / 100 (contracts/libs/LogExpMath.sol#275)
LogExpMath.exp(int256) (contracts/libs/LogExpMath.sol#141-276) performs a multiplication on the result of a division:
	- product = (product * a8) / ONE_20 (contracts/libs/LogExpMath.sol#213)
	- product = (product * a9) / ONE_20 (contracts/libs/LogExpMath.sol#217)
LogExpMath.exp(int256) (contracts/libs/LogExpMath.sol#141-276) performs a multiplication on the result of a division:
	- product = (product * a7) / ONE_20 (contracts/libs/LogExpMath.sol#209)
	- product = (product * a8) / ONE_20 (contracts/libs/LogExpMath.sol#213)
LogExpMath.exp(int256) (contracts/libs/LogExpMath.sol#141-276) performs a multiplication on the result of a division:
	- product = (product * a6) / ONE_20 (contracts/libs/LogExpMath.sol#205)
	- product = (product * a7) / ONE_20 (contracts/libs/LogExpMath.sol#209)
LogExpMath.exp(int256) (contracts/libs/LogExpMath.sol#141-276) performs a multiplication on the result of a division:
	- product = (product * a5) / ONE_20 (contracts/libs/LogExpMath.sol#201)
	- product = (product * a6) / ONE_20 (contracts/libs/LogExpMath.sol#205)
LogExpMath.exp(int256) (contracts/libs/LogExpMath.sol#141-276) performs a multiplication on the result of a division:
	- product = (product * a4) / ONE_20 (contracts/libs/LogExpMath.sol#197)
	- product = (product * a5) / ONE_20 (contracts/libs/LogExpMath.sol#201)
LogExpMath.exp(int256) (contracts/libs/LogExpMath.sol#141-276) performs a multiplication on the result of a division:
	- product = (product * a3) / ONE_20 (contracts/libs/LogExpMath.sol#193)
	- product = (product * a4) / ONE_20 (contracts/libs/LogExpMath.sol#197)
LogExpMath.exp(int256) (contracts/libs/LogExpMath.sol#141-276) performs a multiplication on the result of a division:
	- product = (product * a2) / ONE_20 (contracts/libs/LogExpMath.sol#189)
	- product = (product * a3) / ONE_20 (contracts/libs/LogExpMath.sol#193)
LogExpMath._ln(int256) (contracts/libs/LogExpMath.sol#321-453) performs a multiplication on the result of a division:
	- z = ((a - ONE_20) * ONE_20) / (a + ONE_20) (contracts/libs/LogExpMath.sol#418)
	- z_squared = (z * z) / ONE_20 (contracts/libs/LogExpMath.sol#419)
LogExpMath._ln(int256) (contracts/libs/LogExpMath.sol#321-453) performs a multiplication on the result of a division:
	- z = ((a - ONE_20) * ONE_20) / (a + ONE_20) (contracts/libs/LogExpMath.sol#418)
	- z_squared = (z * z) / ONE_20 (contracts/libs/LogExpMath.sol#419)
	- num = z (contracts/libs/LogExpMath.sol#422)
	- num = (num * z_squared) / ONE_20 (contracts/libs/LogExpMath.sol#428)
LogExpMath._ln(int256) (contracts/libs/LogExpMath.sol#321-453) performs a multiplication on the result of a division:
	- z_squared = (z * z) / ONE_20 (contracts/libs/LogExpMath.sol#419)
	- num = (num * z_squared) / ONE_20 (contracts/libs/LogExpMath.sol#428)
	- num = (num * z_squared) / ONE_20 (contracts/libs/LogExpMath.sol#431)
LogExpMath._ln(int256) (contracts/libs/LogExpMath.sol#321-453) performs a multiplication on the result of a division:
	- z_squared = (z * z) / ONE_20 (contracts/libs/LogExpMath.sol#419)
	- num = (num * z_squared) / ONE_20 (contracts/libs/LogExpMath.sol#431)
	- num = (num * z_squared) / ONE_20 (contracts/libs/LogExpMath.sol#434)
LogExpMath._ln(int256) (contracts/libs/LogExpMath.sol#321-453) performs a multiplication on the result of a division:
	- z_squared = (z * z) / ONE_20 (contracts/libs/LogExpMath.sol#419)
	- num = (num * z_squared) / ONE_20 (contracts/libs/LogExpMath.sol#434)
	- num = (num * z_squared) / ONE_20 (contracts/libs/LogExpMath.sol#437)
LogExpMath._ln(int256) (contracts/libs/LogExpMath.sol#321-453) performs a multiplication on the result of a division:
	- z_squared = (z * z) / ONE_20 (contracts/libs/LogExpMath.sol#419)
	- num = (num * z_squared) / ONE_20 (contracts/libs/LogExpMath.sol#437)
	- num = (num * z_squared) / ONE_20 (contracts/libs/LogExpMath.sol#440)
LogExpMath._ln(int256) (contracts/libs/LogExpMath.sol#321-453) performs a multiplication on the result of a division:
	- z = ((a - ONE_20) * ONE_20) / (a + ONE_20) (contracts/libs/LogExpMath.sol#418)
	- num = z (contracts/libs/LogExpMath.sol#422)
	- seriesSum = num (contracts/libs/LogExpMath.sol#425)
	- seriesSum *= 2 (contracts/libs/LogExpMath.sol#446)
LogExpMath._ln(int256) (contracts/libs/LogExpMath.sol#321-453) performs a multiplication on the result of a division:
	- a = (a * ONE_20) / a10 (contracts/libs/LogExpMath.sol#402)
	- a = (a * ONE_20) / a11 (contracts/libs/LogExpMath.sol#407)
LogExpMath._ln(int256) (contracts/libs/LogExpMath.sol#321-453) performs a multiplication on the result of a division:
	- a = (a * ONE_20) / a9 (contracts/libs/LogExpMath.sol#397)
	- a = (a * ONE_20) / a10 (contracts/libs/LogExpMath.sol#402)
LogExpMath._ln(int256) (contracts/libs/LogExpMath.sol#321-453) performs a multiplication on the result of a division:
	- a = (a * ONE_20) / a8 (contracts/libs/LogExpMath.sol#392)
	- a = (a * ONE_20) / a9 (contracts/libs/LogExpMath.sol#397)
LogExpMath._ln(int256) (contracts/libs/LogExpMath.sol#321-453) performs a multiplication on the result of a division:
	- a = (a * ONE_20) / a7 (contracts/libs/LogExpMath.sol#387)
	- a = (a * ONE_20) / a8 (contracts/libs/LogExpMath.sol#392)
LogExpMath._ln(int256) (contracts/libs/LogExpMath.sol#321-453) performs a multiplication on the result of a division:
	- a = (a * ONE_20) / a6 (contracts/libs/LogExpMath.sol#382)
	- a = (a * ONE_20) / a7 (contracts/libs/LogExpMath.sol#387)
LogExpMath._ln(int256) (contracts/libs/LogExpMath.sol#321-453) performs a multiplication on the result of a division:
	- a = (a * ONE_20) / a5 (contracts/libs/LogExpMath.sol#377)
	- a = (a * ONE_20) / a6 (contracts/libs/LogExpMath.sol#382)
LogExpMath._ln(int256) (contracts/libs/LogExpMath.sol#321-453) performs a multiplication on the result of a division:
	- a = (a * ONE_20) / a4 (contracts/libs/LogExpMath.sol#372)
	- a = (a * ONE_20) / a5 (contracts/libs/LogExpMath.sol#377)
LogExpMath._ln(int256) (contracts/libs/LogExpMath.sol#321-453) performs a multiplication on the result of a division:
	- a = (a * ONE_20) / a3 (contracts/libs/LogExpMath.sol#367)
	- a = (a * ONE_20) / a4 (contracts/libs/LogExpMath.sol#372)
LogExpMath._ln(int256) (contracts/libs/LogExpMath.sol#321-453) performs a multiplication on the result of a division:
	- a = (a * ONE_20) / a2 (contracts/libs/LogExpMath.sol#362)
	- a = (a * ONE_20) / a3 (contracts/libs/LogExpMath.sol#367)
LogExpMath._ln_36(int256) (contracts/libs/LogExpMath.sol#461-508) performs a multiplication on the result of a division:
	- z = ((x - ONE_36) * ONE_36) / (x + ONE_36) (contracts/libs/LogExpMath.sol#473)
	- z_squared = (z * z) / ONE_36 (contracts/libs/LogExpMath.sol#474)
LogExpMath._ln_36(int256) (contracts/libs/LogExpMath.sol#461-508) performs a multiplication on the result of a division:
	- z = ((x - ONE_36) * ONE_36) / (x + ONE_36) (contracts/libs/LogExpMath.sol#473)
	- z_squared = (z * z) / ONE_36 (contracts/libs/LogExpMath.sol#474)
	- num = z (contracts/libs/LogExpMath.sol#477)
	- num = (num * z_squared) / ONE_36 (contracts/libs/LogExpMath.sol#483)
LogExpMath._ln_36(int256) (contracts/libs/LogExpMath.sol#461-508) performs a multiplication on the result of a division:
	- z_squared = (z * z) / ONE_36 (contracts/libs/LogExpMath.sol#474)
	- num = (num * z_squared) / ONE_36 (contracts/libs/LogExpMath.sol#483)
	- num = (num * z_squared) / ONE_36 (contracts/libs/LogExpMath.sol#486)
LogExpMath._ln_36(int256) (contracts/libs/LogExpMath.sol#461-508) performs a multiplication on the result of a division:
	- z_squared = (z * z) / ONE_36 (contracts/libs/LogExpMath.sol#474)
	- num = (num * z_squared) / ONE_36 (contracts/libs/LogExpMath.sol#486)
	- num = (num * z_squared) / ONE_36 (contracts/libs/LogExpMath.sol#489)
LogExpMath._ln_36(int256) (contracts/libs/LogExpMath.sol#461-508) performs a multiplication on the result of a division:
	- z_squared = (z * z) / ONE_36 (contracts/libs/LogExpMath.sol#474)
	- num = (num * z_squared) / ONE_36 (contracts/libs/LogExpMath.sol#489)
	- num = (num * z_squared) / ONE_36 (contracts/libs/LogExpMath.sol#492)
LogExpMath._ln_36(int256) (contracts/libs/LogExpMath.sol#461-508) performs a multiplication on the result of a division:
	- z_squared = (z * z) / ONE_36 (contracts/libs/LogExpMath.sol#474)
	- num = (num * z_squared) / ONE_36 (contracts/libs/LogExpMath.sol#492)
	- num = (num * z_squared) / ONE_36 (contracts/libs/LogExpMath.sol#495)
LogExpMath._ln_36(int256) (contracts/libs/LogExpMath.sol#461-508) performs a multiplication on the result of a division:
	- z_squared = (z * z) / ONE_36 (contracts/libs/LogExpMath.sol#474)
	- num = (num * z_squared) / ONE_36 (contracts/libs/LogExpMath.sol#495)
	- num = (num * z_squared) / ONE_36 (contracts/libs/LogExpMath.sol#498)
LogExpMath._ln_36(int256) (contracts/libs/LogExpMath.sol#461-508) performs a multiplication on the result of a division:
	- z_squared = (z * z) / ONE_36 (contracts/libs/LogExpMath.sol#474)
	- num = (num * z_squared) / ONE_36 (contracts/libs/LogExpMath.sol#498)
	- num = (num * z_squared) / ONE_36 (contracts/libs/LogExpMath.sol#501)
LogExpMath._ln_36(int256) (contracts/libs/LogExpMath.sol#461-508) performs a multiplication on the result of a division:
	- z = ((x - ONE_36) * ONE_36) / (x + ONE_36) (contracts/libs/LogExpMath.sol#473)
	- num = z (contracts/libs/LogExpMath.sol#477)
	- seriesSum = num (contracts/libs/LogExpMath.sol#480)
	- seriesSum * 2 (contracts/libs/LogExpMath.sol#507)
Reference: https://github.com/crytic/slither/wiki/Detector-Documentation#divide-before-multiply
INFO:Detectors:
BuilderSubnets.collectRewardRate(uint256,uint128,uint128) (contracts/builder-protocol/BuilderSubnets.sol#398-433) uses a dangerous strict equality:
	- emissionToPeriodEnd_ == 0 (contracts/builder-protocol/BuilderSubnets.sol#418)
BuildersV2._getCurrentRate() (contracts/builder-protocol/BuildersV2.sol#312-320) uses a dangerous strict equality:
	- totalPoolData.totalVirtualDeposited == 0 (contracts/builder-protocol/BuildersV2.sol#313)
BuildersV3._getCurrentRate() (contracts/builder-protocol/BuildersV3.sol#332-340) uses a dangerous strict equality:
	- totalPoolData.totalVirtualDeposited == 0 (contracts/builder-protocol/BuildersV3.sol#333)
Builders._getCurrentRate() (contracts/builder-protocol/old/Builders.sol#312-320) uses a dangerous strict equality:
	- totalPoolData.totalVirtualDeposited == 0 (contracts/builder-protocol/old/Builders.sol#313)
DistributionV6._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/DistributionV6.sol#405-477) uses a dangerous strict equality:
	- userData.virtualDeposited == 0 (contracts/capital-protocol/DistributionV6.sol#447)
DistributionV6._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/DistributionV6.sol#479-557) uses a dangerous strict equality:
	- require(bool,string)(newDeposited_ >= pool.minimalStake || newDeposited_ == 0 || depositTokenContractBalance_ == amount_,DS: invalid withdraw amount) (contracts/capital-protocol/DistributionV6.sol#508-511)
DistributionV6._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/DistributionV6.sol#479-557) uses a dangerous strict equality:
	- userData.virtualDeposited == 0 (contracts/capital-protocol/DistributionV6.sol#525)
DistributionV6.lockClaim(uint256,uint128) (contracts/capital-protocol/DistributionV6.sol#350-384) uses a dangerous strict equality:
	- userData.virtualDeposited == 0 (contracts/capital-protocol/DistributionV6.sol#368)
Distributor.distributeRewards(uint256) (contracts/capital-protocol/Distributor.sol#330-410) uses a dangerous strict equality:
	- rewards_ == 0 (contracts/capital-protocol/Distributor.sol#346)
Distributor.distributeRewards(uint256) (contracts/capital-protocol/Distributor.sol#330-410) uses a dangerous strict equality:
	- totalYield_ == 0 (contracts/capital-protocol/Distributor.sol#395)
Distributor.distributeRewards(uint256) (contracts/capital-protocol/Distributor.sol#330-410) uses a dangerous strict equality:
	- yields_[i_scope_0] == 0 (contracts/capital-protocol/Distributor.sol#403)
Distribution._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/Distribution.sol#231-284) uses a dangerous strict equality:
	- require(bool,string)(newDeposited_ >= pool.minimalStake || newDeposited_ == 0,DS: invalid withdraw amount) (contracts/capital-protocol/old/Distribution.sol#260)
DistributionV2._stake(address,uint256,uint256,uint256,uint128) (contracts/capital-protocol/old/DistributionV2.sol#254-310) uses a dangerous strict equality:
	- userData.virtualDeposited == 0 (contracts/capital-protocol/old/DistributionV2.sol#291)
DistributionV2._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV2.sol#312-373) uses a dangerous strict equality:
	- require(bool,string)(newDeposited_ >= pool.minimalStake || newDeposited_ == 0,DS: invalid withdraw amount) (contracts/capital-protocol/old/DistributionV2.sol#341)
DistributionV2._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV2.sol#312-373) uses a dangerous strict equality:
	- userData.virtualDeposited == 0 (contracts/capital-protocol/old/DistributionV2.sol#351)
DistributionV2.lockClaim(uint256,uint128) (contracts/capital-protocol/old/DistributionV2.sol#207-241) uses a dangerous strict equality:
	- userData.virtualDeposited == 0 (contracts/capital-protocol/old/DistributionV2.sol#225)
DistributionV3._stake(address,uint256,uint256,uint256,uint128) (contracts/capital-protocol/old/DistributionV3.sol#254-310) uses a dangerous strict equality:
	- userData.virtualDeposited == 0 (contracts/capital-protocol/old/DistributionV3.sol#291)
DistributionV3._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV3.sol#312-373) uses a dangerous strict equality:
	- require(bool,string)(newDeposited_ >= pool.minimalStake || newDeposited_ == 0,DS: invalid withdraw amount) (contracts/capital-protocol/old/DistributionV3.sol#341)
DistributionV3._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV3.sol#312-373) uses a dangerous strict equality:
	- userData.virtualDeposited == 0 (contracts/capital-protocol/old/DistributionV3.sol#351)
DistributionV3.lockClaim(uint256,uint128) (contracts/capital-protocol/old/DistributionV3.sol#207-241) uses a dangerous strict equality:
	- userData.virtualDeposited == 0 (contracts/capital-protocol/old/DistributionV3.sol#225)
DistributionV4._stake(address,uint256,uint256,uint256,uint128) (contracts/capital-protocol/old/DistributionV4.sol#273-329) uses a dangerous strict equality:
	- userData.virtualDeposited == 0 (contracts/capital-protocol/old/DistributionV4.sol#310)
DistributionV4._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV4.sol#331-392) uses a dangerous strict equality:
	- require(bool,string)(newDeposited_ >= pool.minimalStake || newDeposited_ == 0,DS: invalid withdraw amount) (contracts/capital-protocol/old/DistributionV4.sol#360)
DistributionV4._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV4.sol#331-392) uses a dangerous strict equality:
	- userData.virtualDeposited == 0 (contracts/capital-protocol/old/DistributionV4.sol#370)
DistributionV4.lockClaim(uint256,uint128) (contracts/capital-protocol/old/DistributionV4.sol#226-260) uses a dangerous strict equality:
	- userData.virtualDeposited == 0 (contracts/capital-protocol/old/DistributionV4.sol#244)
DistributionV5._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/old/DistributionV5.sol#351-423) uses a dangerous strict equality:
	- userData.virtualDeposited == 0 (contracts/capital-protocol/old/DistributionV5.sol#393)
DistributionV5._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV5.sol#425-503) uses a dangerous strict equality:
	- require(bool,string)(newDeposited_ >= pool.minimalStake || newDeposited_ == 0 || depositTokenContractBalance_ == amount_,DS: invalid withdraw amount) (contracts/capital-protocol/old/DistributionV5.sol#454-457)
DistributionV5._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV5.sol#425-503) uses a dangerous strict equality:
	- userData.virtualDeposited == 0 (contracts/capital-protocol/old/DistributionV5.sol#471)
DistributionV5.lockClaim(uint256,uint128) (contracts/capital-protocol/old/DistributionV5.sol#296-330) uses a dangerous strict equality:
	- userData.virtualDeposited == 0 (contracts/capital-protocol/old/DistributionV5.sol#314)
Reference: https://github.com/crytic/slither/wiki/Detector-Documentation#dangerous-strict-equalities
INFO:Detectors:
Reentrancy in DepositPool._claim(uint256,address,address) (contracts/capital-protocol/DepositPool.sol#513-574):
	External calls:
	- IDistributor(distributor).distributeRewards(rewardPoolIndex_) (contracts/capital-protocol/DepositPool.sol#531)
	State variables written after the call(s):
	- rewardPoolsProtocolDetails[rewardPoolIndex_].distributedRewards += rewards_ (contracts/capital-protocol/DepositPool.sol#563)
	DepositPool.rewardPoolsProtocolDetails (contracts/capital-protocol/DepositPool.sol#74) can be used in cross function reentrancies:
	- DepositPool._claim(uint256,address,address) (contracts/capital-protocol/DepositPool.sol#513-574)
	- DepositPool._claimReferrerTier(uint256,address,address) (contracts/capital-protocol/DepositPool.sol#576-611)
	- DepositPool._getCurrentPoolRate(uint256) (contracts/capital-protocol/DepositPool.sol#704-717)
	- DepositPool._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/DepositPool.sol#353-432)
	- DepositPool._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/DepositPool.sol#434-511)
	- DepositPool.lockClaim(uint256,uint128) (contracts/capital-protocol/DepositPool.sol#307-351)
	- DepositPool.manageUsersInPrivateRewardPool(uint256,address[],uint256[],uint128[],address[]) (contracts/capital-protocol/DepositPool.sol#188-225)
	- DepositPool.rewardPoolsProtocolDetails (contracts/capital-protocol/DepositPool.sol#74)
	- DepositPool.setRewardPoolProtocolDetails(uint256,uint128,uint128,uint128,uint256) (contracts/capital-protocol/DepositPool.sol#114-135)
	- DepositPool.stake(uint256,uint256,uint128,address) (contracts/capital-protocol/DepositPool.sol#254-266)
	- DepositPool.withdraw(uint256,uint256) (contracts/capital-protocol/DepositPool.sol#268-281)
	- userData.virtualDeposited = userData.deposited (contracts/capital-protocol/DepositPool.sol#543)
	DepositPool.usersData (contracts/capital-protocol/DepositPool.sol#45) can be used in cross function reentrancies:
	- DepositPool._claim(uint256,address,address) (contracts/capital-protocol/DepositPool.sol#513-574)
	- DepositPool._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/DepositPool.sol#353-432)
	- DepositPool._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/DepositPool.sol#434-511)
	- DepositPool.getCurrentUserMultiplier(uint256,address) (contracts/capital-protocol/DepositPool.sol#723-731)
	- DepositPool.getLatestUserReward(uint256,address) (contracts/capital-protocol/DepositPool.sol#675-684)
	- DepositPool.lockClaim(uint256,uint128) (contracts/capital-protocol/DepositPool.sol#307-351)
	- DepositPool.manageUsersInPrivateRewardPool(uint256,address[],uint256[],uint128[],address[]) (contracts/capital-protocol/DepositPool.sol#188-225)
	- DepositPool.usersData (contracts/capital-protocol/DepositPool.sol#45)
	- userData.rate = currentPoolRate_ (contracts/capital-protocol/DepositPool.sol#556)
	DepositPool.usersData (contracts/capital-protocol/DepositPool.sol#45) can be used in cross function reentrancies:
	- DepositPool._claim(uint256,address,address) (contracts/capital-protocol/DepositPool.sol#513-574)
	- DepositPool._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/DepositPool.sol#353-432)
	- DepositPool._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/DepositPool.sol#434-511)
	- DepositPool.getCurrentUserMultiplier(uint256,address) (contracts/capital-protocol/DepositPool.sol#723-731)
	- DepositPool.getLatestUserReward(uint256,address) (contracts/capital-protocol/DepositPool.sol#675-684)
	- DepositPool.lockClaim(uint256,uint128) (contracts/capital-protocol/DepositPool.sol#307-351)
	- DepositPool.manageUsersInPrivateRewardPool(uint256,address[],uint256[],uint128[],address[]) (contracts/capital-protocol/DepositPool.sol#188-225)
	- DepositPool.usersData (contracts/capital-protocol/DepositPool.sol#45)
	- userData.pendingRewards = 0 (contracts/capital-protocol/DepositPool.sol#557)
	DepositPool.usersData (contracts/capital-protocol/DepositPool.sol#45) can be used in cross function reentrancies:
	- DepositPool._claim(uint256,address,address) (contracts/capital-protocol/DepositPool.sol#513-574)
	- DepositPool._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/DepositPool.sol#353-432)
	- DepositPool._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/DepositPool.sol#434-511)
	- DepositPool.getCurrentUserMultiplier(uint256,address) (contracts/capital-protocol/DepositPool.sol#723-731)
	- DepositPool.getLatestUserReward(uint256,address) (contracts/capital-protocol/DepositPool.sol#675-684)
	- DepositPool.lockClaim(uint256,uint128) (contracts/capital-protocol/DepositPool.sol#307-351)
	- DepositPool.manageUsersInPrivateRewardPool(uint256,address[],uint256[],uint128[],address[]) (contracts/capital-protocol/DepositPool.sol#188-225)
	- DepositPool.usersData (contracts/capital-protocol/DepositPool.sol#45)
	- userData.virtualDeposited = virtualDeposited_ (contracts/capital-protocol/DepositPool.sol#558)
	DepositPool.usersData (contracts/capital-protocol/DepositPool.sol#45) can be used in cross function reentrancies:
	- DepositPool._claim(uint256,address,address) (contracts/capital-protocol/DepositPool.sol#513-574)
	- DepositPool._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/DepositPool.sol#353-432)
	- DepositPool._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/DepositPool.sol#434-511)
	- DepositPool.getCurrentUserMultiplier(uint256,address) (contracts/capital-protocol/DepositPool.sol#723-731)
	- DepositPool.getLatestUserReward(uint256,address) (contracts/capital-protocol/DepositPool.sol#675-684)
	- DepositPool.lockClaim(uint256,uint128) (contracts/capital-protocol/DepositPool.sol#307-351)
	- DepositPool.manageUsersInPrivateRewardPool(uint256,address[],uint256[],uint128[],address[]) (contracts/capital-protocol/DepositPool.sol#188-225)
	- DepositPool.usersData (contracts/capital-protocol/DepositPool.sol#45)
	- userData.claimLockStart = 0 (contracts/capital-protocol/DepositPool.sol#559)
	DepositPool.usersData (contracts/capital-protocol/DepositPool.sol#45) can be used in cross function reentrancies:
	- DepositPool._claim(uint256,address,address) (contracts/capital-protocol/DepositPool.sol#513-574)
	- DepositPool._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/DepositPool.sol#353-432)
	- DepositPool._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/DepositPool.sol#434-511)
	- DepositPool.getCurrentUserMultiplier(uint256,address) (contracts/capital-protocol/DepositPool.sol#723-731)
	- DepositPool.getLatestUserReward(uint256,address) (contracts/capital-protocol/DepositPool.sol#675-684)
	- DepositPool.lockClaim(uint256,uint128) (contracts/capital-protocol/DepositPool.sol#307-351)
	- DepositPool.manageUsersInPrivateRewardPool(uint256,address[],uint256[],uint128[],address[]) (contracts/capital-protocol/DepositPool.sol#188-225)
	- DepositPool.usersData (contracts/capital-protocol/DepositPool.sol#45)
	- userData.claimLockEnd = 0 (contracts/capital-protocol/DepositPool.sol#560)
	DepositPool.usersData (contracts/capital-protocol/DepositPool.sol#45) can be used in cross function reentrancies:
	- DepositPool._claim(uint256,address,address) (contracts/capital-protocol/DepositPool.sol#513-574)
	- DepositPool._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/DepositPool.sol#353-432)
	- DepositPool._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/DepositPool.sol#434-511)
	- DepositPool.getCurrentUserMultiplier(uint256,address) (contracts/capital-protocol/DepositPool.sol#723-731)
	- DepositPool.getLatestUserReward(uint256,address) (contracts/capital-protocol/DepositPool.sol#675-684)
	- DepositPool.lockClaim(uint256,uint128) (contracts/capital-protocol/DepositPool.sol#307-351)
	- DepositPool.manageUsersInPrivateRewardPool(uint256,address[],uint256[],uint128[],address[]) (contracts/capital-protocol/DepositPool.sol#188-225)
	- DepositPool.usersData (contracts/capital-protocol/DepositPool.sol#45)
	- userData.lastClaim = uint128(block.timestamp) (contracts/capital-protocol/DepositPool.sol#561)
	DepositPool.usersData (contracts/capital-protocol/DepositPool.sol#45) can be used in cross function reentrancies:
	- DepositPool._claim(uint256,address,address) (contracts/capital-protocol/DepositPool.sol#513-574)
	- DepositPool._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/DepositPool.sol#353-432)
	- DepositPool._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/DepositPool.sol#434-511)
	- DepositPool.getCurrentUserMultiplier(uint256,address) (contracts/capital-protocol/DepositPool.sol#723-731)
	- DepositPool.getLatestUserReward(uint256,address) (contracts/capital-protocol/DepositPool.sol#675-684)
	- DepositPool.lockClaim(uint256,uint128) (contracts/capital-protocol/DepositPool.sol#307-351)
	- DepositPool.manageUsersInPrivateRewardPool(uint256,address[],uint256[],uint128[],address[]) (contracts/capital-protocol/DepositPool.sol#188-225)
	- DepositPool.usersData (contracts/capital-protocol/DepositPool.sol#45)
Reentrancy in BuildersV3._migrateUserStake(bytes32,address) (contracts/builder-protocol/BuildersV3.sol#447-457):
	External calls:
	- IBuilderSubnets(builderSubnets).stake(builderPoolId_,user_,userData.deposited) (contracts/builder-protocol/BuildersV3.sol#451)
	State variables written after the call(s):
	- isBuilderPoolUserMigrate[builderPoolId_][user_] = true (contracts/builder-protocol/BuildersV3.sol#453)
	BuildersV3.isBuilderPoolUserMigrate (contracts/builder-protocol/BuildersV3.sol#48) can be used in cross function reentrancies:
	- BuildersV3._migrateUserStake(bytes32,address) (contracts/builder-protocol/BuildersV3.sol#447-457)
	- BuildersV3.isBuilderPoolUserMigrate (contracts/builder-protocol/BuildersV3.sol#48)
Reentrancy in L1Sender._replaceDepositToken(address,address) (contracts/capital-protocol/old/L1Sender.sol#67-83):
	External calls:
	- IERC20(unwrappedDepositToken).approve(oldToken_,0) (contracts/capital-protocol/old/L1Sender.sol#72)
	- unwrappedToken_ = IWStETH(newToken_).stETH() (contracts/capital-protocol/old/L1Sender.sol#77)
	- IERC20(unwrappedToken_).approve(newToken_,type()(uint256).max) (contracts/capital-protocol/old/L1Sender.sol#79)
	State variables written after the call(s):
	- unwrappedDepositToken = unwrappedToken_ (contracts/capital-protocol/old/L1Sender.sol#81)
	L1Sender.unwrappedDepositToken (contracts/capital-protocol/old/L1Sender.sol#16) can be used in cross function reentrancies:
	- L1Sender._replaceDepositToken(address,address) (contracts/capital-protocol/old/L1Sender.sol#67-83)
	- L1Sender.sendDepositToken(uint256,uint256,uint256) (contracts/capital-protocol/old/L1Sender.sol#102-125)
	- L1Sender.unwrappedDepositToken (contracts/capital-protocol/old/L1Sender.sol#16)
Reentrancy in DepositPool._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/DepositPool.sol#353-432):
	External calls:
	- IERC20(depositToken).safeTransferFrom(_msgSender(),address(this),amount_) (contracts/capital-protocol/DepositPool.sol#381)
	- IDistributor(distributor).supply(rewardPoolIndex_,amount_) (contracts/capital-protocol/DepositPool.sol#386)
	State variables written after the call(s):
	- _applyReferrerTier(user_,rewardPoolIndex_,currentPoolRate_,userData.deposited,deposited_,userData.referrer,referrer_) (contracts/capital-protocol/DepositPool.sol#403-411)
		- rewardPoolData.totalVirtualDeposited = rewardPoolData.totalVirtualDeposited + newVirtualAmountStaked - oldVirtualAmountStaked (contracts/capital-protocol/DepositPool.sol#665-668)
	DepositPool.rewardPoolsData (contracts/capital-protocol/DepositPool.sol#42) can be used in cross function reentrancies:
	- DepositPool._applyReferrerTier(address,uint256,uint256,uint256,uint256,address,address) (contracts/capital-protocol/DepositPool.sol#613-669)
	- DepositPool._claim(uint256,address,address) (contracts/capital-protocol/DepositPool.sol#513-574)
	- DepositPool._claimReferrerTier(uint256,address,address) (contracts/capital-protocol/DepositPool.sol#576-611)
	- DepositPool._getCurrentPoolRate(uint256) (contracts/capital-protocol/DepositPool.sol#704-717)
	- DepositPool._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/DepositPool.sol#353-432)
	- DepositPool._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/DepositPool.sol#434-511)
	- DepositPool.lockClaim(uint256,uint128) (contracts/capital-protocol/DepositPool.sol#307-351)
	- DepositPool.rewardPoolsData (contracts/capital-protocol/DepositPool.sol#42)
	- rewardPoolData.lastUpdate = uint128(block.timestamp) (contracts/capital-protocol/DepositPool.sol#414)
	DepositPool.rewardPoolsData (contracts/capital-protocol/DepositPool.sol#42) can be used in cross function reentrancies:
	- DepositPool._applyReferrerTier(address,uint256,uint256,uint256,uint256,address,address) (contracts/capital-protocol/DepositPool.sol#613-669)
	- DepositPool._claim(uint256,address,address) (contracts/capital-protocol/DepositPool.sol#513-574)
	- DepositPool._claimReferrerTier(uint256,address,address) (contracts/capital-protocol/DepositPool.sol#576-611)
	- DepositPool._getCurrentPoolRate(uint256) (contracts/capital-protocol/DepositPool.sol#704-717)
	- DepositPool._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/DepositPool.sol#353-432)
	- DepositPool._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/DepositPool.sol#434-511)
	- DepositPool.lockClaim(uint256,uint128) (contracts/capital-protocol/DepositPool.sol#307-351)
	- DepositPool.rewardPoolsData (contracts/capital-protocol/DepositPool.sol#42)
	- rewardPoolData.rate = currentPoolRate_ (contracts/capital-protocol/DepositPool.sol#415)
	DepositPool.rewardPoolsData (contracts/capital-protocol/DepositPool.sol#42) can be used in cross function reentrancies:
	- DepositPool._applyReferrerTier(address,uint256,uint256,uint256,uint256,address,address) (contracts/capital-protocol/DepositPool.sol#613-669)
	- DepositPool._claim(uint256,address,address) (contracts/capital-protocol/DepositPool.sol#513-574)
	- DepositPool._claimReferrerTier(uint256,address,address) (contracts/capital-protocol/DepositPool.sol#576-611)
	- DepositPool._getCurrentPoolRate(uint256) (contracts/capital-protocol/DepositPool.sol#704-717)
	- DepositPool._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/DepositPool.sol#353-432)
	- DepositPool._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/DepositPool.sol#434-511)
	- DepositPool.lockClaim(uint256,uint128) (contracts/capital-protocol/DepositPool.sol#307-351)
	- DepositPool.rewardPoolsData (contracts/capital-protocol/DepositPool.sol#42)
	- rewardPoolData.totalVirtualDeposited = rewardPoolData.totalVirtualDeposited + virtualDeposited_ - userData.virtualDeposited (contracts/capital-protocol/DepositPool.sol#416-419)
	DepositPool.rewardPoolsData (contracts/capital-protocol/DepositPool.sol#42) can be used in cross function reentrancies:
	- DepositPool._applyReferrerTier(address,uint256,uint256,uint256,uint256,address,address) (contracts/capital-protocol/DepositPool.sol#613-669)
	- DepositPool._claim(uint256,address,address) (contracts/capital-protocol/DepositPool.sol#513-574)
	- DepositPool._claimReferrerTier(uint256,address,address) (contracts/capital-protocol/DepositPool.sol#576-611)
	- DepositPool._getCurrentPoolRate(uint256) (contracts/capital-protocol/DepositPool.sol#704-717)
	- DepositPool._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/DepositPool.sol#353-432)
	- DepositPool._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/DepositPool.sol#434-511)
	- DepositPool.lockClaim(uint256,uint128) (contracts/capital-protocol/DepositPool.sol#307-351)
	- DepositPool.rewardPoolsData (contracts/capital-protocol/DepositPool.sol#42)
	- userData.pendingRewards = _getCurrentUserReward(currentPoolRate_,userData) (contracts/capital-protocol/DepositPool.sol#393)
	DepositPool.usersData (contracts/capital-protocol/DepositPool.sol#45) can be used in cross function reentrancies:
	- DepositPool._claim(uint256,address,address) (contracts/capital-protocol/DepositPool.sol#513-574)
	- DepositPool._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/DepositPool.sol#353-432)
	- DepositPool._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/DepositPool.sol#434-511)
	- DepositPool.getCurrentUserMultiplier(uint256,address) (contracts/capital-protocol/DepositPool.sol#723-731)
	- DepositPool.getLatestUserReward(uint256,address) (contracts/capital-protocol/DepositPool.sol#675-684)
	- DepositPool.lockClaim(uint256,uint128) (contracts/capital-protocol/DepositPool.sol#307-351)
	- DepositPool.manageUsersInPrivateRewardPool(uint256,address[],uint256[],uint128[],address[]) (contracts/capital-protocol/DepositPool.sol#188-225)
	- DepositPool.usersData (contracts/capital-protocol/DepositPool.sol#45)
	- userData.virtualDeposited = userData.deposited (contracts/capital-protocol/DepositPool.sol#400)
	DepositPool.usersData (contracts/capital-protocol/DepositPool.sol#45) can be used in cross function reentrancies:
	- DepositPool._claim(uint256,address,address) (contracts/capital-protocol/DepositPool.sol#513-574)
	- DepositPool._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/DepositPool.sol#353-432)
	- DepositPool._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/DepositPool.sol#434-511)
	- DepositPool.getCurrentUserMultiplier(uint256,address) (contracts/capital-protocol/DepositPool.sol#723-731)
	- DepositPool.getLatestUserReward(uint256,address) (contracts/capital-protocol/DepositPool.sol#675-684)
	- DepositPool.lockClaim(uint256,uint128) (contracts/capital-protocol/DepositPool.sol#307-351)
	- DepositPool.manageUsersInPrivateRewardPool(uint256,address[],uint256[],uint128[],address[]) (contracts/capital-protocol/DepositPool.sol#188-225)
	- DepositPool.usersData (contracts/capital-protocol/DepositPool.sol#45)
	- userData.lastStake = uint128(block.timestamp) (contracts/capital-protocol/DepositPool.sol#422)
	DepositPool.usersData (contracts/capital-protocol/DepositPool.sol#45) can be used in cross function reentrancies:
	- DepositPool._claim(uint256,address,address) (contracts/capital-protocol/DepositPool.sol#513-574)
	- DepositPool._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/DepositPool.sol#353-432)
	- DepositPool._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/DepositPool.sol#434-511)
	- DepositPool.getCurrentUserMultiplier(uint256,address) (contracts/capital-protocol/DepositPool.sol#723-731)
	- DepositPool.getLatestUserReward(uint256,address) (contracts/capital-protocol/DepositPool.sol#675-684)
	- DepositPool.lockClaim(uint256,uint128) (contracts/capital-protocol/DepositPool.sol#307-351)
	- DepositPool.manageUsersInPrivateRewardPool(uint256,address[],uint256[],uint128[],address[]) (contracts/capital-protocol/DepositPool.sol#188-225)
	- DepositPool.usersData (contracts/capital-protocol/DepositPool.sol#45)
	- userData.rate = currentPoolRate_ (contracts/capital-protocol/DepositPool.sol#423)
	DepositPool.usersData (contracts/capital-protocol/DepositPool.sol#45) can be used in cross function reentrancies:
	- DepositPool._claim(uint256,address,address) (contracts/capital-protocol/DepositPool.sol#513-574)
	- DepositPool._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/DepositPool.sol#353-432)
	- DepositPool._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/DepositPool.sol#434-511)
	- DepositPool.getCurrentUserMultiplier(uint256,address) (contracts/capital-protocol/DepositPool.sol#723-731)
	- DepositPool.getLatestUserReward(uint256,address) (contracts/capital-protocol/DepositPool.sol#675-684)
	- DepositPool.lockClaim(uint256,uint128) (contracts/capital-protocol/DepositPool.sol#307-351)
	- DepositPool.manageUsersInPrivateRewardPool(uint256,address[],uint256[],uint128[],address[]) (contracts/capital-protocol/DepositPool.sol#188-225)
	- DepositPool.usersData (contracts/capital-protocol/DepositPool.sol#45)
	- userData.deposited = deposited_ (contracts/capital-protocol/DepositPool.sol#424)
	DepositPool.usersData (contracts/capital-protocol/DepositPool.sol#45) can be used in cross function reentrancies:
	- DepositPool._claim(uint256,address,address) (contracts/capital-protocol/DepositPool.sol#513-574)
	- DepositPool._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/DepositPool.sol#353-432)
	- DepositPool._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/DepositPool.sol#434-511)
	- DepositPool.getCurrentUserMultiplier(uint256,address) (contracts/capital-protocol/DepositPool.sol#723-731)
	- DepositPool.getLatestUserReward(uint256,address) (contracts/capital-protocol/DepositPool.sol#675-684)
	- DepositPool.lockClaim(uint256,uint128) (contracts/capital-protocol/DepositPool.sol#307-351)
	- DepositPool.manageUsersInPrivateRewardPool(uint256,address[],uint256[],uint128[],address[]) (contracts/capital-protocol/DepositPool.sol#188-225)
	- DepositPool.usersData (contracts/capital-protocol/DepositPool.sol#45)
	- userData.virtualDeposited = virtualDeposited_ (contracts/capital-protocol/DepositPool.sol#425)
	DepositPool.usersData (contracts/capital-protocol/DepositPool.sol#45) can be used in cross function reentrancies:
	- DepositPool._claim(uint256,address,address) (contracts/capital-protocol/DepositPool.sol#513-574)
	- DepositPool._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/DepositPool.sol#353-432)
	- DepositPool._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/DepositPool.sol#434-511)
	- DepositPool.getCurrentUserMultiplier(uint256,address) (contracts/capital-protocol/DepositPool.sol#723-731)
	- DepositPool.getLatestUserReward(uint256,address) (contracts/capital-protocol/DepositPool.sol#675-684)
	- DepositPool.lockClaim(uint256,uint128) (contracts/capital-protocol/DepositPool.sol#307-351)
	- DepositPool.manageUsersInPrivateRewardPool(uint256,address[],uint256[],uint128[],address[]) (contracts/capital-protocol/DepositPool.sol#188-225)
	- DepositPool.usersData (contracts/capital-protocol/DepositPool.sol#45)
	- userData.claimLockStart = uint128(block.timestamp) (contracts/capital-protocol/DepositPool.sol#426)
	DepositPool.usersData (contracts/capital-protocol/DepositPool.sol#45) can be used in cross function reentrancies:
	- DepositPool._claim(uint256,address,address) (contracts/capital-protocol/DepositPool.sol#513-574)
	- DepositPool._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/DepositPool.sol#353-432)
	- DepositPool._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/DepositPool.sol#434-511)
	- DepositPool.getCurrentUserMultiplier(uint256,address) (contracts/capital-protocol/DepositPool.sol#723-731)
	- DepositPool.getLatestUserReward(uint256,address) (contracts/capital-protocol/DepositPool.sol#675-684)
	- DepositPool.lockClaim(uint256,uint128) (contracts/capital-protocol/DepositPool.sol#307-351)
	- DepositPool.manageUsersInPrivateRewardPool(uint256,address[],uint256[],uint128[],address[]) (contracts/capital-protocol/DepositPool.sol#188-225)
	- DepositPool.usersData (contracts/capital-protocol/DepositPool.sol#45)
	- userData.claimLockEnd = claimLockEnd_ (contracts/capital-protocol/DepositPool.sol#427)
	DepositPool.usersData (contracts/capital-protocol/DepositPool.sol#45) can be used in cross function reentrancies:
	- DepositPool._claim(uint256,address,address) (contracts/capital-protocol/DepositPool.sol#513-574)
	- DepositPool._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/DepositPool.sol#353-432)
	- DepositPool._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/DepositPool.sol#434-511)
	- DepositPool.getCurrentUserMultiplier(uint256,address) (contracts/capital-protocol/DepositPool.sol#723-731)
	- DepositPool.getLatestUserReward(uint256,address) (contracts/capital-protocol/DepositPool.sol#675-684)
	- DepositPool.lockClaim(uint256,uint128) (contracts/capital-protocol/DepositPool.sol#307-351)
	- DepositPool.manageUsersInPrivateRewardPool(uint256,address[],uint256[],uint128[],address[]) (contracts/capital-protocol/DepositPool.sol#188-225)
	- DepositPool.usersData (contracts/capital-protocol/DepositPool.sol#45)
	- userData.referrer = referrer_ (contracts/capital-protocol/DepositPool.sol#428)
	DepositPool.usersData (contracts/capital-protocol/DepositPool.sol#45) can be used in cross function reentrancies:
	- DepositPool._claim(uint256,address,address) (contracts/capital-protocol/DepositPool.sol#513-574)
	- DepositPool._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/DepositPool.sol#353-432)
	- DepositPool._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/DepositPool.sol#434-511)
	- DepositPool.getCurrentUserMultiplier(uint256,address) (contracts/capital-protocol/DepositPool.sol#723-731)
	- DepositPool.getLatestUserReward(uint256,address) (contracts/capital-protocol/DepositPool.sol#675-684)
	- DepositPool.lockClaim(uint256,uint128) (contracts/capital-protocol/DepositPool.sol#307-351)
	- DepositPool.manageUsersInPrivateRewardPool(uint256,address[],uint256[],uint128[],address[]) (contracts/capital-protocol/DepositPool.sol#188-225)
	- DepositPool.usersData (contracts/capital-protocol/DepositPool.sol#45)
Reentrancy in DistributionV6._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/DistributionV6.sol#405-477):
	External calls:
	- IERC20(depositToken).safeTransferFrom(_msgSender(),address(this),amount_) (contracts/capital-protocol/DistributionV6.sol#431)
	State variables written after the call(s):
	- _applyReferrerTier(user_,poolId_,currentPoolRate_,userData.deposited,deposited_,userData.referrer,referrer_) (contracts/capital-protocol/DistributionV6.sol#451-459)
		- poolData.totalVirtualDeposited = poolData.totalVirtualDeposited + newVirtualAmountStaked - oldVirtualAmountStaked (contracts/capital-protocol/DistributionV6.sol#613-616)
	DistributionV6.poolsData (contracts/capital-protocol/DistributionV6.sol#32) can be used in cross function reentrancies:
	- DistributionV6._applyReferrerTier(address,uint256,uint256,uint256,uint256,address,address) (contracts/capital-protocol/DistributionV6.sol#559-617)
	- DistributionV6._claim(uint256,address,address) (contracts/capital-protocol/DistributionV6.sol#257-304)
	- DistributionV6._claimReferrerTier(uint256,address,address) (contracts/capital-protocol/DistributionV6.sol#320-344)
	- DistributionV6._getCurrentPoolRate(uint256) (contracts/capital-protocol/DistributionV6.sol#627-637)
	- DistributionV6._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/DistributionV6.sol#405-477)
	- DistributionV6._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/DistributionV6.sol#479-557)
	- DistributionV6.editPool(uint256,IDistributionV5.Pool) (contracts/capital-protocol/DistributionV6.sol#102-116)
	- DistributionV6.lockClaim(uint256,uint128) (contracts/capital-protocol/DistributionV6.sol#350-384)
	- DistributionV6.poolsData (contracts/capital-protocol/DistributionV6.sol#32)
	- poolData.lastUpdate = uint128(block.timestamp) (contracts/capital-protocol/DistributionV6.sol#462)
	DistributionV6.poolsData (contracts/capital-protocol/DistributionV6.sol#32) can be used in cross function reentrancies:
	- DistributionV6._applyReferrerTier(address,uint256,uint256,uint256,uint256,address,address) (contracts/capital-protocol/DistributionV6.sol#559-617)
	- DistributionV6._claim(uint256,address,address) (contracts/capital-protocol/DistributionV6.sol#257-304)
	- DistributionV6._claimReferrerTier(uint256,address,address) (contracts/capital-protocol/DistributionV6.sol#320-344)
	- DistributionV6._getCurrentPoolRate(uint256) (contracts/capital-protocol/DistributionV6.sol#627-637)
	- DistributionV6._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/DistributionV6.sol#405-477)
	- DistributionV6._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/DistributionV6.sol#479-557)
	- DistributionV6.editPool(uint256,IDistributionV5.Pool) (contracts/capital-protocol/DistributionV6.sol#102-116)
	- DistributionV6.lockClaim(uint256,uint128) (contracts/capital-protocol/DistributionV6.sol#350-384)
	- DistributionV6.poolsData (contracts/capital-protocol/DistributionV6.sol#32)
	- poolData.rate = currentPoolRate_ (contracts/capital-protocol/DistributionV6.sol#463)
	DistributionV6.poolsData (contracts/capital-protocol/DistributionV6.sol#32) can be used in cross function reentrancies:
	- DistributionV6._applyReferrerTier(address,uint256,uint256,uint256,uint256,address,address) (contracts/capital-protocol/DistributionV6.sol#559-617)
	- DistributionV6._claim(uint256,address,address) (contracts/capital-protocol/DistributionV6.sol#257-304)
	- DistributionV6._claimReferrerTier(uint256,address,address) (contracts/capital-protocol/DistributionV6.sol#320-344)
	- DistributionV6._getCurrentPoolRate(uint256) (contracts/capital-protocol/DistributionV6.sol#627-637)
	- DistributionV6._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/DistributionV6.sol#405-477)
	- DistributionV6._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/DistributionV6.sol#479-557)
	- DistributionV6.editPool(uint256,IDistributionV5.Pool) (contracts/capital-protocol/DistributionV6.sol#102-116)
	- DistributionV6.lockClaim(uint256,uint128) (contracts/capital-protocol/DistributionV6.sol#350-384)
	- DistributionV6.poolsData (contracts/capital-protocol/DistributionV6.sol#32)
	- poolData.totalVirtualDeposited = poolData.totalVirtualDeposited + virtualDeposited_ - userData.virtualDeposited (contracts/capital-protocol/DistributionV6.sol#464)
	DistributionV6.poolsData (contracts/capital-protocol/DistributionV6.sol#32) can be used in cross function reentrancies:
	- DistributionV6._applyReferrerTier(address,uint256,uint256,uint256,uint256,address,address) (contracts/capital-protocol/DistributionV6.sol#559-617)
	- DistributionV6._claim(uint256,address,address) (contracts/capital-protocol/DistributionV6.sol#257-304)
	- DistributionV6._claimReferrerTier(uint256,address,address) (contracts/capital-protocol/DistributionV6.sol#320-344)
	- DistributionV6._getCurrentPoolRate(uint256) (contracts/capital-protocol/DistributionV6.sol#627-637)
	- DistributionV6._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/DistributionV6.sol#405-477)
	- DistributionV6._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/DistributionV6.sol#479-557)
	- DistributionV6.editPool(uint256,IDistributionV5.Pool) (contracts/capital-protocol/DistributionV6.sol#102-116)
	- DistributionV6.lockClaim(uint256,uint128) (contracts/capital-protocol/DistributionV6.sol#350-384)
	- DistributionV6.poolsData (contracts/capital-protocol/DistributionV6.sol#32)
	- userData.pendingRewards = _getCurrentUserReward(currentPoolRate_,userData) (contracts/capital-protocol/DistributionV6.sol#441)
	DistributionV6.usersData (contracts/capital-protocol/DistributionV6.sol#35) can be used in cross function reentrancies:
	- DistributionV6._claim(uint256,address,address) (contracts/capital-protocol/DistributionV6.sol#257-304)
	- DistributionV6._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/DistributionV6.sol#405-477)
	- DistributionV6._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/DistributionV6.sol#479-557)
	- DistributionV6.getCurrentUserMultiplier(uint256,address) (contracts/capital-protocol/DistributionV6.sol#659-667)
	- DistributionV6.getCurrentUserReward(uint256,address) (contracts/capital-protocol/DistributionV6.sol#386-395)
	- DistributionV6.lockClaim(uint256,uint128) (contracts/capital-protocol/DistributionV6.sol#350-384)
	- DistributionV6.manageUsersInPrivatePool(uint256,address[],uint256[],uint128[],address[]) (contracts/capital-protocol/DistributionV6.sol#175-205)
	- DistributionV6.usersData (contracts/capital-protocol/DistributionV6.sol#35)
	- userData.virtualDeposited = userData.deposited (contracts/capital-protocol/DistributionV6.sol#448)
	DistributionV6.usersData (contracts/capital-protocol/DistributionV6.sol#35) can be used in cross function reentrancies:
	- DistributionV6._claim(uint256,address,address) (contracts/capital-protocol/DistributionV6.sol#257-304)
	- DistributionV6._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/DistributionV6.sol#405-477)
	- DistributionV6._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/DistributionV6.sol#479-557)
	- DistributionV6.getCurrentUserMultiplier(uint256,address) (contracts/capital-protocol/DistributionV6.sol#659-667)
	- DistributionV6.getCurrentUserReward(uint256,address) (contracts/capital-protocol/DistributionV6.sol#386-395)
	- DistributionV6.lockClaim(uint256,uint128) (contracts/capital-protocol/DistributionV6.sol#350-384)
	- DistributionV6.manageUsersInPrivatePool(uint256,address[],uint256[],uint128[],address[]) (contracts/capital-protocol/DistributionV6.sol#175-205)
	- DistributionV6.usersData (contracts/capital-protocol/DistributionV6.sol#35)
	- userData.lastStake = uint128(block.timestamp) (contracts/capital-protocol/DistributionV6.sol#467)
	DistributionV6.usersData (contracts/capital-protocol/DistributionV6.sol#35) can be used in cross function reentrancies:
	- DistributionV6._claim(uint256,address,address) (contracts/capital-protocol/DistributionV6.sol#257-304)
	- DistributionV6._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/DistributionV6.sol#405-477)
	- DistributionV6._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/DistributionV6.sol#479-557)
	- DistributionV6.getCurrentUserMultiplier(uint256,address) (contracts/capital-protocol/DistributionV6.sol#659-667)
	- DistributionV6.getCurrentUserReward(uint256,address) (contracts/capital-protocol/DistributionV6.sol#386-395)
	- DistributionV6.lockClaim(uint256,uint128) (contracts/capital-protocol/DistributionV6.sol#350-384)
	- DistributionV6.manageUsersInPrivatePool(uint256,address[],uint256[],uint128[],address[]) (contracts/capital-protocol/DistributionV6.sol#175-205)
	- DistributionV6.usersData (contracts/capital-protocol/DistributionV6.sol#35)
	- userData.rate = currentPoolRate_ (contracts/capital-protocol/DistributionV6.sol#468)
	DistributionV6.usersData (contracts/capital-protocol/DistributionV6.sol#35) can be used in cross function reentrancies:
	- DistributionV6._claim(uint256,address,address) (contracts/capital-protocol/DistributionV6.sol#257-304)
	- DistributionV6._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/DistributionV6.sol#405-477)
	- DistributionV6._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/DistributionV6.sol#479-557)
	- DistributionV6.getCurrentUserMultiplier(uint256,address) (contracts/capital-protocol/DistributionV6.sol#659-667)
	- DistributionV6.getCurrentUserReward(uint256,address) (contracts/capital-protocol/DistributionV6.sol#386-395)
	- DistributionV6.lockClaim(uint256,uint128) (contracts/capital-protocol/DistributionV6.sol#350-384)
	- DistributionV6.manageUsersInPrivatePool(uint256,address[],uint256[],uint128[],address[]) (contracts/capital-protocol/DistributionV6.sol#175-205)
	- DistributionV6.usersData (contracts/capital-protocol/DistributionV6.sol#35)
	- userData.deposited = deposited_ (contracts/capital-protocol/DistributionV6.sol#469)
	DistributionV6.usersData (contracts/capital-protocol/DistributionV6.sol#35) can be used in cross function reentrancies:
	- DistributionV6._claim(uint256,address,address) (contracts/capital-protocol/DistributionV6.sol#257-304)
	- DistributionV6._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/DistributionV6.sol#405-477)
	- DistributionV6._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/DistributionV6.sol#479-557)
	- DistributionV6.getCurrentUserMultiplier(uint256,address) (contracts/capital-protocol/DistributionV6.sol#659-667)
	- DistributionV6.getCurrentUserReward(uint256,address) (contracts/capital-protocol/DistributionV6.sol#386-395)
	- DistributionV6.lockClaim(uint256,uint128) (contracts/capital-protocol/DistributionV6.sol#350-384)
	- DistributionV6.manageUsersInPrivatePool(uint256,address[],uint256[],uint128[],address[]) (contracts/capital-protocol/DistributionV6.sol#175-205)
	- DistributionV6.usersData (contracts/capital-protocol/DistributionV6.sol#35)
	- userData.virtualDeposited = virtualDeposited_ (contracts/capital-protocol/DistributionV6.sol#470)
	DistributionV6.usersData (contracts/capital-protocol/DistributionV6.sol#35) can be used in cross function reentrancies:
	- DistributionV6._claim(uint256,address,address) (contracts/capital-protocol/DistributionV6.sol#257-304)
	- DistributionV6._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/DistributionV6.sol#405-477)
	- DistributionV6._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/DistributionV6.sol#479-557)
	- DistributionV6.getCurrentUserMultiplier(uint256,address) (contracts/capital-protocol/DistributionV6.sol#659-667)
	- DistributionV6.getCurrentUserReward(uint256,address) (contracts/capital-protocol/DistributionV6.sol#386-395)
	- DistributionV6.lockClaim(uint256,uint128) (contracts/capital-protocol/DistributionV6.sol#350-384)
	- DistributionV6.manageUsersInPrivatePool(uint256,address[],uint256[],uint128[],address[]) (contracts/capital-protocol/DistributionV6.sol#175-205)
	- DistributionV6.usersData (contracts/capital-protocol/DistributionV6.sol#35)
	- userData.claimLockStart = uint128(block.timestamp) (contracts/capital-protocol/DistributionV6.sol#471)
	DistributionV6.usersData (contracts/capital-protocol/DistributionV6.sol#35) can be used in cross function reentrancies:
	- DistributionV6._claim(uint256,address,address) (contracts/capital-protocol/DistributionV6.sol#257-304)
	- DistributionV6._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/DistributionV6.sol#405-477)
	- DistributionV6._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/DistributionV6.sol#479-557)
	- DistributionV6.getCurrentUserMultiplier(uint256,address) (contracts/capital-protocol/DistributionV6.sol#659-667)
	- DistributionV6.getCurrentUserReward(uint256,address) (contracts/capital-protocol/DistributionV6.sol#386-395)
	- DistributionV6.lockClaim(uint256,uint128) (contracts/capital-protocol/DistributionV6.sol#350-384)
	- DistributionV6.manageUsersInPrivatePool(uint256,address[],uint256[],uint128[],address[]) (contracts/capital-protocol/DistributionV6.sol#175-205)
	- DistributionV6.usersData (contracts/capital-protocol/DistributionV6.sol#35)
	- userData.claimLockEnd = claimLockEnd_ (contracts/capital-protocol/DistributionV6.sol#472)
	DistributionV6.usersData (contracts/capital-protocol/DistributionV6.sol#35) can be used in cross function reentrancies:
	- DistributionV6._claim(uint256,address,address) (contracts/capital-protocol/DistributionV6.sol#257-304)
	- DistributionV6._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/DistributionV6.sol#405-477)
	- DistributionV6._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/DistributionV6.sol#479-557)
	- DistributionV6.getCurrentUserMultiplier(uint256,address) (contracts/capital-protocol/DistributionV6.sol#659-667)
	- DistributionV6.getCurrentUserReward(uint256,address) (contracts/capital-protocol/DistributionV6.sol#386-395)
	- DistributionV6.lockClaim(uint256,uint128) (contracts/capital-protocol/DistributionV6.sol#350-384)
	- DistributionV6.manageUsersInPrivatePool(uint256,address[],uint256[],uint128[],address[]) (contracts/capital-protocol/DistributionV6.sol#175-205)
	- DistributionV6.usersData (contracts/capital-protocol/DistributionV6.sol#35)
	- userData.referrer = referrer_ (contracts/capital-protocol/DistributionV6.sol#473)
	DistributionV6.usersData (contracts/capital-protocol/DistributionV6.sol#35) can be used in cross function reentrancies:
	- DistributionV6._claim(uint256,address,address) (contracts/capital-protocol/DistributionV6.sol#257-304)
	- DistributionV6._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/DistributionV6.sol#405-477)
	- DistributionV6._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/DistributionV6.sol#479-557)
	- DistributionV6.getCurrentUserMultiplier(uint256,address) (contracts/capital-protocol/DistributionV6.sol#659-667)
	- DistributionV6.getCurrentUserReward(uint256,address) (contracts/capital-protocol/DistributionV6.sol#386-395)
	- DistributionV6.lockClaim(uint256,uint128) (contracts/capital-protocol/DistributionV6.sol#350-384)
	- DistributionV6.manageUsersInPrivatePool(uint256,address[],uint256[],uint128[],address[]) (contracts/capital-protocol/DistributionV6.sol#175-205)
	- DistributionV6.usersData (contracts/capital-protocol/DistributionV6.sol#35)
Reentrancy in Distribution._stake(address,uint256,uint256,uint256) (contracts/capital-protocol/old/Distribution.sol#196-229):
	External calls:
	- IERC20(depositToken).safeTransferFrom(_msgSender(),address(this),amount_) (contracts/capital-protocol/old/Distribution.sol#206)
	State variables written after the call(s):
	- poolData.lastUpdate = uint128(block.timestamp) (contracts/capital-protocol/old/Distribution.sol#219)
	Distribution.poolsData (contracts/capital-protocol/old/Distribution.sol#25) can be used in cross function reentrancies:
	- Distribution._getCurrentPoolRate(uint256) (contracts/capital-protocol/old/Distribution.sol#292-302)
	- Distribution._stake(address,uint256,uint256,uint256) (contracts/capital-protocol/old/Distribution.sol#196-229)
	- Distribution._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/Distribution.sol#231-284)
	- Distribution.claim(uint256,address) (contracts/capital-protocol/old/Distribution.sol#154-179)
	- Distribution.editPool(uint256,IDistribution.Pool) (contracts/capital-protocol/old/Distribution.sol#82-96)
	- Distribution.poolsData (contracts/capital-protocol/old/Distribution.sol#25)
	- poolData.rate = currentPoolRate_ (contracts/capital-protocol/old/Distribution.sol#220)
	Distribution.poolsData (contracts/capital-protocol/old/Distribution.sol#25) can be used in cross function reentrancies:
	- Distribution._getCurrentPoolRate(uint256) (contracts/capital-protocol/old/Distribution.sol#292-302)
	- Distribution._stake(address,uint256,uint256,uint256) (contracts/capital-protocol/old/Distribution.sol#196-229)
	- Distribution._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/Distribution.sol#231-284)
	- Distribution.claim(uint256,address) (contracts/capital-protocol/old/Distribution.sol#154-179)
	- Distribution.editPool(uint256,IDistribution.Pool) (contracts/capital-protocol/old/Distribution.sol#82-96)
	- Distribution.poolsData (contracts/capital-protocol/old/Distribution.sol#25)
	- poolData.totalDeposited += amount_ (contracts/capital-protocol/old/Distribution.sol#221)
	Distribution.poolsData (contracts/capital-protocol/old/Distribution.sol#25) can be used in cross function reentrancies:
	- Distribution._getCurrentPoolRate(uint256) (contracts/capital-protocol/old/Distribution.sol#292-302)
	- Distribution._stake(address,uint256,uint256,uint256) (contracts/capital-protocol/old/Distribution.sol#196-229)
	- Distribution._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/Distribution.sol#231-284)
	- Distribution.claim(uint256,address) (contracts/capital-protocol/old/Distribution.sol#154-179)
	- Distribution.editPool(uint256,IDistribution.Pool) (contracts/capital-protocol/old/Distribution.sol#82-96)
	- Distribution.poolsData (contracts/capital-protocol/old/Distribution.sol#25)
	- userData.pendingRewards = _getCurrentUserReward(currentPoolRate_,userData) (contracts/capital-protocol/old/Distribution.sol#216)
	Distribution.usersData (contracts/capital-protocol/old/Distribution.sol#28) can be used in cross function reentrancies:
	- Distribution._stake(address,uint256,uint256,uint256) (contracts/capital-protocol/old/Distribution.sol#196-229)
	- Distribution._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/Distribution.sol#231-284)
	- Distribution.claim(uint256,address) (contracts/capital-protocol/old/Distribution.sol#154-179)
	- Distribution.getCurrentUserReward(uint256,address) (contracts/capital-protocol/old/Distribution.sol#185-194)
	- Distribution.manageUsersInPrivatePool(uint256,address[],uint256[]) (contracts/capital-protocol/old/Distribution.sol#123-145)
	- Distribution.usersData (contracts/capital-protocol/old/Distribution.sol#28)
	- userData.lastStake = uint128(block.timestamp) (contracts/capital-protocol/old/Distribution.sol#224)
	Distribution.usersData (contracts/capital-protocol/old/Distribution.sol#28) can be used in cross function reentrancies:
	- Distribution._stake(address,uint256,uint256,uint256) (contracts/capital-protocol/old/Distribution.sol#196-229)
	- Distribution._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/Distribution.sol#231-284)
	- Distribution.claim(uint256,address) (contracts/capital-protocol/old/Distribution.sol#154-179)
	- Distribution.getCurrentUserReward(uint256,address) (contracts/capital-protocol/old/Distribution.sol#185-194)
	- Distribution.manageUsersInPrivatePool(uint256,address[],uint256[]) (contracts/capital-protocol/old/Distribution.sol#123-145)
	- Distribution.usersData (contracts/capital-protocol/old/Distribution.sol#28)
	- userData.rate = currentPoolRate_ (contracts/capital-protocol/old/Distribution.sol#225)
	Distribution.usersData (contracts/capital-protocol/old/Distribution.sol#28) can be used in cross function reentrancies:
	- Distribution._stake(address,uint256,uint256,uint256) (contracts/capital-protocol/old/Distribution.sol#196-229)
	- Distribution._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/Distribution.sol#231-284)
	- Distribution.claim(uint256,address) (contracts/capital-protocol/old/Distribution.sol#154-179)
	- Distribution.getCurrentUserReward(uint256,address) (contracts/capital-protocol/old/Distribution.sol#185-194)
	- Distribution.manageUsersInPrivatePool(uint256,address[],uint256[]) (contracts/capital-protocol/old/Distribution.sol#123-145)
	- Distribution.usersData (contracts/capital-protocol/old/Distribution.sol#28)
	- userData.deposited += amount_ (contracts/capital-protocol/old/Distribution.sol#226)
	Distribution.usersData (contracts/capital-protocol/old/Distribution.sol#28) can be used in cross function reentrancies:
	- Distribution._stake(address,uint256,uint256,uint256) (contracts/capital-protocol/old/Distribution.sol#196-229)
	- Distribution._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/Distribution.sol#231-284)
	- Distribution.claim(uint256,address) (contracts/capital-protocol/old/Distribution.sol#154-179)
	- Distribution.getCurrentUserReward(uint256,address) (contracts/capital-protocol/old/Distribution.sol#185-194)
	- Distribution.manageUsersInPrivatePool(uint256,address[],uint256[]) (contracts/capital-protocol/old/Distribution.sol#123-145)
	- Distribution.usersData (contracts/capital-protocol/old/Distribution.sol#28)
Reentrancy in DistributionV2._stake(address,uint256,uint256,uint256,uint128) (contracts/capital-protocol/old/DistributionV2.sol#254-310):
	External calls:
	- IERC20(depositToken).safeTransferFrom(_msgSender(),address(this),amount_) (contracts/capital-protocol/old/DistributionV2.sol#275)
	State variables written after the call(s):
	- poolData.lastUpdate = uint128(block.timestamp) (contracts/capital-protocol/old/DistributionV2.sol#296)
	DistributionV2.poolsData (contracts/capital-protocol/old/DistributionV2.sol#29) can be used in cross function reentrancies:
	- DistributionV2._getCurrentPoolRate(uint256) (contracts/capital-protocol/old/DistributionV2.sol#383-393)
	- DistributionV2._stake(address,uint256,uint256,uint256,uint128) (contracts/capital-protocol/old/DistributionV2.sol#254-310)
	- DistributionV2._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV2.sol#312-373)
	- DistributionV2.claim(uint256,address) (contracts/capital-protocol/old/DistributionV2.sol#164-201)
	- DistributionV2.editPool(uint256,IDistributionV2.Pool) (contracts/capital-protocol/old/DistributionV2.sol#86-100)
	- DistributionV2.lockClaim(uint256,uint128) (contracts/capital-protocol/old/DistributionV2.sol#207-241)
	- DistributionV2.poolsData (contracts/capital-protocol/old/DistributionV2.sol#29)
	- poolData.rate = currentPoolRate_ (contracts/capital-protocol/old/DistributionV2.sol#297)
	DistributionV2.poolsData (contracts/capital-protocol/old/DistributionV2.sol#29) can be used in cross function reentrancies:
	- DistributionV2._getCurrentPoolRate(uint256) (contracts/capital-protocol/old/DistributionV2.sol#383-393)
	- DistributionV2._stake(address,uint256,uint256,uint256,uint128) (contracts/capital-protocol/old/DistributionV2.sol#254-310)
	- DistributionV2._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV2.sol#312-373)
	- DistributionV2.claim(uint256,address) (contracts/capital-protocol/old/DistributionV2.sol#164-201)
	- DistributionV2.editPool(uint256,IDistributionV2.Pool) (contracts/capital-protocol/old/DistributionV2.sol#86-100)
	- DistributionV2.lockClaim(uint256,uint128) (contracts/capital-protocol/old/DistributionV2.sol#207-241)
	- DistributionV2.poolsData (contracts/capital-protocol/old/DistributionV2.sol#29)
	- poolData.totalVirtualDeposited = poolData.totalVirtualDeposited + virtualDeposited_ - userData.virtualDeposited (contracts/capital-protocol/old/DistributionV2.sol#298)
	DistributionV2.poolsData (contracts/capital-protocol/old/DistributionV2.sol#29) can be used in cross function reentrancies:
	- DistributionV2._getCurrentPoolRate(uint256) (contracts/capital-protocol/old/DistributionV2.sol#383-393)
	- DistributionV2._stake(address,uint256,uint256,uint256,uint128) (contracts/capital-protocol/old/DistributionV2.sol#254-310)
	- DistributionV2._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV2.sol#312-373)
	- DistributionV2.claim(uint256,address) (contracts/capital-protocol/old/DistributionV2.sol#164-201)
	- DistributionV2.editPool(uint256,IDistributionV2.Pool) (contracts/capital-protocol/old/DistributionV2.sol#86-100)
	- DistributionV2.lockClaim(uint256,uint128) (contracts/capital-protocol/old/DistributionV2.sol#207-241)
	- DistributionV2.poolsData (contracts/capital-protocol/old/DistributionV2.sol#29)
	- userData.pendingRewards = _getCurrentUserReward(currentPoolRate_,userData) (contracts/capital-protocol/old/DistributionV2.sol#285)
	DistributionV2.usersData (contracts/capital-protocol/old/DistributionV2.sol#32) can be used in cross function reentrancies:
	- DistributionV2._stake(address,uint256,uint256,uint256,uint128) (contracts/capital-protocol/old/DistributionV2.sol#254-310)
	- DistributionV2._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV2.sol#312-373)
	- DistributionV2.claim(uint256,address) (contracts/capital-protocol/old/DistributionV2.sol#164-201)
	- DistributionV2.getCurrentUserMultiplier(uint256,address) (contracts/capital-protocol/old/DistributionV2.sol#415-423)
	- DistributionV2.getCurrentUserReward(uint256,address) (contracts/capital-protocol/old/DistributionV2.sol#243-252)
	- DistributionV2.lockClaim(uint256,uint128) (contracts/capital-protocol/old/DistributionV2.sol#207-241)
	- DistributionV2.manageUsersInPrivatePool(uint256,address[],uint256[],uint128[]) (contracts/capital-protocol/old/DistributionV2.sol#127-151)
	- DistributionV2.usersData (contracts/capital-protocol/old/DistributionV2.sol#32)
	- userData.virtualDeposited = userData.deposited (contracts/capital-protocol/old/DistributionV2.sol#292)
	DistributionV2.usersData (contracts/capital-protocol/old/DistributionV2.sol#32) can be used in cross function reentrancies:
	- DistributionV2._stake(address,uint256,uint256,uint256,uint128) (contracts/capital-protocol/old/DistributionV2.sol#254-310)
	- DistributionV2._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV2.sol#312-373)
	- DistributionV2.claim(uint256,address) (contracts/capital-protocol/old/DistributionV2.sol#164-201)
	- DistributionV2.getCurrentUserMultiplier(uint256,address) (contracts/capital-protocol/old/DistributionV2.sol#415-423)
	- DistributionV2.getCurrentUserReward(uint256,address) (contracts/capital-protocol/old/DistributionV2.sol#243-252)
	- DistributionV2.lockClaim(uint256,uint128) (contracts/capital-protocol/old/DistributionV2.sol#207-241)
	- DistributionV2.manageUsersInPrivatePool(uint256,address[],uint256[],uint128[]) (contracts/capital-protocol/old/DistributionV2.sol#127-151)
	- DistributionV2.usersData (contracts/capital-protocol/old/DistributionV2.sol#32)
	- userData.lastStake = uint128(block.timestamp) (contracts/capital-protocol/old/DistributionV2.sol#301)
	DistributionV2.usersData (contracts/capital-protocol/old/DistributionV2.sol#32) can be used in cross function reentrancies:
	- DistributionV2._stake(address,uint256,uint256,uint256,uint128) (contracts/capital-protocol/old/DistributionV2.sol#254-310)
	- DistributionV2._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV2.sol#312-373)
	- DistributionV2.claim(uint256,address) (contracts/capital-protocol/old/DistributionV2.sol#164-201)
	- DistributionV2.getCurrentUserMultiplier(uint256,address) (contracts/capital-protocol/old/DistributionV2.sol#415-423)
	- DistributionV2.getCurrentUserReward(uint256,address) (contracts/capital-protocol/old/DistributionV2.sol#243-252)
	- DistributionV2.lockClaim(uint256,uint128) (contracts/capital-protocol/old/DistributionV2.sol#207-241)
	- DistributionV2.manageUsersInPrivatePool(uint256,address[],uint256[],uint128[]) (contracts/capital-protocol/old/DistributionV2.sol#127-151)
	- DistributionV2.usersData (contracts/capital-protocol/old/DistributionV2.sol#32)
	- userData.rate = currentPoolRate_ (contracts/capital-protocol/old/DistributionV2.sol#302)
	DistributionV2.usersData (contracts/capital-protocol/old/DistributionV2.sol#32) can be used in cross function reentrancies:
	- DistributionV2._stake(address,uint256,uint256,uint256,uint128) (contracts/capital-protocol/old/DistributionV2.sol#254-310)
	- DistributionV2._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV2.sol#312-373)
	- DistributionV2.claim(uint256,address) (contracts/capital-protocol/old/DistributionV2.sol#164-201)
	- DistributionV2.getCurrentUserMultiplier(uint256,address) (contracts/capital-protocol/old/DistributionV2.sol#415-423)
	- DistributionV2.getCurrentUserReward(uint256,address) (contracts/capital-protocol/old/DistributionV2.sol#243-252)
	- DistributionV2.lockClaim(uint256,uint128) (contracts/capital-protocol/old/DistributionV2.sol#207-241)
	- DistributionV2.manageUsersInPrivatePool(uint256,address[],uint256[],uint128[]) (contracts/capital-protocol/old/DistributionV2.sol#127-151)
	- DistributionV2.usersData (contracts/capital-protocol/old/DistributionV2.sol#32)
	- userData.deposited = deposited_ (contracts/capital-protocol/old/DistributionV2.sol#303)
	DistributionV2.usersData (contracts/capital-protocol/old/DistributionV2.sol#32) can be used in cross function reentrancies:
	- DistributionV2._stake(address,uint256,uint256,uint256,uint128) (contracts/capital-protocol/old/DistributionV2.sol#254-310)
	- DistributionV2._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV2.sol#312-373)
	- DistributionV2.claim(uint256,address) (contracts/capital-protocol/old/DistributionV2.sol#164-201)
	- DistributionV2.getCurrentUserMultiplier(uint256,address) (contracts/capital-protocol/old/DistributionV2.sol#415-423)
	- DistributionV2.getCurrentUserReward(uint256,address) (contracts/capital-protocol/old/DistributionV2.sol#243-252)
	- DistributionV2.lockClaim(uint256,uint128) (contracts/capital-protocol/old/DistributionV2.sol#207-241)
	- DistributionV2.manageUsersInPrivatePool(uint256,address[],uint256[],uint128[]) (contracts/capital-protocol/old/DistributionV2.sol#127-151)
	- DistributionV2.usersData (contracts/capital-protocol/old/DistributionV2.sol#32)
	- userData.virtualDeposited = virtualDeposited_ (contracts/capital-protocol/old/DistributionV2.sol#304)
	DistributionV2.usersData (contracts/capital-protocol/old/DistributionV2.sol#32) can be used in cross function reentrancies:
	- DistributionV2._stake(address,uint256,uint256,uint256,uint128) (contracts/capital-protocol/old/DistributionV2.sol#254-310)
	- DistributionV2._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV2.sol#312-373)
	- DistributionV2.claim(uint256,address) (contracts/capital-protocol/old/DistributionV2.sol#164-201)
	- DistributionV2.getCurrentUserMultiplier(uint256,address) (contracts/capital-protocol/old/DistributionV2.sol#415-423)
	- DistributionV2.getCurrentUserReward(uint256,address) (contracts/capital-protocol/old/DistributionV2.sol#243-252)
	- DistributionV2.lockClaim(uint256,uint128) (contracts/capital-protocol/old/DistributionV2.sol#207-241)
	- DistributionV2.manageUsersInPrivatePool(uint256,address[],uint256[],uint128[]) (contracts/capital-protocol/old/DistributionV2.sol#127-151)
	- DistributionV2.usersData (contracts/capital-protocol/old/DistributionV2.sol#32)
	- userData.claimLockStart = uint128(block.timestamp) (contracts/capital-protocol/old/DistributionV2.sol#305)
	DistributionV2.usersData (contracts/capital-protocol/old/DistributionV2.sol#32) can be used in cross function reentrancies:
	- DistributionV2._stake(address,uint256,uint256,uint256,uint128) (contracts/capital-protocol/old/DistributionV2.sol#254-310)
	- DistributionV2._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV2.sol#312-373)
	- DistributionV2.claim(uint256,address) (contracts/capital-protocol/old/DistributionV2.sol#164-201)
	- DistributionV2.getCurrentUserMultiplier(uint256,address) (contracts/capital-protocol/old/DistributionV2.sol#415-423)
	- DistributionV2.getCurrentUserReward(uint256,address) (contracts/capital-protocol/old/DistributionV2.sol#243-252)
	- DistributionV2.lockClaim(uint256,uint128) (contracts/capital-protocol/old/DistributionV2.sol#207-241)
	- DistributionV2.manageUsersInPrivatePool(uint256,address[],uint256[],uint128[]) (contracts/capital-protocol/old/DistributionV2.sol#127-151)
	- DistributionV2.usersData (contracts/capital-protocol/old/DistributionV2.sol#32)
	- userData.claimLockEnd = claimLockEnd_ (contracts/capital-protocol/old/DistributionV2.sol#306)
	DistributionV2.usersData (contracts/capital-protocol/old/DistributionV2.sol#32) can be used in cross function reentrancies:
	- DistributionV2._stake(address,uint256,uint256,uint256,uint128) (contracts/capital-protocol/old/DistributionV2.sol#254-310)
	- DistributionV2._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV2.sol#312-373)
	- DistributionV2.claim(uint256,address) (contracts/capital-protocol/old/DistributionV2.sol#164-201)
	- DistributionV2.getCurrentUserMultiplier(uint256,address) (contracts/capital-protocol/old/DistributionV2.sol#415-423)
	- DistributionV2.getCurrentUserReward(uint256,address) (contracts/capital-protocol/old/DistributionV2.sol#243-252)
	- DistributionV2.lockClaim(uint256,uint128) (contracts/capital-protocol/old/DistributionV2.sol#207-241)
	- DistributionV2.manageUsersInPrivatePool(uint256,address[],uint256[],uint128[]) (contracts/capital-protocol/old/DistributionV2.sol#127-151)
	- DistributionV2.usersData (contracts/capital-protocol/old/DistributionV2.sol#32)
Reentrancy in DistributionV3._stake(address,uint256,uint256,uint256,uint128) (contracts/capital-protocol/old/DistributionV3.sol#254-310):
	External calls:
	- IERC20(depositToken).safeTransferFrom(_msgSender(),address(this),amount_) (contracts/capital-protocol/old/DistributionV3.sol#275)
	State variables written after the call(s):
	- poolData.lastUpdate = uint128(block.timestamp) (contracts/capital-protocol/old/DistributionV3.sol#296)
	DistributionV3.poolsData (contracts/capital-protocol/old/DistributionV3.sol#29) can be used in cross function reentrancies:
	- DistributionV3._getCurrentPoolRate(uint256) (contracts/capital-protocol/old/DistributionV3.sol#383-393)
	- DistributionV3._stake(address,uint256,uint256,uint256,uint128) (contracts/capital-protocol/old/DistributionV3.sol#254-310)
	- DistributionV3._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV3.sol#312-373)
	- DistributionV3.claim(uint256,address) (contracts/capital-protocol/old/DistributionV3.sol#164-201)
	- DistributionV3.editPool(uint256,IDistributionV3.Pool) (contracts/capital-protocol/old/DistributionV3.sol#86-100)
	- DistributionV3.lockClaim(uint256,uint128) (contracts/capital-protocol/old/DistributionV3.sol#207-241)
	- DistributionV3.poolsData (contracts/capital-protocol/old/DistributionV3.sol#29)
	- poolData.rate = currentPoolRate_ (contracts/capital-protocol/old/DistributionV3.sol#297)
	DistributionV3.poolsData (contracts/capital-protocol/old/DistributionV3.sol#29) can be used in cross function reentrancies:
	- DistributionV3._getCurrentPoolRate(uint256) (contracts/capital-protocol/old/DistributionV3.sol#383-393)
	- DistributionV3._stake(address,uint256,uint256,uint256,uint128) (contracts/capital-protocol/old/DistributionV3.sol#254-310)
	- DistributionV3._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV3.sol#312-373)
	- DistributionV3.claim(uint256,address) (contracts/capital-protocol/old/DistributionV3.sol#164-201)
	- DistributionV3.editPool(uint256,IDistributionV3.Pool) (contracts/capital-protocol/old/DistributionV3.sol#86-100)
	- DistributionV3.lockClaim(uint256,uint128) (contracts/capital-protocol/old/DistributionV3.sol#207-241)
	- DistributionV3.poolsData (contracts/capital-protocol/old/DistributionV3.sol#29)
	- poolData.totalVirtualDeposited = poolData.totalVirtualDeposited + virtualDeposited_ - userData.virtualDeposited (contracts/capital-protocol/old/DistributionV3.sol#298)
	DistributionV3.poolsData (contracts/capital-protocol/old/DistributionV3.sol#29) can be used in cross function reentrancies:
	- DistributionV3._getCurrentPoolRate(uint256) (contracts/capital-protocol/old/DistributionV3.sol#383-393)
	- DistributionV3._stake(address,uint256,uint256,uint256,uint128) (contracts/capital-protocol/old/DistributionV3.sol#254-310)
	- DistributionV3._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV3.sol#312-373)
	- DistributionV3.claim(uint256,address) (contracts/capital-protocol/old/DistributionV3.sol#164-201)
	- DistributionV3.editPool(uint256,IDistributionV3.Pool) (contracts/capital-protocol/old/DistributionV3.sol#86-100)
	- DistributionV3.lockClaim(uint256,uint128) (contracts/capital-protocol/old/DistributionV3.sol#207-241)
	- DistributionV3.poolsData (contracts/capital-protocol/old/DistributionV3.sol#29)
	- userData.pendingRewards = _getCurrentUserReward(currentPoolRate_,userData) (contracts/capital-protocol/old/DistributionV3.sol#285)
	DistributionV3.usersData (contracts/capital-protocol/old/DistributionV3.sol#32) can be used in cross function reentrancies:
	- DistributionV3._stake(address,uint256,uint256,uint256,uint128) (contracts/capital-protocol/old/DistributionV3.sol#254-310)
	- DistributionV3._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV3.sol#312-373)
	- DistributionV3.claim(uint256,address) (contracts/capital-protocol/old/DistributionV3.sol#164-201)
	- DistributionV3.getCurrentUserMultiplier(uint256,address) (contracts/capital-protocol/old/DistributionV3.sol#415-423)
	- DistributionV3.getCurrentUserReward(uint256,address) (contracts/capital-protocol/old/DistributionV3.sol#243-252)
	- DistributionV3.lockClaim(uint256,uint128) (contracts/capital-protocol/old/DistributionV3.sol#207-241)
	- DistributionV3.manageUsersInPrivatePool(uint256,address[],uint256[],uint128[]) (contracts/capital-protocol/old/DistributionV3.sol#127-151)
	- DistributionV3.usersData (contracts/capital-protocol/old/DistributionV3.sol#32)
	- userData.virtualDeposited = userData.deposited (contracts/capital-protocol/old/DistributionV3.sol#292)
	DistributionV3.usersData (contracts/capital-protocol/old/DistributionV3.sol#32) can be used in cross function reentrancies:
	- DistributionV3._stake(address,uint256,uint256,uint256,uint128) (contracts/capital-protocol/old/DistributionV3.sol#254-310)
	- DistributionV3._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV3.sol#312-373)
	- DistributionV3.claim(uint256,address) (contracts/capital-protocol/old/DistributionV3.sol#164-201)
	- DistributionV3.getCurrentUserMultiplier(uint256,address) (contracts/capital-protocol/old/DistributionV3.sol#415-423)
	- DistributionV3.getCurrentUserReward(uint256,address) (contracts/capital-protocol/old/DistributionV3.sol#243-252)
	- DistributionV3.lockClaim(uint256,uint128) (contracts/capital-protocol/old/DistributionV3.sol#207-241)
	- DistributionV3.manageUsersInPrivatePool(uint256,address[],uint256[],uint128[]) (contracts/capital-protocol/old/DistributionV3.sol#127-151)
	- DistributionV3.usersData (contracts/capital-protocol/old/DistributionV3.sol#32)
	- userData.lastStake = uint128(block.timestamp) (contracts/capital-protocol/old/DistributionV3.sol#301)
	DistributionV3.usersData (contracts/capital-protocol/old/DistributionV3.sol#32) can be used in cross function reentrancies:
	- DistributionV3._stake(address,uint256,uint256,uint256,uint128) (contracts/capital-protocol/old/DistributionV3.sol#254-310)
	- DistributionV3._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV3.sol#312-373)
	- DistributionV3.claim(uint256,address) (contracts/capital-protocol/old/DistributionV3.sol#164-201)
	- DistributionV3.getCurrentUserMultiplier(uint256,address) (contracts/capital-protocol/old/DistributionV3.sol#415-423)
	- DistributionV3.getCurrentUserReward(uint256,address) (contracts/capital-protocol/old/DistributionV3.sol#243-252)
	- DistributionV3.lockClaim(uint256,uint128) (contracts/capital-protocol/old/DistributionV3.sol#207-241)
	- DistributionV3.manageUsersInPrivatePool(uint256,address[],uint256[],uint128[]) (contracts/capital-protocol/old/DistributionV3.sol#127-151)
	- DistributionV3.usersData (contracts/capital-protocol/old/DistributionV3.sol#32)
	- userData.rate = currentPoolRate_ (contracts/capital-protocol/old/DistributionV3.sol#302)
	DistributionV3.usersData (contracts/capital-protocol/old/DistributionV3.sol#32) can be used in cross function reentrancies:
	- DistributionV3._stake(address,uint256,uint256,uint256,uint128) (contracts/capital-protocol/old/DistributionV3.sol#254-310)
	- DistributionV3._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV3.sol#312-373)
	- DistributionV3.claim(uint256,address) (contracts/capital-protocol/old/DistributionV3.sol#164-201)
	- DistributionV3.getCurrentUserMultiplier(uint256,address) (contracts/capital-protocol/old/DistributionV3.sol#415-423)
	- DistributionV3.getCurrentUserReward(uint256,address) (contracts/capital-protocol/old/DistributionV3.sol#243-252)
	- DistributionV3.lockClaim(uint256,uint128) (contracts/capital-protocol/old/DistributionV3.sol#207-241)
	- DistributionV3.manageUsersInPrivatePool(uint256,address[],uint256[],uint128[]) (contracts/capital-protocol/old/DistributionV3.sol#127-151)
	- DistributionV3.usersData (contracts/capital-protocol/old/DistributionV3.sol#32)
	- userData.deposited = deposited_ (contracts/capital-protocol/old/DistributionV3.sol#303)
	DistributionV3.usersData (contracts/capital-protocol/old/DistributionV3.sol#32) can be used in cross function reentrancies:
	- DistributionV3._stake(address,uint256,uint256,uint256,uint128) (contracts/capital-protocol/old/DistributionV3.sol#254-310)
	- DistributionV3._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV3.sol#312-373)
	- DistributionV3.claim(uint256,address) (contracts/capital-protocol/old/DistributionV3.sol#164-201)
	- DistributionV3.getCurrentUserMultiplier(uint256,address) (contracts/capital-protocol/old/DistributionV3.sol#415-423)
	- DistributionV3.getCurrentUserReward(uint256,address) (contracts/capital-protocol/old/DistributionV3.sol#243-252)
	- DistributionV3.lockClaim(uint256,uint128) (contracts/capital-protocol/old/DistributionV3.sol#207-241)
	- DistributionV3.manageUsersInPrivatePool(uint256,address[],uint256[],uint128[]) (contracts/capital-protocol/old/DistributionV3.sol#127-151)
	- DistributionV3.usersData (contracts/capital-protocol/old/DistributionV3.sol#32)
	- userData.virtualDeposited = virtualDeposited_ (contracts/capital-protocol/old/DistributionV3.sol#304)
	DistributionV3.usersData (contracts/capital-protocol/old/DistributionV3.sol#32) can be used in cross function reentrancies:
	- DistributionV3._stake(address,uint256,uint256,uint256,uint128) (contracts/capital-protocol/old/DistributionV3.sol#254-310)
	- DistributionV3._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV3.sol#312-373)
	- DistributionV3.claim(uint256,address) (contracts/capital-protocol/old/DistributionV3.sol#164-201)
	- DistributionV3.getCurrentUserMultiplier(uint256,address) (contracts/capital-protocol/old/DistributionV3.sol#415-423)
	- DistributionV3.getCurrentUserReward(uint256,address) (contracts/capital-protocol/old/DistributionV3.sol#243-252)
	- DistributionV3.lockClaim(uint256,uint128) (contracts/capital-protocol/old/DistributionV3.sol#207-241)
	- DistributionV3.manageUsersInPrivatePool(uint256,address[],uint256[],uint128[]) (contracts/capital-protocol/old/DistributionV3.sol#127-151)
	- DistributionV3.usersData (contracts/capital-protocol/old/DistributionV3.sol#32)
	- userData.claimLockStart = uint128(block.timestamp) (contracts/capital-protocol/old/DistributionV3.sol#305)
	DistributionV3.usersData (contracts/capital-protocol/old/DistributionV3.sol#32) can be used in cross function reentrancies:
	- DistributionV3._stake(address,uint256,uint256,uint256,uint128) (contracts/capital-protocol/old/DistributionV3.sol#254-310)
	- DistributionV3._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV3.sol#312-373)
	- DistributionV3.claim(uint256,address) (contracts/capital-protocol/old/DistributionV3.sol#164-201)
	- DistributionV3.getCurrentUserMultiplier(uint256,address) (contracts/capital-protocol/old/DistributionV3.sol#415-423)
	- DistributionV3.getCurrentUserReward(uint256,address) (contracts/capital-protocol/old/DistributionV3.sol#243-252)
	- DistributionV3.lockClaim(uint256,uint128) (contracts/capital-protocol/old/DistributionV3.sol#207-241)
	- DistributionV3.manageUsersInPrivatePool(uint256,address[],uint256[],uint128[]) (contracts/capital-protocol/old/DistributionV3.sol#127-151)
	- DistributionV3.usersData (contracts/capital-protocol/old/DistributionV3.sol#32)
	- userData.claimLockEnd = claimLockEnd_ (contracts/capital-protocol/old/DistributionV3.sol#306)
	DistributionV3.usersData (contracts/capital-protocol/old/DistributionV3.sol#32) can be used in cross function reentrancies:
	- DistributionV3._stake(address,uint256,uint256,uint256,uint128) (contracts/capital-protocol/old/DistributionV3.sol#254-310)
	- DistributionV3._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV3.sol#312-373)
	- DistributionV3.claim(uint256,address) (contracts/capital-protocol/old/DistributionV3.sol#164-201)
	- DistributionV3.getCurrentUserMultiplier(uint256,address) (contracts/capital-protocol/old/DistributionV3.sol#415-423)
	- DistributionV3.getCurrentUserReward(uint256,address) (contracts/capital-protocol/old/DistributionV3.sol#243-252)
	- DistributionV3.lockClaim(uint256,uint128) (contracts/capital-protocol/old/DistributionV3.sol#207-241)
	- DistributionV3.manageUsersInPrivatePool(uint256,address[],uint256[],uint128[]) (contracts/capital-protocol/old/DistributionV3.sol#127-151)
	- DistributionV3.usersData (contracts/capital-protocol/old/DistributionV3.sol#32)
Reentrancy in DistributionV4._stake(address,uint256,uint256,uint256,uint128) (contracts/capital-protocol/old/DistributionV4.sol#273-329):
	External calls:
	- IERC20(depositToken).safeTransferFrom(_msgSender(),address(this),amount_) (contracts/capital-protocol/old/DistributionV4.sol#294)
	State variables written after the call(s):
	- poolData.lastUpdate = uint128(block.timestamp) (contracts/capital-protocol/old/DistributionV4.sol#315)
	DistributionV4.poolsData (contracts/capital-protocol/old/DistributionV4.sol#29) can be used in cross function reentrancies:
	- DistributionV4._getCurrentPoolRate(uint256) (contracts/capital-protocol/old/DistributionV4.sol#402-412)
	- DistributionV4._stake(address,uint256,uint256,uint256,uint128) (contracts/capital-protocol/old/DistributionV4.sol#273-329)
	- DistributionV4._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV4.sol#331-392)
	- DistributionV4.claim(uint256,address) (contracts/capital-protocol/old/DistributionV4.sol#173-220)
	- DistributionV4.editPool(uint256,IDistributionV4.Pool) (contracts/capital-protocol/old/DistributionV4.sol#89-103)
	- DistributionV4.lockClaim(uint256,uint128) (contracts/capital-protocol/old/DistributionV4.sol#226-260)
	- DistributionV4.poolsData (contracts/capital-protocol/old/DistributionV4.sol#29)
	- poolData.rate = currentPoolRate_ (contracts/capital-protocol/old/DistributionV4.sol#316)
	DistributionV4.poolsData (contracts/capital-protocol/old/DistributionV4.sol#29) can be used in cross function reentrancies:
	- DistributionV4._getCurrentPoolRate(uint256) (contracts/capital-protocol/old/DistributionV4.sol#402-412)
	- DistributionV4._stake(address,uint256,uint256,uint256,uint128) (contracts/capital-protocol/old/DistributionV4.sol#273-329)
	- DistributionV4._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV4.sol#331-392)
	- DistributionV4.claim(uint256,address) (contracts/capital-protocol/old/DistributionV4.sol#173-220)
	- DistributionV4.editPool(uint256,IDistributionV4.Pool) (contracts/capital-protocol/old/DistributionV4.sol#89-103)
	- DistributionV4.lockClaim(uint256,uint128) (contracts/capital-protocol/old/DistributionV4.sol#226-260)
	- DistributionV4.poolsData (contracts/capital-protocol/old/DistributionV4.sol#29)
	- poolData.totalVirtualDeposited = poolData.totalVirtualDeposited + virtualDeposited_ - userData.virtualDeposited (contracts/capital-protocol/old/DistributionV4.sol#317)
	DistributionV4.poolsData (contracts/capital-protocol/old/DistributionV4.sol#29) can be used in cross function reentrancies:
	- DistributionV4._getCurrentPoolRate(uint256) (contracts/capital-protocol/old/DistributionV4.sol#402-412)
	- DistributionV4._stake(address,uint256,uint256,uint256,uint128) (contracts/capital-protocol/old/DistributionV4.sol#273-329)
	- DistributionV4._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV4.sol#331-392)
	- DistributionV4.claim(uint256,address) (contracts/capital-protocol/old/DistributionV4.sol#173-220)
	- DistributionV4.editPool(uint256,IDistributionV4.Pool) (contracts/capital-protocol/old/DistributionV4.sol#89-103)
	- DistributionV4.lockClaim(uint256,uint128) (contracts/capital-protocol/old/DistributionV4.sol#226-260)
	- DistributionV4.poolsData (contracts/capital-protocol/old/DistributionV4.sol#29)
	- userData.pendingRewards = _getCurrentUserReward(currentPoolRate_,userData) (contracts/capital-protocol/old/DistributionV4.sol#304)
	DistributionV4.usersData (contracts/capital-protocol/old/DistributionV4.sol#32) can be used in cross function reentrancies:
	- DistributionV4._stake(address,uint256,uint256,uint256,uint128) (contracts/capital-protocol/old/DistributionV4.sol#273-329)
	- DistributionV4._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV4.sol#331-392)
	- DistributionV4.claim(uint256,address) (contracts/capital-protocol/old/DistributionV4.sol#173-220)
	- DistributionV4.getCurrentUserMultiplier(uint256,address) (contracts/capital-protocol/old/DistributionV4.sol#434-442)
	- DistributionV4.getCurrentUserReward(uint256,address) (contracts/capital-protocol/old/DistributionV4.sol#262-271)
	- DistributionV4.lockClaim(uint256,uint128) (contracts/capital-protocol/old/DistributionV4.sol#226-260)
	- DistributionV4.manageUsersInPrivatePool(uint256,address[],uint256[],uint128[]) (contracts/capital-protocol/old/DistributionV4.sol#136-160)
	- DistributionV4.usersData (contracts/capital-protocol/old/DistributionV4.sol#32)
	- userData.virtualDeposited = userData.deposited (contracts/capital-protocol/old/DistributionV4.sol#311)
	DistributionV4.usersData (contracts/capital-protocol/old/DistributionV4.sol#32) can be used in cross function reentrancies:
	- DistributionV4._stake(address,uint256,uint256,uint256,uint128) (contracts/capital-protocol/old/DistributionV4.sol#273-329)
	- DistributionV4._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV4.sol#331-392)
	- DistributionV4.claim(uint256,address) (contracts/capital-protocol/old/DistributionV4.sol#173-220)
	- DistributionV4.getCurrentUserMultiplier(uint256,address) (contracts/capital-protocol/old/DistributionV4.sol#434-442)
	- DistributionV4.getCurrentUserReward(uint256,address) (contracts/capital-protocol/old/DistributionV4.sol#262-271)
	- DistributionV4.lockClaim(uint256,uint128) (contracts/capital-protocol/old/DistributionV4.sol#226-260)
	- DistributionV4.manageUsersInPrivatePool(uint256,address[],uint256[],uint128[]) (contracts/capital-protocol/old/DistributionV4.sol#136-160)
	- DistributionV4.usersData (contracts/capital-protocol/old/DistributionV4.sol#32)
	- userData.lastStake = uint128(block.timestamp) (contracts/capital-protocol/old/DistributionV4.sol#320)
	DistributionV4.usersData (contracts/capital-protocol/old/DistributionV4.sol#32) can be used in cross function reentrancies:
	- DistributionV4._stake(address,uint256,uint256,uint256,uint128) (contracts/capital-protocol/old/DistributionV4.sol#273-329)
	- DistributionV4._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV4.sol#331-392)
	- DistributionV4.claim(uint256,address) (contracts/capital-protocol/old/DistributionV4.sol#173-220)
	- DistributionV4.getCurrentUserMultiplier(uint256,address) (contracts/capital-protocol/old/DistributionV4.sol#434-442)
	- DistributionV4.getCurrentUserReward(uint256,address) (contracts/capital-protocol/old/DistributionV4.sol#262-271)
	- DistributionV4.lockClaim(uint256,uint128) (contracts/capital-protocol/old/DistributionV4.sol#226-260)
	- DistributionV4.manageUsersInPrivatePool(uint256,address[],uint256[],uint128[]) (contracts/capital-protocol/old/DistributionV4.sol#136-160)
	- DistributionV4.usersData (contracts/capital-protocol/old/DistributionV4.sol#32)
	- userData.rate = currentPoolRate_ (contracts/capital-protocol/old/DistributionV4.sol#321)
	DistributionV4.usersData (contracts/capital-protocol/old/DistributionV4.sol#32) can be used in cross function reentrancies:
	- DistributionV4._stake(address,uint256,uint256,uint256,uint128) (contracts/capital-protocol/old/DistributionV4.sol#273-329)
	- DistributionV4._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV4.sol#331-392)
	- DistributionV4.claim(uint256,address) (contracts/capital-protocol/old/DistributionV4.sol#173-220)
	- DistributionV4.getCurrentUserMultiplier(uint256,address) (contracts/capital-protocol/old/DistributionV4.sol#434-442)
	- DistributionV4.getCurrentUserReward(uint256,address) (contracts/capital-protocol/old/DistributionV4.sol#262-271)
	- DistributionV4.lockClaim(uint256,uint128) (contracts/capital-protocol/old/DistributionV4.sol#226-260)
	- DistributionV4.manageUsersInPrivatePool(uint256,address[],uint256[],uint128[]) (contracts/capital-protocol/old/DistributionV4.sol#136-160)
	- DistributionV4.usersData (contracts/capital-protocol/old/DistributionV4.sol#32)
	- userData.deposited = deposited_ (contracts/capital-protocol/old/DistributionV4.sol#322)
	DistributionV4.usersData (contracts/capital-protocol/old/DistributionV4.sol#32) can be used in cross function reentrancies:
	- DistributionV4._stake(address,uint256,uint256,uint256,uint128) (contracts/capital-protocol/old/DistributionV4.sol#273-329)
	- DistributionV4._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV4.sol#331-392)
	- DistributionV4.claim(uint256,address) (contracts/capital-protocol/old/DistributionV4.sol#173-220)
	- DistributionV4.getCurrentUserMultiplier(uint256,address) (contracts/capital-protocol/old/DistributionV4.sol#434-442)
	- DistributionV4.getCurrentUserReward(uint256,address) (contracts/capital-protocol/old/DistributionV4.sol#262-271)
	- DistributionV4.lockClaim(uint256,uint128) (contracts/capital-protocol/old/DistributionV4.sol#226-260)
	- DistributionV4.manageUsersInPrivatePool(uint256,address[],uint256[],uint128[]) (contracts/capital-protocol/old/DistributionV4.sol#136-160)
	- DistributionV4.usersData (contracts/capital-protocol/old/DistributionV4.sol#32)
	- userData.virtualDeposited = virtualDeposited_ (contracts/capital-protocol/old/DistributionV4.sol#323)
	DistributionV4.usersData (contracts/capital-protocol/old/DistributionV4.sol#32) can be used in cross function reentrancies:
	- DistributionV4._stake(address,uint256,uint256,uint256,uint128) (contracts/capital-protocol/old/DistributionV4.sol#273-329)
	- DistributionV4._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV4.sol#331-392)
	- DistributionV4.claim(uint256,address) (contracts/capital-protocol/old/DistributionV4.sol#173-220)
	- DistributionV4.getCurrentUserMultiplier(uint256,address) (contracts/capital-protocol/old/DistributionV4.sol#434-442)
	- DistributionV4.getCurrentUserReward(uint256,address) (contracts/capital-protocol/old/DistributionV4.sol#262-271)
	- DistributionV4.lockClaim(uint256,uint128) (contracts/capital-protocol/old/DistributionV4.sol#226-260)
	- DistributionV4.manageUsersInPrivatePool(uint256,address[],uint256[],uint128[]) (contracts/capital-protocol/old/DistributionV4.sol#136-160)
	- DistributionV4.usersData (contracts/capital-protocol/old/DistributionV4.sol#32)
	- userData.claimLockStart = uint128(block.timestamp) (contracts/capital-protocol/old/DistributionV4.sol#324)
	DistributionV4.usersData (contracts/capital-protocol/old/DistributionV4.sol#32) can be used in cross function reentrancies:
	- DistributionV4._stake(address,uint256,uint256,uint256,uint128) (contracts/capital-protocol/old/DistributionV4.sol#273-329)
	- DistributionV4._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV4.sol#331-392)
	- DistributionV4.claim(uint256,address) (contracts/capital-protocol/old/DistributionV4.sol#173-220)
	- DistributionV4.getCurrentUserMultiplier(uint256,address) (contracts/capital-protocol/old/DistributionV4.sol#434-442)
	- DistributionV4.getCurrentUserReward(uint256,address) (contracts/capital-protocol/old/DistributionV4.sol#262-271)
	- DistributionV4.lockClaim(uint256,uint128) (contracts/capital-protocol/old/DistributionV4.sol#226-260)
	- DistributionV4.manageUsersInPrivatePool(uint256,address[],uint256[],uint128[]) (contracts/capital-protocol/old/DistributionV4.sol#136-160)
	- DistributionV4.usersData (contracts/capital-protocol/old/DistributionV4.sol#32)
	- userData.claimLockEnd = claimLockEnd_ (contracts/capital-protocol/old/DistributionV4.sol#325)
	DistributionV4.usersData (contracts/capital-protocol/old/DistributionV4.sol#32) can be used in cross function reentrancies:
	- DistributionV4._stake(address,uint256,uint256,uint256,uint128) (contracts/capital-protocol/old/DistributionV4.sol#273-329)
	- DistributionV4._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV4.sol#331-392)
	- DistributionV4.claim(uint256,address) (contracts/capital-protocol/old/DistributionV4.sol#173-220)
	- DistributionV4.getCurrentUserMultiplier(uint256,address) (contracts/capital-protocol/old/DistributionV4.sol#434-442)
	- DistributionV4.getCurrentUserReward(uint256,address) (contracts/capital-protocol/old/DistributionV4.sol#262-271)
	- DistributionV4.lockClaim(uint256,uint128) (contracts/capital-protocol/old/DistributionV4.sol#226-260)
	- DistributionV4.manageUsersInPrivatePool(uint256,address[],uint256[],uint128[]) (contracts/capital-protocol/old/DistributionV4.sol#136-160)
	- DistributionV4.usersData (contracts/capital-protocol/old/DistributionV4.sol#32)
Reentrancy in DistributionV5._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/old/DistributionV5.sol#351-423):
	External calls:
	- IERC20(depositToken).safeTransferFrom(_msgSender(),address(this),amount_) (contracts/capital-protocol/old/DistributionV5.sol#377)
	State variables written after the call(s):
	- _applyReferrerTier(user_,poolId_,currentPoolRate_,userData.deposited,deposited_,userData.referrer,referrer_) (contracts/capital-protocol/old/DistributionV5.sol#397-405)
		- poolData.totalVirtualDeposited = poolData.totalVirtualDeposited + newVirtualAmountStaked - oldVirtualAmountStaked (contracts/capital-protocol/old/DistributionV5.sol#559-562)
	DistributionV5.poolsData (contracts/capital-protocol/old/DistributionV5.sol#32) can be used in cross function reentrancies:
	- DistributionV5._applyReferrerTier(address,uint256,uint256,uint256,uint256,address,address) (contracts/capital-protocol/old/DistributionV5.sol#505-563)
	- DistributionV5._getCurrentPoolRate(uint256) (contracts/capital-protocol/old/DistributionV5.sol#573-583)
	- DistributionV5._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/old/DistributionV5.sol#351-423)
	- DistributionV5._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV5.sol#425-503)
	- DistributionV5.claim(uint256,address) (contracts/capital-protocol/old/DistributionV5.sol#213-262)
	- DistributionV5.claimReferrerTier(uint256,address) (contracts/capital-protocol/old/DistributionV5.sol#264-290)
	- DistributionV5.editPool(uint256,IDistributionV5.Pool) (contracts/capital-protocol/old/DistributionV5.sol#96-110)
	- DistributionV5.lockClaim(uint256,uint128) (contracts/capital-protocol/old/DistributionV5.sol#296-330)
	- DistributionV5.poolsData (contracts/capital-protocol/old/DistributionV5.sol#32)
	- poolData.lastUpdate = uint128(block.timestamp) (contracts/capital-protocol/old/DistributionV5.sol#408)
	DistributionV5.poolsData (contracts/capital-protocol/old/DistributionV5.sol#32) can be used in cross function reentrancies:
	- DistributionV5._applyReferrerTier(address,uint256,uint256,uint256,uint256,address,address) (contracts/capital-protocol/old/DistributionV5.sol#505-563)
	- DistributionV5._getCurrentPoolRate(uint256) (contracts/capital-protocol/old/DistributionV5.sol#573-583)
	- DistributionV5._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/old/DistributionV5.sol#351-423)
	- DistributionV5._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV5.sol#425-503)
	- DistributionV5.claim(uint256,address) (contracts/capital-protocol/old/DistributionV5.sol#213-262)
	- DistributionV5.claimReferrerTier(uint256,address) (contracts/capital-protocol/old/DistributionV5.sol#264-290)
	- DistributionV5.editPool(uint256,IDistributionV5.Pool) (contracts/capital-protocol/old/DistributionV5.sol#96-110)
	- DistributionV5.lockClaim(uint256,uint128) (contracts/capital-protocol/old/DistributionV5.sol#296-330)
	- DistributionV5.poolsData (contracts/capital-protocol/old/DistributionV5.sol#32)
	- poolData.rate = currentPoolRate_ (contracts/capital-protocol/old/DistributionV5.sol#409)
	DistributionV5.poolsData (contracts/capital-protocol/old/DistributionV5.sol#32) can be used in cross function reentrancies:
	- DistributionV5._applyReferrerTier(address,uint256,uint256,uint256,uint256,address,address) (contracts/capital-protocol/old/DistributionV5.sol#505-563)
	- DistributionV5._getCurrentPoolRate(uint256) (contracts/capital-protocol/old/DistributionV5.sol#573-583)
	- DistributionV5._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/old/DistributionV5.sol#351-423)
	- DistributionV5._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV5.sol#425-503)
	- DistributionV5.claim(uint256,address) (contracts/capital-protocol/old/DistributionV5.sol#213-262)
	- DistributionV5.claimReferrerTier(uint256,address) (contracts/capital-protocol/old/DistributionV5.sol#264-290)
	- DistributionV5.editPool(uint256,IDistributionV5.Pool) (contracts/capital-protocol/old/DistributionV5.sol#96-110)
	- DistributionV5.lockClaim(uint256,uint128) (contracts/capital-protocol/old/DistributionV5.sol#296-330)
	- DistributionV5.poolsData (contracts/capital-protocol/old/DistributionV5.sol#32)
	- poolData.totalVirtualDeposited = poolData.totalVirtualDeposited + virtualDeposited_ - userData.virtualDeposited (contracts/capital-protocol/old/DistributionV5.sol#410)
	DistributionV5.poolsData (contracts/capital-protocol/old/DistributionV5.sol#32) can be used in cross function reentrancies:
	- DistributionV5._applyReferrerTier(address,uint256,uint256,uint256,uint256,address,address) (contracts/capital-protocol/old/DistributionV5.sol#505-563)
	- DistributionV5._getCurrentPoolRate(uint256) (contracts/capital-protocol/old/DistributionV5.sol#573-583)
	- DistributionV5._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/old/DistributionV5.sol#351-423)
	- DistributionV5._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV5.sol#425-503)
	- DistributionV5.claim(uint256,address) (contracts/capital-protocol/old/DistributionV5.sol#213-262)
	- DistributionV5.claimReferrerTier(uint256,address) (contracts/capital-protocol/old/DistributionV5.sol#264-290)
	- DistributionV5.editPool(uint256,IDistributionV5.Pool) (contracts/capital-protocol/old/DistributionV5.sol#96-110)
	- DistributionV5.lockClaim(uint256,uint128) (contracts/capital-protocol/old/DistributionV5.sol#296-330)
	- DistributionV5.poolsData (contracts/capital-protocol/old/DistributionV5.sol#32)
	- userData.pendingRewards = _getCurrentUserReward(currentPoolRate_,userData) (contracts/capital-protocol/old/DistributionV5.sol#387)
	DistributionV5.usersData (contracts/capital-protocol/old/DistributionV5.sol#35) can be used in cross function reentrancies:
	- DistributionV5._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/old/DistributionV5.sol#351-423)
	- DistributionV5._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV5.sol#425-503)
	- DistributionV5.claim(uint256,address) (contracts/capital-protocol/old/DistributionV5.sol#213-262)
	- DistributionV5.getCurrentUserMultiplier(uint256,address) (contracts/capital-protocol/old/DistributionV5.sol#605-613)
	- DistributionV5.getCurrentUserReward(uint256,address) (contracts/capital-protocol/old/DistributionV5.sol#332-341)
	- DistributionV5.lockClaim(uint256,uint128) (contracts/capital-protocol/old/DistributionV5.sol#296-330)
	- DistributionV5.manageUsersInPrivatePool(uint256,address[],uint256[],uint128[],address[]) (contracts/capital-protocol/old/DistributionV5.sol#169-199)
	- DistributionV5.usersData (contracts/capital-protocol/old/DistributionV5.sol#35)
	- userData.virtualDeposited = userData.deposited (contracts/capital-protocol/old/DistributionV5.sol#394)
	DistributionV5.usersData (contracts/capital-protocol/old/DistributionV5.sol#35) can be used in cross function reentrancies:
	- DistributionV5._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/old/DistributionV5.sol#351-423)
	- DistributionV5._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV5.sol#425-503)
	- DistributionV5.claim(uint256,address) (contracts/capital-protocol/old/DistributionV5.sol#213-262)
	- DistributionV5.getCurrentUserMultiplier(uint256,address) (contracts/capital-protocol/old/DistributionV5.sol#605-613)
	- DistributionV5.getCurrentUserReward(uint256,address) (contracts/capital-protocol/old/DistributionV5.sol#332-341)
	- DistributionV5.lockClaim(uint256,uint128) (contracts/capital-protocol/old/DistributionV5.sol#296-330)
	- DistributionV5.manageUsersInPrivatePool(uint256,address[],uint256[],uint128[],address[]) (contracts/capital-protocol/old/DistributionV5.sol#169-199)
	- DistributionV5.usersData (contracts/capital-protocol/old/DistributionV5.sol#35)
	- userData.lastStake = uint128(block.timestamp) (contracts/capital-protocol/old/DistributionV5.sol#413)
	DistributionV5.usersData (contracts/capital-protocol/old/DistributionV5.sol#35) can be used in cross function reentrancies:
	- DistributionV5._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/old/DistributionV5.sol#351-423)
	- DistributionV5._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV5.sol#425-503)
	- DistributionV5.claim(uint256,address) (contracts/capital-protocol/old/DistributionV5.sol#213-262)
	- DistributionV5.getCurrentUserMultiplier(uint256,address) (contracts/capital-protocol/old/DistributionV5.sol#605-613)
	- DistributionV5.getCurrentUserReward(uint256,address) (contracts/capital-protocol/old/DistributionV5.sol#332-341)
	- DistributionV5.lockClaim(uint256,uint128) (contracts/capital-protocol/old/DistributionV5.sol#296-330)
	- DistributionV5.manageUsersInPrivatePool(uint256,address[],uint256[],uint128[],address[]) (contracts/capital-protocol/old/DistributionV5.sol#169-199)
	- DistributionV5.usersData (contracts/capital-protocol/old/DistributionV5.sol#35)
	- userData.rate = currentPoolRate_ (contracts/capital-protocol/old/DistributionV5.sol#414)
	DistributionV5.usersData (contracts/capital-protocol/old/DistributionV5.sol#35) can be used in cross function reentrancies:
	- DistributionV5._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/old/DistributionV5.sol#351-423)
	- DistributionV5._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV5.sol#425-503)
	- DistributionV5.claim(uint256,address) (contracts/capital-protocol/old/DistributionV5.sol#213-262)
	- DistributionV5.getCurrentUserMultiplier(uint256,address) (contracts/capital-protocol/old/DistributionV5.sol#605-613)
	- DistributionV5.getCurrentUserReward(uint256,address) (contracts/capital-protocol/old/DistributionV5.sol#332-341)
	- DistributionV5.lockClaim(uint256,uint128) (contracts/capital-protocol/old/DistributionV5.sol#296-330)
	- DistributionV5.manageUsersInPrivatePool(uint256,address[],uint256[],uint128[],address[]) (contracts/capital-protocol/old/DistributionV5.sol#169-199)
	- DistributionV5.usersData (contracts/capital-protocol/old/DistributionV5.sol#35)
	- userData.deposited = deposited_ (contracts/capital-protocol/old/DistributionV5.sol#415)
	DistributionV5.usersData (contracts/capital-protocol/old/DistributionV5.sol#35) can be used in cross function reentrancies:
	- DistributionV5._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/old/DistributionV5.sol#351-423)
	- DistributionV5._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV5.sol#425-503)
	- DistributionV5.claim(uint256,address) (contracts/capital-protocol/old/DistributionV5.sol#213-262)
	- DistributionV5.getCurrentUserMultiplier(uint256,address) (contracts/capital-protocol/old/DistributionV5.sol#605-613)
	- DistributionV5.getCurrentUserReward(uint256,address) (contracts/capital-protocol/old/DistributionV5.sol#332-341)
	- DistributionV5.lockClaim(uint256,uint128) (contracts/capital-protocol/old/DistributionV5.sol#296-330)
	- DistributionV5.manageUsersInPrivatePool(uint256,address[],uint256[],uint128[],address[]) (contracts/capital-protocol/old/DistributionV5.sol#169-199)
	- DistributionV5.usersData (contracts/capital-protocol/old/DistributionV5.sol#35)
	- userData.virtualDeposited = virtualDeposited_ (contracts/capital-protocol/old/DistributionV5.sol#416)
	DistributionV5.usersData (contracts/capital-protocol/old/DistributionV5.sol#35) can be used in cross function reentrancies:
	- DistributionV5._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/old/DistributionV5.sol#351-423)
	- DistributionV5._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV5.sol#425-503)
	- DistributionV5.claim(uint256,address) (contracts/capital-protocol/old/DistributionV5.sol#213-262)
	- DistributionV5.getCurrentUserMultiplier(uint256,address) (contracts/capital-protocol/old/DistributionV5.sol#605-613)
	- DistributionV5.getCurrentUserReward(uint256,address) (contracts/capital-protocol/old/DistributionV5.sol#332-341)
	- DistributionV5.lockClaim(uint256,uint128) (contracts/capital-protocol/old/DistributionV5.sol#296-330)
	- DistributionV5.manageUsersInPrivatePool(uint256,address[],uint256[],uint128[],address[]) (contracts/capital-protocol/old/DistributionV5.sol#169-199)
	- DistributionV5.usersData (contracts/capital-protocol/old/DistributionV5.sol#35)
	- userData.claimLockStart = uint128(block.timestamp) (contracts/capital-protocol/old/DistributionV5.sol#417)
	DistributionV5.usersData (contracts/capital-protocol/old/DistributionV5.sol#35) can be used in cross function reentrancies:
	- DistributionV5._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/old/DistributionV5.sol#351-423)
	- DistributionV5._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV5.sol#425-503)
	- DistributionV5.claim(uint256,address) (contracts/capital-protocol/old/DistributionV5.sol#213-262)
	- DistributionV5.getCurrentUserMultiplier(uint256,address) (contracts/capital-protocol/old/DistributionV5.sol#605-613)
	- DistributionV5.getCurrentUserReward(uint256,address) (contracts/capital-protocol/old/DistributionV5.sol#332-341)
	- DistributionV5.lockClaim(uint256,uint128) (contracts/capital-protocol/old/DistributionV5.sol#296-330)
	- DistributionV5.manageUsersInPrivatePool(uint256,address[],uint256[],uint128[],address[]) (contracts/capital-protocol/old/DistributionV5.sol#169-199)
	- DistributionV5.usersData (contracts/capital-protocol/old/DistributionV5.sol#35)
	- userData.claimLockEnd = claimLockEnd_ (contracts/capital-protocol/old/DistributionV5.sol#418)
	DistributionV5.usersData (contracts/capital-protocol/old/DistributionV5.sol#35) can be used in cross function reentrancies:
	- DistributionV5._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/old/DistributionV5.sol#351-423)
	- DistributionV5._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV5.sol#425-503)
	- DistributionV5.claim(uint256,address) (contracts/capital-protocol/old/DistributionV5.sol#213-262)
	- DistributionV5.getCurrentUserMultiplier(uint256,address) (contracts/capital-protocol/old/DistributionV5.sol#605-613)
	- DistributionV5.getCurrentUserReward(uint256,address) (contracts/capital-protocol/old/DistributionV5.sol#332-341)
	- DistributionV5.lockClaim(uint256,uint128) (contracts/capital-protocol/old/DistributionV5.sol#296-330)
	- DistributionV5.manageUsersInPrivatePool(uint256,address[],uint256[],uint128[],address[]) (contracts/capital-protocol/old/DistributionV5.sol#169-199)
	- DistributionV5.usersData (contracts/capital-protocol/old/DistributionV5.sol#35)
	- userData.referrer = referrer_ (contracts/capital-protocol/old/DistributionV5.sol#419)
	DistributionV5.usersData (contracts/capital-protocol/old/DistributionV5.sol#35) can be used in cross function reentrancies:
	- DistributionV5._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/old/DistributionV5.sol#351-423)
	- DistributionV5._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV5.sol#425-503)
	- DistributionV5.claim(uint256,address) (contracts/capital-protocol/old/DistributionV5.sol#213-262)
	- DistributionV5.getCurrentUserMultiplier(uint256,address) (contracts/capital-protocol/old/DistributionV5.sol#605-613)
	- DistributionV5.getCurrentUserReward(uint256,address) (contracts/capital-protocol/old/DistributionV5.sol#332-341)
	- DistributionV5.lockClaim(uint256,uint128) (contracts/capital-protocol/old/DistributionV5.sol#296-330)
	- DistributionV5.manageUsersInPrivatePool(uint256,address[],uint256[],uint128[],address[]) (contracts/capital-protocol/old/DistributionV5.sol#169-199)
	- DistributionV5.usersData (contracts/capital-protocol/old/DistributionV5.sol#35)
Reentrancy in Distributor._withdrawYield(uint256,address) (contracts/capital-protocol/Distributor.sol#479-492):
	External calls:
	- IPool(aavePool).withdraw(depositPool.token,yield_,l1Sender) (contracts/capital-protocol/Distributor.sol#486)
	- IERC20(depositPool.token).safeTransfer(l1Sender,yield_) (contracts/capital-protocol/Distributor.sol#488)
	State variables written after the call(s):
	- depositPool.lastUnderlyingBalance -= yield_ (contracts/capital-protocol/Distributor.sol#491)
	Distributor.depositPools (contracts/capital-protocol/Distributor.sol#28) can be used in cross function reentrancies:
	- Distributor._onlyExistedDepositPool(uint256,address) (contracts/capital-protocol/Distributor.sol#250-252)
	- Distributor._withdrawYield(uint256,address) (contracts/capital-protocol/Distributor.sol#479-492)
	- Distributor.addDepositPool(uint256,address,address,string,IDistributor.Strategy) (contracts/capital-protocol/Distributor.sol#192-248)
	- Distributor.depositPools (contracts/capital-protocol/Distributor.sol#28)
	- Distributor.distributeRewards(uint256) (contracts/capital-protocol/Distributor.sol#330-410)
	- Distributor.supply(uint256,uint256) (contracts/capital-protocol/Distributor.sol#285-302)
	- Distributor.updateDepositTokensPrices(uint256) (contracts/capital-protocol/Distributor.sol#258-279)
	- Distributor.withdraw(uint256,uint256) (contracts/capital-protocol/Distributor.sol#304-328)
	- Distributor.withdrawYield(uint256,address) (contracts/capital-protocol/Distributor.sol#416-424)
Reentrancy in Distributor.addDepositPool(uint256,address,address,string,IDistributor.Strategy) (contracts/capital-protocol/Distributor.sol#192-248):
	External calls:
	- IERC20(token_).safeApprove(aavePool,type()(uint256).max) (contracts/capital-protocol/Distributor.sol#232)
	- IERC20(aToken_).approve(aavePool,type()(uint256).max) (contracts/capital-protocol/Distributor.sol#233)
	State variables written after the call(s):
	- depositPoolAddresses[rewardPoolIndex_].push(depositPoolAddress_) (contracts/capital-protocol/Distributor.sol#238)
	Distributor.depositPoolAddresses (contracts/capital-protocol/Distributor.sol#36) can be used in cross function reentrancies:
	- Distributor.addDepositPool(uint256,address,address,string,IDistributor.Strategy) (contracts/capital-protocol/Distributor.sol#192-248)
	- Distributor.depositPoolAddresses (contracts/capital-protocol/Distributor.sol#36)
	- Distributor.distributeRewards(uint256) (contracts/capital-protocol/Distributor.sol#330-410)
	- Distributor.updateDepositTokensPrices(uint256) (contracts/capital-protocol/Distributor.sol#258-279)
	- isDepositTokenAdded[token_] = true (contracts/capital-protocol/Distributor.sol#240)
	Distributor.isDepositTokenAdded (contracts/capital-protocol/Distributor.sol#30) can be used in cross function reentrancies:
	- Distributor.addDepositPool(uint256,address,address,string,IDistributor.Strategy) (contracts/capital-protocol/Distributor.sol#192-248)
	- Distributor.isDepositTokenAdded (contracts/capital-protocol/Distributor.sol#30)
Reentrancy in BuilderSubnets.createSubnet(IBuilderSubnets.Subnet,IBuilderSubnets.SubnetMetadata) (contracts/builder-protocol/BuilderSubnets.sol#167-195):
	External calls:
	- IERC20(token).safeTransferFrom(_msgSender(),subnetCreationFeeTreasury,subnetCreationFeeAmount) (contracts/builder-protocol/BuilderSubnets.sol#187)
	State variables written after the call(s):
	- subnets[subnetId_] = subnet_ (contracts/builder-protocol/BuilderSubnets.sol#190)
	BuilderSubnets.subnets (contracts/builder-protocol/BuilderSubnets.sol#58) can be used in cross function reentrancies:
	- BuilderSubnets._getSubnetFee(uint256,bytes32) (contracts/builder-protocol/BuilderSubnets.sol#498-504)
	- BuilderSubnets._subnetExists(bytes32) (contracts/builder-protocol/BuilderSubnets.sol#514-516)
	- BuilderSubnets.createSubnet(IBuilderSubnets.Subnet,IBuilderSubnets.SubnetMetadata) (contracts/builder-protocol/BuilderSubnets.sol#167-195)
	- BuilderSubnets.onlySubnetOwner(bytes32) (contracts/builder-protocol/BuilderSubnets.sol#73-76)
	- BuilderSubnets.setSubnetFee(bytes32,uint256) (contracts/builder-protocol/BuilderSubnets.sol#226-234)
	- BuilderSubnets.setSubnetFeeTreasury(bytes32,address) (contracts/builder-protocol/BuilderSubnets.sol#236-244)
	- BuilderSubnets.setSubnetMinStake(bytes32,uint256) (contracts/builder-protocol/BuilderSubnets.sol#217-224)
	- BuilderSubnets.setSubnetOwnership(bytes32,address) (contracts/builder-protocol/BuilderSubnets.sol#206-215)
	- BuilderSubnets.stake(bytes32,address,uint256) (contracts/builder-protocol/BuilderSubnets.sol#254-276)
	- BuilderSubnets.subnets (contracts/builder-protocol/BuilderSubnets.sol#58)
	- BuilderSubnets.withdraw(bytes32,uint256) (contracts/builder-protocol/BuilderSubnets.sol#278-306)
Reentrancy in BuildersV2.deposit(bytes32,uint256) (contracts/builder-protocol/BuildersV2.sol#129-148):
	External calls:
	- IERC20(depositToken).safeTransferFrom(_msgSender(),address(this),amount_) (contracts/builder-protocol/BuildersV2.sol#142)
	State variables written after the call(s):
	- _updatePoolData(builderPoolId_,deposited_,userData) (contracts/builder-protocol/BuildersV2.sol#144)
		- userData.deposited = newDeposited_ (contracts/builder-protocol/BuildersV2.sol#256)
		- userData.virtualDeposited = virtualDeposited_ (contracts/builder-protocol/BuildersV2.sol#257)
		- userData.claimLockStart = uint128(block.timestamp) (contracts/builder-protocol/BuildersV2.sol#258)
	BuildersV2.usersData (contracts/builder-protocol/BuildersV2.sol#31) can be used in cross function reentrancies:
	- BuildersV2.deposit(bytes32,uint256) (contracts/builder-protocol/BuildersV2.sol#129-148)
	- BuildersV2.getCurrentUserMultiplier(bytes32,address) (contracts/builder-protocol/BuildersV2.sol#265-278)
	- BuildersV2.usersData (contracts/builder-protocol/BuildersV2.sol#31)
	- BuildersV2.withdraw(bytes32,uint256) (contracts/builder-protocol/BuildersV2.sol#150-181)
	- userData.lastDeposit = uint128(block.timestamp) (contracts/builder-protocol/BuildersV2.sol#145)
	BuildersV2.usersData (contracts/builder-protocol/BuildersV2.sol#31) can be used in cross function reentrancies:
	- BuildersV2.deposit(bytes32,uint256) (contracts/builder-protocol/BuildersV2.sol#129-148)
	- BuildersV2.getCurrentUserMultiplier(bytes32,address) (contracts/builder-protocol/BuildersV2.sol#265-278)
	- BuildersV2.usersData (contracts/builder-protocol/BuildersV2.sol#31)
	- BuildersV2.withdraw(bytes32,uint256) (contracts/builder-protocol/BuildersV2.sol#150-181)
Reentrancy in BuildersV3.deposit(bytes32,uint256) (contracts/builder-protocol/BuildersV3.sol#143-162):
	External calls:
	- IERC20(depositToken).safeTransferFrom(_msgSender(),address(this),amount_) (contracts/builder-protocol/BuildersV3.sol#156)
	State variables written after the call(s):
	- _updatePoolData(builderPoolId_,deposited_,userData) (contracts/builder-protocol/BuildersV3.sol#158)
		- userData.deposited = newDeposited_ (contracts/builder-protocol/BuildersV3.sol#276)
		- userData.virtualDeposited = virtualDeposited_ (contracts/builder-protocol/BuildersV3.sol#277)
		- userData.claimLockStart = uint128(block.timestamp) (contracts/builder-protocol/BuildersV3.sol#278)
	BuildersV3.usersData (contracts/builder-protocol/BuildersV3.sol#32) can be used in cross function reentrancies:
	- BuildersV3._migrateUserStake(bytes32,address) (contracts/builder-protocol/BuildersV3.sol#447-457)
	- BuildersV3.deposit(bytes32,uint256) (contracts/builder-protocol/BuildersV3.sol#143-162)
	- BuildersV3.getCurrentUserMultiplier(bytes32,address) (contracts/builder-protocol/BuildersV3.sol#285-298)
	- BuildersV3.usersData (contracts/builder-protocol/BuildersV3.sol#32)
	- BuildersV3.withdraw(bytes32,uint256) (contracts/builder-protocol/BuildersV3.sol#164-198)
	- userData.lastDeposit = uint128(block.timestamp) (contracts/builder-protocol/BuildersV3.sol#159)
	BuildersV3.usersData (contracts/builder-protocol/BuildersV3.sol#32) can be used in cross function reentrancies:
	- BuildersV3._migrateUserStake(bytes32,address) (contracts/builder-protocol/BuildersV3.sol#447-457)
	- BuildersV3.deposit(bytes32,uint256) (contracts/builder-protocol/BuildersV3.sol#143-162)
	- BuildersV3.getCurrentUserMultiplier(bytes32,address) (contracts/builder-protocol/BuildersV3.sol#285-298)
	- BuildersV3.usersData (contracts/builder-protocol/BuildersV3.sol#32)
	- BuildersV3.withdraw(bytes32,uint256) (contracts/builder-protocol/BuildersV3.sol#164-198)
Reentrancy in Builders.deposit(bytes32,uint256) (contracts/builder-protocol/old/Builders.sol#129-148):
	External calls:
	- IERC20(depositToken).safeTransferFrom(_msgSender(),address(this),amount_) (contracts/builder-protocol/old/Builders.sol#142)
	State variables written after the call(s):
	- _updatePoolData(builderPoolId_,deposited_,userData) (contracts/builder-protocol/old/Builders.sol#144)
		- userData.deposited = newDeposited_ (contracts/builder-protocol/old/Builders.sol#256)
		- userData.virtualDeposited = virtualDeposited_ (contracts/builder-protocol/old/Builders.sol#257)
		- userData.claimLockStart = uint128(block.timestamp) (contracts/builder-protocol/old/Builders.sol#258)
	Builders.usersData (contracts/builder-protocol/old/Builders.sol#31) can be used in cross function reentrancies:
	- Builders.deposit(bytes32,uint256) (contracts/builder-protocol/old/Builders.sol#129-148)
	- Builders.getCurrentUserMultiplier(bytes32,address) (contracts/builder-protocol/old/Builders.sol#265-278)
	- Builders.usersData (contracts/builder-protocol/old/Builders.sol#31)
	- Builders.withdraw(bytes32,uint256) (contracts/builder-protocol/old/Builders.sol#150-181)
	- userData.lastDeposit = uint128(block.timestamp) (contracts/builder-protocol/old/Builders.sol#145)
	Builders.usersData (contracts/builder-protocol/old/Builders.sol#31) can be used in cross function reentrancies:
	- Builders.deposit(bytes32,uint256) (contracts/builder-protocol/old/Builders.sol#129-148)
	- Builders.getCurrentUserMultiplier(bytes32,address) (contracts/builder-protocol/old/Builders.sol#265-278)
	- Builders.usersData (contracts/builder-protocol/old/Builders.sol#31)
	- Builders.withdraw(bytes32,uint256) (contracts/builder-protocol/old/Builders.sol#150-181)
Reentrancy in DepositPool.migrate(uint256) (contracts/capital-protocol/DepositPool.sol#137-160):
	External calls:
	- IERC20(depositToken).transfer(distributor,remainder_) (contracts/capital-protocol/DepositPool.sol#153)
	- IDistributor(distributor).supply(rewardPoolIndex_,totalDepositedInPublicPools) (contracts/capital-protocol/DepositPool.sol#155)
	State variables written after the call(s):
	- isMigrationOver = true (contracts/capital-protocol/DepositPool.sol#157)
	DepositPool.isMigrationOver (contracts/capital-protocol/DepositPool.sol#68) can be used in cross function reentrancies:
	- DepositPool._claim(uint256,address,address) (contracts/capital-protocol/DepositPool.sol#513-574)
	- DepositPool._claimReferrerTier(uint256,address,address) (contracts/capital-protocol/DepositPool.sol#576-611)
	- DepositPool._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/DepositPool.sol#353-432)
	- DepositPool._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/DepositPool.sol#434-511)
	- DepositPool.isMigrationOver (contracts/capital-protocol/DepositPool.sol#68)
	- DepositPool.lockClaim(uint256,uint128) (contracts/capital-protocol/DepositPool.sol#307-351)
	- DepositPool.migrate(uint256) (contracts/capital-protocol/DepositPool.sol#137-160)
Reentrancy in L2MessageReceiver.retryMessage(uint16,bytes,uint64,bytes) (contracts/capital-protocol/old/L2MessageReceiver.sol#52-67):
	External calls:
	- _nonblockingLzReceive(senderChainId_,senderAndReceiverAddresses_,payload_) (contracts/capital-protocol/old/L2MessageReceiver.sol#62)
		- IMOROFT(rewardToken).mint(user_,amount_) (contracts/capital-protocol/old/L2MessageReceiver.sol#105)
	State variables written after the call(s):
	- delete failedMessages[senderChainId_][senderAndReceiverAddresses_][nonce_] (contracts/capital-protocol/old/L2MessageReceiver.sol#64)
	L2MessageReceiver.failedMessages (contracts/capital-protocol/old/L2MessageReceiver.sol#15) can be used in cross function reentrancies:
	- L2MessageReceiver._blockingLzReceive(uint16,bytes,uint64,bytes) (contracts/capital-protocol/old/L2MessageReceiver.sol#69-88)
	- L2MessageReceiver.failedMessages (contracts/capital-protocol/old/L2MessageReceiver.sol#15)
	- L2MessageReceiver.retryMessage(uint16,bytes,uint64,bytes) (contracts/capital-protocol/old/L2MessageReceiver.sol#52-67)
Reentrancy in L1SenderV2.setArbitrumBridgeConfig(IL1SenderV2.ArbitrumBridgeConfig) (contracts/capital-protocol/L1SenderV2.sol#132-152):
	External calls:
	- IERC20(stETH).approve(oldConfig_.wstETH,0) (contracts/capital-protocol/L1SenderV2.sol#139)
	- IERC20(oldConfig_.wstETH).approve(IGatewayRouter(oldConfig_.gateway).getGateway(oldConfig_.wstETH),0) (contracts/capital-protocol/L1SenderV2.sol#140)
	- IERC20(stETH).approve(newConfig_.wstETH,type()(uint256).max) (contracts/capital-protocol/L1SenderV2.sol#143)
	- IERC20(newConfig_.wstETH).approve(IGatewayRouter(newConfig_.gateway).getGateway(newConfig_.wstETH),type()(uint256).max) (contracts/capital-protocol/L1SenderV2.sol#144-147)
	State variables written after the call(s):
	- arbitrumBridgeConfig = newConfig_ (contracts/capital-protocol/L1SenderV2.sol#149)
	L1SenderV2.arbitrumBridgeConfig (contracts/capital-protocol/L1SenderV2.sol#27) can be used in cross function reentrancies:
	- L1SenderV2.arbitrumBridgeConfig (contracts/capital-protocol/L1SenderV2.sol#27)
	- L1SenderV2.sendWstETH(uint256,uint256,uint256) (contracts/capital-protocol/L1SenderV2.sol#154-183)
	- L1SenderV2.setArbitrumBridgeConfig(IL1SenderV2.ArbitrumBridgeConfig) (contracts/capital-protocol/L1SenderV2.sol#132-152)
Reentrancy in BuildersV3.setBuilderSubnets(address) (contracts/builder-protocol/BuildersV3.sol#408-419):
	External calls:
	- IERC20(depositToken).approve(builderSubnets,0) (contracts/builder-protocol/BuildersV3.sol#412)
	- IERC20(depositToken).approve(value_,type()(uint256).max) (contracts/builder-protocol/BuildersV3.sol#414)
	State variables written after the call(s):
	- builderSubnets = value_ (contracts/builder-protocol/BuildersV3.sol#416)
	BuildersV3.builderSubnets (contracts/builder-protocol/BuildersV3.sol#42) can be used in cross function reentrancies:
	- BuildersV3._migrateUserStake(bytes32,address) (contracts/builder-protocol/BuildersV3.sol#447-457)
	- BuildersV3.builderSubnets (contracts/builder-protocol/BuildersV3.sol#42)
	- BuildersV3.setBuilderSubnets(address) (contracts/builder-protocol/BuildersV3.sol#408-419)
Reentrancy in L1Sender.setDepositTokenConfig(IL1Sender.DepositTokenConfig) (contracts/capital-protocol/old/L1Sender.sol#56-65):
	External calls:
	- _replaceDepositToken(oldConfig.token,newConfig_.token) (contracts/capital-protocol/old/L1Sender.sol#61)
		- IERC20(unwrappedDepositToken).approve(oldToken_,0) (contracts/capital-protocol/old/L1Sender.sol#72)
		- unwrappedToken_ = IWStETH(newToken_).stETH() (contracts/capital-protocol/old/L1Sender.sol#77)
		- IERC20(unwrappedToken_).approve(newToken_,type()(uint256).max) (contracts/capital-protocol/old/L1Sender.sol#79)
	- _replaceDepositTokenGateway(oldConfig.gateway,newConfig_.gateway,oldConfig.token,newConfig_.token) (contracts/capital-protocol/old/L1Sender.sol#62)
		- IERC20(oldToken_).approve(IGatewayRouter(oldGateway_).getGateway(oldToken_),0) (contracts/capital-protocol/old/L1Sender.sol#94)
		- IERC20(newToken_).approve(IGatewayRouter(newGateway_).getGateway(newToken_),type()(uint256).max) (contracts/capital-protocol/old/L1Sender.sol#98)
	State variables written after the call(s):
	- depositTokenConfig = newConfig_ (contracts/capital-protocol/old/L1Sender.sol#64)
	L1Sender.depositTokenConfig (contracts/capital-protocol/old/L1Sender.sol#19) can be used in cross function reentrancies:
	- L1Sender.depositTokenConfig (contracts/capital-protocol/old/L1Sender.sol#19)
	- L1Sender.sendDepositToken(uint256,uint256,uint256) (contracts/capital-protocol/old/L1Sender.sol#102-125)
	- L1Sender.setDepositTokenConfig(IL1Sender.DepositTokenConfig) (contracts/capital-protocol/old/L1Sender.sol#56-65)
Reentrancy in DepositPool.setDistributor(address) (contracts/capital-protocol/DepositPool.sol#101-112):
	External calls:
	- IERC20(depositToken).approve(distributor,0) (contracts/capital-protocol/DepositPool.sol#105)
	- IERC20(depositToken).approve(value_,type()(uint256).max) (contracts/capital-protocol/DepositPool.sol#107)
	State variables written after the call(s):
	- distributor = value_ (contracts/capital-protocol/DepositPool.sol#109)
	DepositPool.distributor (contracts/capital-protocol/DepositPool.sol#71) can be used in cross function reentrancies:
	- DepositPool._claim(uint256,address,address) (contracts/capital-protocol/DepositPool.sol#513-574)
	- DepositPool._claimReferrerTier(uint256,address,address) (contracts/capital-protocol/DepositPool.sol#576-611)
	- DepositPool._getCurrentPoolRate(uint256) (contracts/capital-protocol/DepositPool.sol#704-717)
	- DepositPool._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/DepositPool.sol#353-432)
	- DepositPool._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/DepositPool.sol#434-511)
	- DepositPool.distributor (contracts/capital-protocol/DepositPool.sol#71)
	- DepositPool.editReferrerTiers(uint256,IReferrer.ReferrerTier[]) (contracts/capital-protocol/DepositPool.sol#162-186)
	- DepositPool.getCurrentUserMultiplier(uint256,address) (contracts/capital-protocol/DepositPool.sol#723-731)
	- DepositPool.getLatestReferrerReward(uint256,address) (contracts/capital-protocol/DepositPool.sol#686-694)
	- DepositPool.getLatestUserReward(uint256,address) (contracts/capital-protocol/DepositPool.sol#675-684)
	- DepositPool.getReferrerMultiplier(uint256,address) (contracts/capital-protocol/DepositPool.sol#733-744)
	- DepositPool.lockClaim(uint256,uint128) (contracts/capital-protocol/DepositPool.sol#307-351)
	- DepositPool.manageUsersInPrivateRewardPool(uint256,address[],uint256[],uint128[],address[]) (contracts/capital-protocol/DepositPool.sol#188-225)
	- DepositPool.migrate(uint256) (contracts/capital-protocol/DepositPool.sol#137-160)
	- DepositPool.setClaimReceiver(uint256,address) (contracts/capital-protocol/DepositPool.sol#246-252)
	- DepositPool.setClaimSender(uint256,address[],bool[]) (contracts/capital-protocol/DepositPool.sol#231-244)
	- DepositPool.setDistributor(address) (contracts/capital-protocol/DepositPool.sol#101-112)
	- DepositPool.stake(uint256,uint256,uint128,address) (contracts/capital-protocol/DepositPool.sol#254-266)
	- DepositPool.withdraw(uint256,uint256) (contracts/capital-protocol/DepositPool.sol#268-281)
Reentrancy in BuilderSubnets.stake(bytes32,address,uint256) (contracts/builder-protocol/BuilderSubnets.sol#254-276):
	External calls:
	- IERC20(token).safeTransferFrom(_msgSender(),address(this),amount_) (contracts/builder-protocol/BuilderSubnets.sol#270)
	State variables written after the call(s):
	- _updateStorage(subnetId_,stakerAddress_,staked_) (contracts/builder-protocol/BuilderSubnets.sol#272)
		- staker.staked = newStaked_ (contracts/builder-protocol/BuilderSubnets.sol#378)
		- staker.rate = rate_ (contracts/builder-protocol/BuilderSubnets.sol#379)
		- staker.pendingRewards = pendingRewards_ (contracts/builder-protocol/BuilderSubnets.sol#380)
	BuilderSubnets.stakers (contracts/builder-protocol/BuilderSubnets.sol#62) can be used in cross function reentrancies:
	- BuilderSubnets._updateStorage(bytes32,address,uint256) (contracts/builder-protocol/BuilderSubnets.sol#359-381)
	- BuilderSubnets.claim(bytes32,address) (contracts/builder-protocol/BuilderSubnets.sol#308-339)
	- BuilderSubnets.getStakerRewards(bytes32,address) (contracts/builder-protocol/BuilderSubnets.sol#387-391)
	- BuilderSubnets.stake(bytes32,address,uint256) (contracts/builder-protocol/BuilderSubnets.sol#254-276)
	- BuilderSubnets.stakers (contracts/builder-protocol/BuilderSubnets.sol#62)
	- BuilderSubnets.withdraw(bytes32,uint256) (contracts/builder-protocol/BuilderSubnets.sol#278-306)
	- staker.lastStake = uint128(block.timestamp) (contracts/builder-protocol/BuilderSubnets.sol#273)
	BuilderSubnets.stakers (contracts/builder-protocol/BuilderSubnets.sol#62) can be used in cross function reentrancies:
	- BuilderSubnets._updateStorage(bytes32,address,uint256) (contracts/builder-protocol/BuilderSubnets.sol#359-381)
	- BuilderSubnets.claim(bytes32,address) (contracts/builder-protocol/BuilderSubnets.sol#308-339)
	- BuilderSubnets.getStakerRewards(bytes32,address) (contracts/builder-protocol/BuilderSubnets.sol#387-391)
	- BuilderSubnets.stake(bytes32,address,uint256) (contracts/builder-protocol/BuilderSubnets.sol#254-276)
	- BuilderSubnets.stakers (contracts/builder-protocol/BuilderSubnets.sol#62)
	- BuilderSubnets.withdraw(bytes32,uint256) (contracts/builder-protocol/BuilderSubnets.sol#278-306)
Reference: https://github.com/crytic/slither/wiki/Detector-Documentation#reentrancy-vulnerabilities-1
INFO:Detectors:
DistributionExt.getTotalRewards().amount_ (contracts/extensions/DistributionExt.sol#41) is a local variable never initialized
DistributionV6.editReferrerTiers(uint256,IReferrer.ReferrerTier[]).lastAmount_ (contracts/capital-protocol/DistributionV6.sol#130) is a local variable never initialized
DistributionV5.editReferrerTiers(uint256,IReferrer.ReferrerTier[]).lastMultiplier_ (contracts/capital-protocol/old/DistributionV5.sol#125) is a local variable never initialized
DistributionV6.editReferrerTiers(uint256,IReferrer.ReferrerTier[]).lastMultiplier_ (contracts/capital-protocol/DistributionV6.sol#131) is a local variable never initialized
L1SenderV2.swapExactInputMultihop(address[],uint24[],uint256,uint256,uint256).path_ (contracts/capital-protocol/L1SenderV2.sol#212) is a local variable never initialized
DistributionV5.editReferrerTiers(uint256,IReferrer.ReferrerTier[]).lastAmount_ (contracts/capital-protocol/old/DistributionV5.sol#124) is a local variable never initialized
Distributor.distributeRewards(uint256).yieldToken_ (contracts/capital-protocol/Distributor.sol#375) is a local variable never initialized
DepositPool.editReferrerTiers(uint256,IReferrer.ReferrerTier[]).lastAmount_ (contracts/capital-protocol/DepositPool.sol#168) is a local variable never initialized
DepositPool.editReferrerTiers(uint256,IReferrer.ReferrerTier[]).lastMultiplier_ (contracts/capital-protocol/DepositPool.sol#169) is a local variable never initialized
Reference: https://github.com/crytic/slither/wiki/Detector-Documentation#uninitialized-local-variables
INFO:Detectors:
OFTCore._buildMsgAndOptions(SendParam,uint256) (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oft/OFTCore.sol#191-212) ignores return value by IOAppMsgInspector(msgInspector).inspect(message,options) (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oft/OFTCore.sol#211)
BuildersV3.setBuilderSubnets(address) (contracts/builder-protocol/BuildersV3.sol#408-419) ignores return value by IERC20(depositToken).approve(builderSubnets,0) (contracts/builder-protocol/BuildersV3.sol#412)
BuildersV3.setBuilderSubnets(address) (contracts/builder-protocol/BuildersV3.sol#408-419) ignores return value by IERC20(depositToken).approve(value_,type()(uint256).max) (contracts/builder-protocol/BuildersV3.sol#414)
ChainLinkDataConsumer.getChainLinkDataFeedLatestAnswer(bytes32) (contracts/capital-protocol/ChainLinkDataConsumer.sol#78-107) ignores return value by (answer_,updatedAt_) = aggregator_.latestRoundData() (contracts/capital-protocol/ChainLinkDataConsumer.sol#86-103)
DepositPool.setDistributor(address) (contracts/capital-protocol/DepositPool.sol#101-112) ignores return value by IERC20(depositToken).approve(distributor,0) (contracts/capital-protocol/DepositPool.sol#105)
DepositPool.setDistributor(address) (contracts/capital-protocol/DepositPool.sol#101-112) ignores return value by IERC20(depositToken).approve(value_,type()(uint256).max) (contracts/capital-protocol/DepositPool.sol#107)
DepositPool._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/DepositPool.sol#434-511) ignores return value by IDistributor(distributor).withdraw(rewardPoolIndex_,amount_) (contracts/capital-protocol/DepositPool.sol#506)
Distributor.addDepositPool(uint256,address,address,string,IDistributor.Strategy) (contracts/capital-protocol/Distributor.sol#192-248) ignores return value by (aToken_,None,None) = IPoolDataProvider(aavePoolDataProvider).getReserveTokensAddresses(token_) (contracts/capital-protocol/Distributor.sol#230)
Distributor.addDepositPool(uint256,address,address,string,IDistributor.Strategy) (contracts/capital-protocol/Distributor.sol#192-248) ignores return value by IERC20(aToken_).approve(aavePool,type()(uint256).max) (contracts/capital-protocol/Distributor.sol#233)
Distributor.withdraw(uint256,uint256) (contracts/capital-protocol/Distributor.sol#304-328) ignores return value by IPool(aavePool).withdraw(depositPool.token,amount_,depositPoolAddress_) (contracts/capital-protocol/Distributor.sol#322)
Distributor._withdrawYield(uint256,address) (contracts/capital-protocol/Distributor.sol#479-492) ignores return value by IPool(aavePool).withdraw(depositPool.token,yield_,l1Sender) (contracts/capital-protocol/Distributor.sol#486)
L1SenderV2.setArbitrumBridgeConfig(IL1SenderV2.ArbitrumBridgeConfig) (contracts/capital-protocol/L1SenderV2.sol#132-152) ignores return value by IERC20(stETH).approve(oldConfig_.wstETH,0) (contracts/capital-protocol/L1SenderV2.sol#139)
L1SenderV2.setArbitrumBridgeConfig(IL1SenderV2.ArbitrumBridgeConfig) (contracts/capital-protocol/L1SenderV2.sol#132-152) ignores return value by IERC20(oldConfig_.wstETH).approve(IGatewayRouter(oldConfig_.gateway).getGateway(oldConfig_.wstETH),0) (contracts/capital-protocol/L1SenderV2.sol#140)
L1SenderV2.setArbitrumBridgeConfig(IL1SenderV2.ArbitrumBridgeConfig) (contracts/capital-protocol/L1SenderV2.sol#132-152) ignores return value by IERC20(stETH).approve(newConfig_.wstETH,type()(uint256).max) (contracts/capital-protocol/L1SenderV2.sol#143)
L1SenderV2.setArbitrumBridgeConfig(IL1SenderV2.ArbitrumBridgeConfig) (contracts/capital-protocol/L1SenderV2.sol#132-152) ignores return value by IERC20(newConfig_.wstETH).approve(IGatewayRouter(newConfig_.gateway).getGateway(newConfig_.wstETH),type()(uint256).max) (contracts/capital-protocol/L1SenderV2.sol#144-147)
L1SenderV2.sendWstETH(uint256,uint256,uint256) (contracts/capital-protocol/L1SenderV2.sol#154-183) ignores return value by IWStETH(config_.wstETH).wrap(stETHBalance_) (contracts/capital-protocol/L1SenderV2.sol#164)
L1Sender._replaceDepositToken(address,address) (contracts/capital-protocol/old/L1Sender.sol#67-83) ignores return value by IERC20(unwrappedDepositToken).approve(oldToken_,0) (contracts/capital-protocol/old/L1Sender.sol#72)
L1Sender._replaceDepositToken(address,address) (contracts/capital-protocol/old/L1Sender.sol#67-83) ignores return value by IERC20(unwrappedToken_).approve(newToken_,type()(uint256).max) (contracts/capital-protocol/old/L1Sender.sol#79)
L1Sender._replaceDepositTokenGateway(address,address,address,address) (contracts/capital-protocol/old/L1Sender.sol#85-100) ignores return value by IERC20(oldToken_).approve(IGatewayRouter(oldGateway_).getGateway(oldToken_),0) (contracts/capital-protocol/old/L1Sender.sol#94)
L1Sender._replaceDepositTokenGateway(address,address,address,address) (contracts/capital-protocol/old/L1Sender.sol#85-100) ignores return value by IERC20(newToken_).approve(IGatewayRouter(newGateway_).getGateway(newToken_),type()(uint256).max) (contracts/capital-protocol/old/L1Sender.sol#98)
L2TokenReceiver.increaseLiquidityCurrentRange(uint256,uint256,uint256,uint256,uint256) (contracts/capital-protocol/old/L2TokenReceiver.sol#82-124) ignores return value by (None,None,token0_,None,None,None,None,None,None,None,None,None) = INonfungiblePositionManager(nonfungiblePositionManager).positions(tokenId_) (contracts/capital-protocol/old/L2TokenReceiver.sol#94-96)
Reference: https://github.com/crytic/slither/wiki/Detector-Documentation#unused-return
INFO:Detectors:
L1Sender.setDistribution(address) (contracts/capital-protocol/old/L1Sender.sol#48-50) should emit an event for: 
	- distribution = distribution_ (contracts/capital-protocol/old/L1Sender.sol#49) 
Reference: https://github.com/crytic/slither/wiki/Detector-Documentation#missing-events-access-control
INFO:Detectors:
ChainLinkDataConsumer.setAllowedPriceUpdateDelay(uint64) (contracts/capital-protocol/ChainLinkDataConsumer.sol#45-47) should emit an event for: 
	- allowedPriceUpdateDelay = allowedPriceUpdateDelay_ (contracts/capital-protocol/ChainLinkDataConsumer.sol#46) 
Reference: https://github.com/crytic/slither/wiki/Detector-Documentation#missing-events-arithmetic
INFO:Detectors:
OFTCore.setMsgInspector(address)._msgInspector (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oft/OFTCore.sol#80) lacks a zero-check on :
		- msgInspector = _msgInspector (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oft/OFTCore.sol#81)
OAppPreCrimeSimulator.setPreCrime(address)._preCrime (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/precrime/OAppPreCrimeSimulator.sol#32) lacks a zero-check on :
		- preCrime = _preCrime (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/precrime/OAppPreCrimeSimulator.sol#33)
BuilderSubnets.BuilderSubnets_init(address,address,address,uint256,address).token_ (contracts/builder-protocol/BuilderSubnets.sol#83) lacks a zero-check on :
		- token = token_ (contracts/builder-protocol/BuilderSubnets.sol#96)
BuildersTreasury.BuildersTreasury_init(address,address).rewardToken_ (contracts/builder-protocol/BuildersTreasury.sol#28) lacks a zero-check on :
		- rewardToken = rewardToken_ (contracts/builder-protocol/BuildersTreasury.sol#31)
BuildersV2.BuildersV2_init(address,address,address,uint128,uint256).depositToken_ (contracts/builder-protocol/BuildersV2.sol#46) lacks a zero-check on :
		- depositToken = depositToken_ (contracts/builder-protocol/BuildersV2.sol#59)
BuildersV3.BuildersV3_init(address,address,address,uint128,uint256).depositToken_ (contracts/builder-protocol/BuildersV3.sol#60) lacks a zero-check on :
		- depositToken = depositToken_ (contracts/builder-protocol/BuildersV3.sol#73)
BuildersV3.setMigrationOwner(address).value_ (contracts/builder-protocol/BuildersV3.sol#402) lacks a zero-check on :
		- migrationOwner = value_ (contracts/builder-protocol/BuildersV3.sol#403)
Builders.Builders_init(address,address,address,uint128,uint256).depositToken_ (contracts/builder-protocol/old/Builders.sol#46) lacks a zero-check on :
		- depositToken = depositToken_ (contracts/builder-protocol/old/Builders.sol#59)
DepositPool.DepositPool_init(address,address).depositToken_ (contracts/capital-protocol/DepositPool.sol#85) lacks a zero-check on :
		- depositToken = depositToken_ (contracts/capital-protocol/DepositPool.sol#89)
DistributionV6.Distribution_init(address,address,IDistributionV5.Pool[]).depositToken_ (contracts/capital-protocol/DistributionV6.sol#75) lacks a zero-check on :
		- depositToken = depositToken_ (contracts/capital-protocol/DistributionV6.sol#86)
DistributionV6.Distribution_init(address,address,IDistributionV5.Pool[]).l1Sender_ (contracts/capital-protocol/DistributionV6.sol#76) lacks a zero-check on :
		- l1Sender = l1Sender_ (contracts/capital-protocol/DistributionV6.sol#87)
L2TokenReceiverV2.L2TokenReceiver__init(address,address,IL2TokenReceiverV2.SwapParams).router_ (contracts/capital-protocol/L2TokenReceiverV2.sol#28) lacks a zero-check on :
		- router = router_ (contracts/capital-protocol/L2TokenReceiverV2.sol#36)
L2TokenReceiverV2.L2TokenReceiver__init(address,address,IL2TokenReceiverV2.SwapParams).nonfungiblePositionManager_ (contracts/capital-protocol/L2TokenReceiverV2.sol#29) lacks a zero-check on :
		- nonfungiblePositionManager = nonfungiblePositionManager_ (contracts/capital-protocol/L2TokenReceiverV2.sol#37)
Distribution.Distribution_init(address,address,IDistribution.Pool[]).depositToken_ (contracts/capital-protocol/old/Distribution.sol#55) lacks a zero-check on :
		- depositToken = depositToken_ (contracts/capital-protocol/old/Distribution.sol#66)
Distribution.Distribution_init(address,address,IDistribution.Pool[]).l1Sender_ (contracts/capital-protocol/old/Distribution.sol#56) lacks a zero-check on :
		- l1Sender = l1Sender_ (contracts/capital-protocol/old/Distribution.sol#67)
DistributionV2.Distribution_init(address,address,IDistributionV2.Pool[]).depositToken_ (contracts/capital-protocol/old/DistributionV2.sol#59) lacks a zero-check on :
		- depositToken = depositToken_ (contracts/capital-protocol/old/DistributionV2.sol#70)
DistributionV2.Distribution_init(address,address,IDistributionV2.Pool[]).l1Sender_ (contracts/capital-protocol/old/DistributionV2.sol#60) lacks a zero-check on :
		- l1Sender = l1Sender_ (contracts/capital-protocol/old/DistributionV2.sol#71)
DistributionV3.Distribution_init(address,address,IDistributionV3.Pool[]).depositToken_ (contracts/capital-protocol/old/DistributionV3.sol#59) lacks a zero-check on :
		- depositToken = depositToken_ (contracts/capital-protocol/old/DistributionV3.sol#70)
DistributionV3.Distribution_init(address,address,IDistributionV3.Pool[]).l1Sender_ (contracts/capital-protocol/old/DistributionV3.sol#60) lacks a zero-check on :
		- l1Sender = l1Sender_ (contracts/capital-protocol/old/DistributionV3.sol#71)
DistributionV4.Distribution_init(address,address,IDistributionV4.Pool[]).depositToken_ (contracts/capital-protocol/old/DistributionV4.sol#62) lacks a zero-check on :
		- depositToken = depositToken_ (contracts/capital-protocol/old/DistributionV4.sol#73)
DistributionV4.Distribution_init(address,address,IDistributionV4.Pool[]).l1Sender_ (contracts/capital-protocol/old/DistributionV4.sol#63) lacks a zero-check on :
		- l1Sender = l1Sender_ (contracts/capital-protocol/old/DistributionV4.sol#74)
DistributionV5.Distribution_init(address,address,IDistributionV5.Pool[]).depositToken_ (contracts/capital-protocol/old/DistributionV5.sol#69) lacks a zero-check on :
		- depositToken = depositToken_ (contracts/capital-protocol/old/DistributionV5.sol#80)
DistributionV5.Distribution_init(address,address,IDistributionV5.Pool[]).l1Sender_ (contracts/capital-protocol/old/DistributionV5.sol#70) lacks a zero-check on :
		- l1Sender = l1Sender_ (contracts/capital-protocol/old/DistributionV5.sol#81)
L1Sender.setDistribution(address).distribution_ (contracts/capital-protocol/old/L1Sender.sol#48) lacks a zero-check on :
		- distribution = distribution_ (contracts/capital-protocol/old/L1Sender.sol#49)
L2MessageReceiver.setParams(address,IL2MessageReceiver.Config).rewardToken_ (contracts/capital-protocol/old/L2MessageReceiver.sol#26) lacks a zero-check on :
		- rewardToken = rewardToken_ (contracts/capital-protocol/old/L2MessageReceiver.sol#27)
L2TokenReceiver.L2TokenReceiver__init(address,address,IL2TokenReceiver.SwapParams).router_ (contracts/capital-protocol/old/L2TokenReceiver.sol#24) lacks a zero-check on :
		- router = router_ (contracts/capital-protocol/old/L2TokenReceiver.sol#31)
L2TokenReceiver.L2TokenReceiver__init(address,address,IL2TokenReceiver.SwapParams).nonfungiblePositionManager_ (contracts/capital-protocol/old/L2TokenReceiver.sol#25) lacks a zero-check on :
		- nonfungiblePositionManager = nonfungiblePositionManager_ (contracts/capital-protocol/old/L2TokenReceiver.sol#32)
Reference: https://github.com/crytic/slither/wiki/Detector-Documentation#missing-zero-address-validation
INFO:Detectors:
OAppPreCrimeSimulator.lzReceiveAndRevert(InboundPacket[]) (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/precrime/OAppPreCrimeSimulator.sol#45-70) has external calls inside a loop: this.lzReceiveSimulate{value: packet.value}(packet.origin,packet.guid,packet.message,packet.executor,packet.extraData) (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/precrime/OAppPreCrimeSimulator.sol#59-65)
BuildersV3._migrateUserStake(bytes32,address) (contracts/builder-protocol/BuildersV3.sol#447-457) has external calls inside a loop: IBuilderSubnets(builderSubnets).stake(builderPoolId_,user_,userData.deposited) (contracts/builder-protocol/BuildersV3.sol#451)
	Calls stack containing the loop:
		BuildersV3.migrateUsersStake(bytes32[],address[])
ChainLinkDataConsumer.getChainLinkDataFeedLatestAnswer(bytes32) (contracts/capital-protocol/ChainLinkDataConsumer.sol#78-107) has external calls inside a loop: (answer_,updatedAt_) = aggregator_.latestRoundData() (contracts/capital-protocol/ChainLinkDataConsumer.sol#86-103)
ChainLinkDataConsumer.getChainLinkDataFeedLatestAnswer(bytes32) (contracts/capital-protocol/ChainLinkDataConsumer.sol#78-107) has external calls inside a loop: baseDecimals_ = aggregator_.decimals() (contracts/capital-protocol/ChainLinkDataConsumer.sol#97)
ChainLinkDataConsumer.getChainLinkDataFeedLatestAnswer(bytes32) (contracts/capital-protocol/ChainLinkDataConsumer.sol#78-107) has external calls inside a loop: res_ = (res_ * uint256(answer_)) / (10 ** aggregator_.decimals()) (contracts/capital-protocol/ChainLinkDataConsumer.sol#99)
DepositPool._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/DepositPool.sol#353-432) has external calls inside a loop: IRewardPool(IDistributor(distributor).rewardPool()).isRewardPoolPublic(rewardPoolIndex_) (contracts/capital-protocol/DepositPool.sol#376)
	Calls stack containing the loop:
		DepositPool.manageUsersInPrivateRewardPool(uint256,address[],uint256[],uint128[],address[])
DepositPool._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/DepositPool.sol#353-432) has external calls inside a loop: balanceBefore_ = IERC20(depositToken).balanceOf(address(this)) (contracts/capital-protocol/DepositPool.sol#380)
	Calls stack containing the loop:
		DepositPool.manageUsersInPrivateRewardPool(uint256,address[],uint256[],uint128[],address[])
DepositPool._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/DepositPool.sol#353-432) has external calls inside a loop: balanceAfter_ = IERC20(depositToken).balanceOf(address(this)) (contracts/capital-protocol/DepositPool.sol#382)
	Calls stack containing the loop:
		DepositPool.manageUsersInPrivateRewardPool(uint256,address[],uint256[],uint128[],address[])
DepositPool._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/DepositPool.sol#353-432) has external calls inside a loop: IDistributor(distributor).supply(rewardPoolIndex_,amount_) (contracts/capital-protocol/DepositPool.sol#386)
	Calls stack containing the loop:
		DepositPool.manageUsersInPrivateRewardPool(uint256,address[],uint256[],uint128[],address[])
DepositPool._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/DepositPool.sol#434-511) has external calls inside a loop: IRewardPool(IDistributor(distributor).rewardPool()).isRewardPoolPublic(rewardPoolIndex_) (contracts/capital-protocol/DepositPool.sol#449)
	Calls stack containing the loop:
		DepositPool.manageUsersInPrivateRewardPool(uint256,address[],uint256[],uint128[],address[])
DepositPool._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/DepositPool.sol#434-511) has external calls inside a loop: IRewardPool(IDistributor(distributor).rewardPool()).isRewardPoolPublic(rewardPoolIndex_) (contracts/capital-protocol/DepositPool.sol#503)
	Calls stack containing the loop:
		DepositPool.manageUsersInPrivateRewardPool(uint256,address[],uint256[],uint128[],address[])
DepositPool._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/DepositPool.sol#434-511) has external calls inside a loop: IDistributor(distributor).withdraw(rewardPoolIndex_,amount_) (contracts/capital-protocol/DepositPool.sol#506)
	Calls stack containing the loop:
		DepositPool.manageUsersInPrivateRewardPool(uint256,address[],uint256[],uint128[],address[])
DistributionV6._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/DistributionV6.sol#405-477) has external calls inside a loop: balanceBefore_ = IERC20(depositToken).balanceOf(address(this)) (contracts/capital-protocol/DistributionV6.sol#430)
	Calls stack containing the loop:
		DistributionV6.manageUsersInPrivatePool(uint256,address[],uint256[],uint128[],address[])
DistributionV6._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/DistributionV6.sol#405-477) has external calls inside a loop: balanceAfter_ = IERC20(depositToken).balanceOf(address(this)) (contracts/capital-protocol/DistributionV6.sol#432)
	Calls stack containing the loop:
		DistributionV6.manageUsersInPrivatePool(uint256,address[],uint256[],uint128[],address[])
DistributionV6._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/DistributionV6.sol#479-557) has external calls inside a loop: depositTokenContractBalance_ = IERC20(depositToken).balanceOf(address(this)) (contracts/capital-protocol/DistributionV6.sol#500)
	Calls stack containing the loop:
		DistributionV6.manageUsersInPrivatePool(uint256,address[],uint256[],uint128[],address[])
Distributor.updateDepositTokensPrices(uint256) (contracts/capital-protocol/Distributor.sol#258-279) has external calls inside a loop: chainLinkPathId_ = chainLinkDataConsumer_.getPathId(depositPool.chainLinkPath) (contracts/capital-protocol/Distributor.sol#271)
	Calls stack containing the loop:
		Distributor.addDepositPool(uint256,address,address,string,IDistributor.Strategy)
Distributor.updateDepositTokensPrices(uint256) (contracts/capital-protocol/Distributor.sol#258-279) has external calls inside a loop: price_ = chainLinkDataConsumer_.getChainLinkDataFeedLatestAnswer(chainLinkPathId_) (contracts/capital-protocol/Distributor.sol#272)
	Calls stack containing the loop:
		Distributor.addDepositPool(uint256,address,address,string,IDistributor.Strategy)
Distributor.updateDepositTokensPrices(uint256) (contracts/capital-protocol/Distributor.sol#258-279) has external calls inside a loop: chainLinkPathId_ = chainLinkDataConsumer_.getPathId(depositPool.chainLinkPath) (contracts/capital-protocol/Distributor.sol#271)
Distributor.updateDepositTokensPrices(uint256) (contracts/capital-protocol/Distributor.sol#258-279) has external calls inside a loop: price_ = chainLinkDataConsumer_.getChainLinkDataFeedLatestAnswer(chainLinkPathId_) (contracts/capital-protocol/Distributor.sol#272)
Distributor.updateDepositTokensPrices(uint256) (contracts/capital-protocol/Distributor.sol#258-279) has external calls inside a loop: chainLinkPathId_ = chainLinkDataConsumer_.getPathId(depositPool.chainLinkPath) (contracts/capital-protocol/Distributor.sol#271)
	Calls stack containing the loop:
		Distributor.supply(uint256,uint256)
		Distributor.distributeRewards(uint256)
Distributor.updateDepositTokensPrices(uint256) (contracts/capital-protocol/Distributor.sol#258-279) has external calls inside a loop: price_ = chainLinkDataConsumer_.getChainLinkDataFeedLatestAnswer(chainLinkPathId_) (contracts/capital-protocol/Distributor.sol#272)
	Calls stack containing the loop:
		Distributor.supply(uint256,uint256)
		Distributor.distributeRewards(uint256)
Distributor.distributeRewards(uint256) (contracts/capital-protocol/Distributor.sol#330-410) has external calls inside a loop: balance_ = IERC20(yieldToken_).balanceOf(address(this)) (contracts/capital-protocol/Distributor.sol#384)
	Calls stack containing the loop:
		Distributor.supply(uint256,uint256)
Distributor.distributeRewards(uint256) (contracts/capital-protocol/Distributor.sol#330-410) has external calls inside a loop: decimals_ = IERC20Metadata(yieldToken_).decimals() (contracts/capital-protocol/Distributor.sol#385)
	Calls stack containing the loop:
		Distributor.supply(uint256,uint256)
Distributor.updateDepositTokensPrices(uint256) (contracts/capital-protocol/Distributor.sol#258-279) has external calls inside a loop: chainLinkPathId_ = chainLinkDataConsumer_.getPathId(depositPool.chainLinkPath) (contracts/capital-protocol/Distributor.sol#271)
	Calls stack containing the loop:
		Distributor.withdraw(uint256,uint256)
		Distributor.distributeRewards(uint256)
Distributor.updateDepositTokensPrices(uint256) (contracts/capital-protocol/Distributor.sol#258-279) has external calls inside a loop: price_ = chainLinkDataConsumer_.getChainLinkDataFeedLatestAnswer(chainLinkPathId_) (contracts/capital-protocol/Distributor.sol#272)
	Calls stack containing the loop:
		Distributor.withdraw(uint256,uint256)
		Distributor.distributeRewards(uint256)
Distributor.distributeRewards(uint256) (contracts/capital-protocol/Distributor.sol#330-410) has external calls inside a loop: balance_ = IERC20(yieldToken_).balanceOf(address(this)) (contracts/capital-protocol/Distributor.sol#384)
	Calls stack containing the loop:
		Distributor.withdraw(uint256,uint256)
Distributor.distributeRewards(uint256) (contracts/capital-protocol/Distributor.sol#330-410) has external calls inside a loop: decimals_ = IERC20Metadata(yieldToken_).decimals() (contracts/capital-protocol/Distributor.sol#385)
	Calls stack containing the loop:
		Distributor.withdraw(uint256,uint256)
Distributor.updateDepositTokensPrices(uint256) (contracts/capital-protocol/Distributor.sol#258-279) has external calls inside a loop: chainLinkPathId_ = chainLinkDataConsumer_.getPathId(depositPool.chainLinkPath) (contracts/capital-protocol/Distributor.sol#271)
	Calls stack containing the loop:
		Distributor.distributeRewards(uint256)
Distributor.updateDepositTokensPrices(uint256) (contracts/capital-protocol/Distributor.sol#258-279) has external calls inside a loop: price_ = chainLinkDataConsumer_.getChainLinkDataFeedLatestAnswer(chainLinkPathId_) (contracts/capital-protocol/Distributor.sol#272)
	Calls stack containing the loop:
		Distributor.distributeRewards(uint256)
Distributor.distributeRewards(uint256) (contracts/capital-protocol/Distributor.sol#330-410) has external calls inside a loop: balance_ = IERC20(yieldToken_).balanceOf(address(this)) (contracts/capital-protocol/Distributor.sol#384)
Distributor.distributeRewards(uint256) (contracts/capital-protocol/Distributor.sol#330-410) has external calls inside a loop: decimals_ = IERC20Metadata(yieldToken_).decimals() (contracts/capital-protocol/Distributor.sol#385)
Distributor.updateDepositTokensPrices(uint256) (contracts/capital-protocol/Distributor.sol#258-279) has external calls inside a loop: chainLinkPathId_ = chainLinkDataConsumer_.getPathId(depositPool.chainLinkPath) (contracts/capital-protocol/Distributor.sol#271)
	Calls stack containing the loop:
		Distributor.withdrawYield(uint256,address)
		Distributor.distributeRewards(uint256)
Distributor.updateDepositTokensPrices(uint256) (contracts/capital-protocol/Distributor.sol#258-279) has external calls inside a loop: price_ = chainLinkDataConsumer_.getChainLinkDataFeedLatestAnswer(chainLinkPathId_) (contracts/capital-protocol/Distributor.sol#272)
	Calls stack containing the loop:
		Distributor.withdrawYield(uint256,address)
		Distributor.distributeRewards(uint256)
Distributor.distributeRewards(uint256) (contracts/capital-protocol/Distributor.sol#330-410) has external calls inside a loop: balance_ = IERC20(yieldToken_).balanceOf(address(this)) (contracts/capital-protocol/Distributor.sol#384)
	Calls stack containing the loop:
		Distributor.withdrawYield(uint256,address)
Distributor.distributeRewards(uint256) (contracts/capital-protocol/Distributor.sol#330-410) has external calls inside a loop: decimals_ = IERC20Metadata(yieldToken_).decimals() (contracts/capital-protocol/Distributor.sol#385)
	Calls stack containing the loop:
		Distributor.withdrawYield(uint256,address)
Distribution._stake(address,uint256,uint256,uint256) (contracts/capital-protocol/old/Distribution.sol#196-229) has external calls inside a loop: balanceBefore_ = IERC20(depositToken).balanceOf(address(this)) (contracts/capital-protocol/old/Distribution.sol#205)
	Calls stack containing the loop:
		Distribution.manageUsersInPrivatePool(uint256,address[],uint256[])
Distribution._stake(address,uint256,uint256,uint256) (contracts/capital-protocol/old/Distribution.sol#196-229) has external calls inside a loop: balanceAfter_ = IERC20(depositToken).balanceOf(address(this)) (contracts/capital-protocol/old/Distribution.sol#207)
	Calls stack containing the loop:
		Distribution.manageUsersInPrivatePool(uint256,address[],uint256[])
Distribution._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/Distribution.sol#231-284) has external calls inside a loop: depositTokenContractBalance_ = IERC20(depositToken).balanceOf(address(this)) (contracts/capital-protocol/old/Distribution.sol#252)
	Calls stack containing the loop:
		Distribution.manageUsersInPrivatePool(uint256,address[],uint256[])
DistributionV2._stake(address,uint256,uint256,uint256,uint128) (contracts/capital-protocol/old/DistributionV2.sol#254-310) has external calls inside a loop: balanceBefore_ = IERC20(depositToken).balanceOf(address(this)) (contracts/capital-protocol/old/DistributionV2.sol#274)
	Calls stack containing the loop:
		DistributionV2.manageUsersInPrivatePool(uint256,address[],uint256[],uint128[])
DistributionV2._stake(address,uint256,uint256,uint256,uint128) (contracts/capital-protocol/old/DistributionV2.sol#254-310) has external calls inside a loop: balanceAfter_ = IERC20(depositToken).balanceOf(address(this)) (contracts/capital-protocol/old/DistributionV2.sol#276)
	Calls stack containing the loop:
		DistributionV2.manageUsersInPrivatePool(uint256,address[],uint256[],uint128[])
DistributionV2._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV2.sol#312-373) has external calls inside a loop: depositTokenContractBalance_ = IERC20(depositToken).balanceOf(address(this)) (contracts/capital-protocol/old/DistributionV2.sol#333)
	Calls stack containing the loop:
		DistributionV2.manageUsersInPrivatePool(uint256,address[],uint256[],uint128[])
DistributionV3._stake(address,uint256,uint256,uint256,uint128) (contracts/capital-protocol/old/DistributionV3.sol#254-310) has external calls inside a loop: balanceBefore_ = IERC20(depositToken).balanceOf(address(this)) (contracts/capital-protocol/old/DistributionV3.sol#274)
	Calls stack containing the loop:
		DistributionV3.manageUsersInPrivatePool(uint256,address[],uint256[],uint128[])
DistributionV3._stake(address,uint256,uint256,uint256,uint128) (contracts/capital-protocol/old/DistributionV3.sol#254-310) has external calls inside a loop: balanceAfter_ = IERC20(depositToken).balanceOf(address(this)) (contracts/capital-protocol/old/DistributionV3.sol#276)
	Calls stack containing the loop:
		DistributionV3.manageUsersInPrivatePool(uint256,address[],uint256[],uint128[])
DistributionV3._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV3.sol#312-373) has external calls inside a loop: depositTokenContractBalance_ = IERC20(depositToken).balanceOf(address(this)) (contracts/capital-protocol/old/DistributionV3.sol#333)
	Calls stack containing the loop:
		DistributionV3.manageUsersInPrivatePool(uint256,address[],uint256[],uint128[])
DistributionV4._stake(address,uint256,uint256,uint256,uint128) (contracts/capital-protocol/old/DistributionV4.sol#273-329) has external calls inside a loop: balanceBefore_ = IERC20(depositToken).balanceOf(address(this)) (contracts/capital-protocol/old/DistributionV4.sol#293)
	Calls stack containing the loop:
		DistributionV4.manageUsersInPrivatePool(uint256,address[],uint256[],uint128[])
DistributionV4._stake(address,uint256,uint256,uint256,uint128) (contracts/capital-protocol/old/DistributionV4.sol#273-329) has external calls inside a loop: balanceAfter_ = IERC20(depositToken).balanceOf(address(this)) (contracts/capital-protocol/old/DistributionV4.sol#295)
	Calls stack containing the loop:
		DistributionV4.manageUsersInPrivatePool(uint256,address[],uint256[],uint128[])
DistributionV4._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV4.sol#331-392) has external calls inside a loop: depositTokenContractBalance_ = IERC20(depositToken).balanceOf(address(this)) (contracts/capital-protocol/old/DistributionV4.sol#352)
	Calls stack containing the loop:
		DistributionV4.manageUsersInPrivatePool(uint256,address[],uint256[],uint128[])
DistributionV5._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/old/DistributionV5.sol#351-423) has external calls inside a loop: balanceBefore_ = IERC20(depositToken).balanceOf(address(this)) (contracts/capital-protocol/old/DistributionV5.sol#376)
	Calls stack containing the loop:
		DistributionV5.manageUsersInPrivatePool(uint256,address[],uint256[],uint128[],address[])
DistributionV5._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/old/DistributionV5.sol#351-423) has external calls inside a loop: balanceAfter_ = IERC20(depositToken).balanceOf(address(this)) (contracts/capital-protocol/old/DistributionV5.sol#378)
	Calls stack containing the loop:
		DistributionV5.manageUsersInPrivatePool(uint256,address[],uint256[],uint128[],address[])
DistributionV5._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV5.sol#425-503) has external calls inside a loop: depositTokenContractBalance_ = IERC20(depositToken).balanceOf(address(this)) (contracts/capital-protocol/old/DistributionV5.sol#446)
	Calls stack containing the loop:
		DistributionV5.manageUsersInPrivatePool(uint256,address[],uint256[],uint128[],address[])
DistributionExt.getTotalRewards() (contracts/extensions/DistributionExt.sol#38-50) has external calls inside a loop: amount_ += distribution_.getPeriodReward(poolId_,0,uint128(block.timestamp)) (contracts/extensions/DistributionExt.sol#46)
Reference: https://github.com/crytic/slither/wiki/Detector-Documentation/#calls-inside-a-loop
INFO:Detectors:
Reentrancy in L2TokenReceiverV2._addAllowanceUpdateSwapParams(IL2TokenReceiverV2.SwapParams,bool) (contracts/capital-protocol/L2TokenReceiverV2.sol#144-158):
	External calls:
	- TransferHelper.safeApprove(newParams_.tokenIn,router,type()(uint256).max) (contracts/capital-protocol/L2TokenReceiverV2.sol#148)
	- TransferHelper.safeApprove(newParams_.tokenIn,nonfungiblePositionManager,type()(uint256).max) (contracts/capital-protocol/L2TokenReceiverV2.sol#149)
	- TransferHelper.safeApprove(newParams_.tokenOut,nonfungiblePositionManager,type()(uint256).max) (contracts/capital-protocol/L2TokenReceiverV2.sol#151)
	State variables written after the call(s):
	- firstSwapParams = newParams_ (contracts/capital-protocol/L2TokenReceiverV2.sol#154)
	- secondSwapParams = newParams_ (contracts/capital-protocol/L2TokenReceiverV2.sol#156)
Reentrancy in L2MessageReceiver._blockingLzReceive(uint16,bytes,uint64,bytes) (contracts/capital-protocol/old/L2MessageReceiver.sol#69-88):
	External calls:
	- IL2MessageReceiver(address(this)).nonblockingLzReceive(senderChainId_,senderAndReceiverAddresses_,payload_) (contracts/capital-protocol/old/L2MessageReceiver.sol#75-87)
	State variables written after the call(s):
	- failedMessages[senderChainId_][senderAndReceiverAddresses_][nonce_] = keccak256(bytes)(payload_) (contracts/capital-protocol/old/L2MessageReceiver.sol#84)
Reentrancy in DepositPool._claim(uint256,address,address) (contracts/capital-protocol/DepositPool.sol#513-574):
	External calls:
	- IDistributor(distributor).distributeRewards(rewardPoolIndex_) (contracts/capital-protocol/DepositPool.sol#531)
	State variables written after the call(s):
	- rewardPoolData.lastUpdate = uint128(block.timestamp) (contracts/capital-protocol/DepositPool.sol#548)
	- rewardPoolData.rate = currentPoolRate_ (contracts/capital-protocol/DepositPool.sol#549)
	- rewardPoolData.totalVirtualDeposited = rewardPoolData.totalVirtualDeposited + virtualDeposited_ - userData.virtualDeposited (contracts/capital-protocol/DepositPool.sol#550-553)
Reentrancy in DepositPool._claimReferrerTier(uint256,address,address) (contracts/capital-protocol/DepositPool.sol#576-611):
	External calls:
	- IDistributor(distributor).distributeRewards(rewardPoolIndex_) (contracts/capital-protocol/DepositPool.sol#580)
	State variables written after the call(s):
	- rewardPoolData.lastUpdate = uint128(block.timestamp) (contracts/capital-protocol/DepositPool.sol#596)
	- rewardPoolData.rate = currentPoolRate_ (contracts/capital-protocol/DepositPool.sol#597)
	- rewardPoolsProtocolDetails[rewardPoolIndex_].distributedRewards += rewards_ (contracts/capital-protocol/DepositPool.sol#600)
Reentrancy in L2TokenReceiver._editParams(IL2TokenReceiver.SwapParams) (contracts/capital-protocol/old/L2TokenReceiver.sol#143-153):
	External calls:
	- TransferHelper.safeApprove(newParams_.tokenIn,router,type()(uint256).max) (contracts/capital-protocol/old/L2TokenReceiver.sol#147)
	- TransferHelper.safeApprove(newParams_.tokenIn,nonfungiblePositionManager,type()(uint256).max) (contracts/capital-protocol/old/L2TokenReceiver.sol#148)
	- TransferHelper.safeApprove(newParams_.tokenOut,nonfungiblePositionManager,type()(uint256).max) (contracts/capital-protocol/old/L2TokenReceiver.sol#150)
	State variables written after the call(s):
	- params = newParams_ (contracts/capital-protocol/old/L2TokenReceiver.sol#152)
Reentrancy in BuildersV3._migrateUserStake(bytes32,address) (contracts/builder-protocol/BuildersV3.sol#447-457):
	External calls:
	- IBuilderSubnets(builderSubnets).stake(builderPoolId_,user_,userData.deposited) (contracts/builder-protocol/BuildersV3.sol#451)
	State variables written after the call(s):
	- totalDepositsMigrated += userData.deposited (contracts/builder-protocol/BuildersV3.sol#454)
Reentrancy in DepositPool._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/DepositPool.sol#353-432):
	External calls:
	- IERC20(depositToken).safeTransferFrom(_msgSender(),address(this),amount_) (contracts/capital-protocol/DepositPool.sol#381)
	- IDistributor(distributor).supply(rewardPoolIndex_,amount_) (contracts/capital-protocol/DepositPool.sol#386)
	State variables written after the call(s):
	- totalDepositedInPublicPools += amount_ (contracts/capital-protocol/DepositPool.sol#390)
Reentrancy in DistributionV6._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/DistributionV6.sol#405-477):
	External calls:
	- IERC20(depositToken).safeTransferFrom(_msgSender(),address(this),amount_) (contracts/capital-protocol/DistributionV6.sol#431)
	State variables written after the call(s):
	- totalDepositedInPublicPools += amount_ (contracts/capital-protocol/DistributionV6.sol#438)
Reentrancy in Distribution._stake(address,uint256,uint256,uint256) (contracts/capital-protocol/old/Distribution.sol#196-229):
	External calls:
	- IERC20(depositToken).safeTransferFrom(_msgSender(),address(this),amount_) (contracts/capital-protocol/old/Distribution.sol#206)
	State variables written after the call(s):
	- totalDepositedInPublicPools += amount_ (contracts/capital-protocol/old/Distribution.sol#213)
Reentrancy in DistributionV2._stake(address,uint256,uint256,uint256,uint128) (contracts/capital-protocol/old/DistributionV2.sol#254-310):
	External calls:
	- IERC20(depositToken).safeTransferFrom(_msgSender(),address(this),amount_) (contracts/capital-protocol/old/DistributionV2.sol#275)
	State variables written after the call(s):
	- totalDepositedInPublicPools += amount_ (contracts/capital-protocol/old/DistributionV2.sol#282)
Reentrancy in DistributionV3._stake(address,uint256,uint256,uint256,uint128) (contracts/capital-protocol/old/DistributionV3.sol#254-310):
	External calls:
	- IERC20(depositToken).safeTransferFrom(_msgSender(),address(this),amount_) (contracts/capital-protocol/old/DistributionV3.sol#275)
	State variables written after the call(s):
	- totalDepositedInPublicPools += amount_ (contracts/capital-protocol/old/DistributionV3.sol#282)
Reentrancy in DistributionV4._stake(address,uint256,uint256,uint256,uint128) (contracts/capital-protocol/old/DistributionV4.sol#273-329):
	External calls:
	- IERC20(depositToken).safeTransferFrom(_msgSender(),address(this),amount_) (contracts/capital-protocol/old/DistributionV4.sol#294)
	State variables written after the call(s):
	- totalDepositedInPublicPools += amount_ (contracts/capital-protocol/old/DistributionV4.sol#301)
Reentrancy in DistributionV5._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/old/DistributionV5.sol#351-423):
	External calls:
	- IERC20(depositToken).safeTransferFrom(_msgSender(),address(this),amount_) (contracts/capital-protocol/old/DistributionV5.sol#377)
	State variables written after the call(s):
	- totalDepositedInPublicPools += amount_ (contracts/capital-protocol/old/DistributionV5.sol#384)
Reentrancy in Distributor.addDepositPool(uint256,address,address,string,IDistributor.Strategy) (contracts/capital-protocol/Distributor.sol#192-248):
	External calls:
	- IERC20(token_).safeApprove(aavePool,type()(uint256).max) (contracts/capital-protocol/Distributor.sol#232)
	- IERC20(aToken_).approve(aavePool,type()(uint256).max) (contracts/capital-protocol/Distributor.sol#233)
	State variables written after the call(s):
	- depositPools[rewardPoolIndex_][depositPoolAddress_] = depositPool_ (contracts/capital-protocol/Distributor.sol#239)
Reentrancy in BuilderSubnets.createSubnet(IBuilderSubnets.Subnet,IBuilderSubnets.SubnetMetadata) (contracts/builder-protocol/BuilderSubnets.sol#167-195):
	External calls:
	- IERC20(token).safeTransferFrom(_msgSender(),subnetCreationFeeTreasury,subnetCreationFeeAmount) (contracts/builder-protocol/BuilderSubnets.sol#187)
	State variables written after the call(s):
	- subnetsMetadata[subnetId_] = metadata_ (contracts/builder-protocol/BuilderSubnets.sol#191)
Reentrancy in BuildersV2.deposit(bytes32,uint256) (contracts/builder-protocol/BuildersV2.sol#129-148):
	External calls:
	- IERC20(depositToken).safeTransferFrom(_msgSender(),address(this),amount_) (contracts/builder-protocol/BuildersV2.sol#142)
	State variables written after the call(s):
	- _updatePoolData(builderPoolId_,deposited_,userData) (contracts/builder-protocol/BuildersV2.sol#144)
		- builderPoolData.rate = currentRate_ (contracts/builder-protocol/BuildersV2.sol#250)
		- builderPoolData.pendingRewards = pendingRewards_ (contracts/builder-protocol/BuildersV2.sol#251)
		- builderPoolData.deposited = builderPoolData.deposited + newDeposited_ - userData.deposited (contracts/builder-protocol/BuildersV2.sol#252)
		- builderPoolData.virtualDeposited = builderPoolData.virtualDeposited + virtualDeposited_ - oldVirtualDeposited_ (contracts/builder-protocol/BuildersV2.sol#253)
	- _updatePoolData(builderPoolId_,deposited_,userData) (contracts/builder-protocol/BuildersV2.sol#144)
		- totalPoolData.distributedRewards += newPoolRewards_ (contracts/builder-protocol/BuildersV2.sol#241)
		- totalPoolData.rate = currentRate_ (contracts/builder-protocol/BuildersV2.sol#242)
		- totalPoolData.totalDeposited = totalPoolData.totalDeposited + newDeposited_ - userData.deposited (contracts/builder-protocol/BuildersV2.sol#243)
		- totalPoolData.totalVirtualDeposited = totalPoolData.totalVirtualDeposited + virtualDeposited_ - oldVirtualDeposited_ (contracts/builder-protocol/BuildersV2.sol#244-247)
Reentrancy in BuildersV3.deposit(bytes32,uint256) (contracts/builder-protocol/BuildersV3.sol#143-162):
	External calls:
	- IERC20(depositToken).safeTransferFrom(_msgSender(),address(this),amount_) (contracts/builder-protocol/BuildersV3.sol#156)
	State variables written after the call(s):
	- _updatePoolData(builderPoolId_,deposited_,userData) (contracts/builder-protocol/BuildersV3.sol#158)
		- builderPoolData.rate = currentRate_ (contracts/builder-protocol/BuildersV3.sol#270)
		- builderPoolData.pendingRewards = pendingRewards_ (contracts/builder-protocol/BuildersV3.sol#271)
		- builderPoolData.deposited = builderPoolData.deposited + newDeposited_ - userData.deposited (contracts/builder-protocol/BuildersV3.sol#272)
		- builderPoolData.virtualDeposited = builderPoolData.virtualDeposited + virtualDeposited_ - oldVirtualDeposited_ (contracts/builder-protocol/BuildersV3.sol#273)
	- _updatePoolData(builderPoolId_,deposited_,userData) (contracts/builder-protocol/BuildersV3.sol#158)
		- totalPoolData.distributedRewards += newPoolRewards_ (contracts/builder-protocol/BuildersV3.sol#261)
		- totalPoolData.rate = currentRate_ (contracts/builder-protocol/BuildersV3.sol#262)
		- totalPoolData.totalDeposited = totalPoolData.totalDeposited + newDeposited_ - userData.deposited (contracts/builder-protocol/BuildersV3.sol#263)
		- totalPoolData.totalVirtualDeposited = totalPoolData.totalVirtualDeposited + virtualDeposited_ - oldVirtualDeposited_ (contracts/builder-protocol/BuildersV3.sol#264-267)
Reentrancy in Builders.deposit(bytes32,uint256) (contracts/builder-protocol/old/Builders.sol#129-148):
	External calls:
	- IERC20(depositToken).safeTransferFrom(_msgSender(),address(this),amount_) (contracts/builder-protocol/old/Builders.sol#142)
	State variables written after the call(s):
	- _updatePoolData(builderPoolId_,deposited_,userData) (contracts/builder-protocol/old/Builders.sol#144)
		- builderPoolData.rate = currentRate_ (contracts/builder-protocol/old/Builders.sol#247)
		- builderPoolData.pendingRewards = pendingRewards_ (contracts/builder-protocol/old/Builders.sol#248)
		- builderPoolData.deposited = builderPoolData.deposited + newDeposited_ - userData.deposited (contracts/builder-protocol/old/Builders.sol#249)
		- builderPoolData.virtualDeposited = builderPoolData.virtualDeposited + virtualDeposited_ - userData.virtualDeposited (contracts/builder-protocol/old/Builders.sol#250-253)
	- _updatePoolData(builderPoolId_,deposited_,userData) (contracts/builder-protocol/old/Builders.sol#144)
		- totalPoolData.distributedRewards += newPoolRewards_ (contracts/builder-protocol/old/Builders.sol#238)
		- totalPoolData.rate = currentRate_ (contracts/builder-protocol/old/Builders.sol#239)
		- totalPoolData.totalDeposited = totalPoolData.totalDeposited + newDeposited_ - userData.deposited (contracts/builder-protocol/old/Builders.sol#240)
		- totalPoolData.totalVirtualDeposited = totalPoolData.totalVirtualDeposited + virtualDeposited_ - userData.virtualDeposited (contracts/builder-protocol/old/Builders.sol#241-244)
Reentrancy in DepositPool.lockClaim(uint256,uint128) (contracts/capital-protocol/DepositPool.sol#307-351):
	External calls:
	- IDistributor(distributor).distributeRewards(rewardPoolIndex_) (contracts/capital-protocol/DepositPool.sol#313)
	State variables written after the call(s):
	- rewardPoolData.lastUpdate = uint128(block.timestamp) (contracts/capital-protocol/DepositPool.sol#335)
	- rewardPoolData.rate = currentPoolRate_ (contracts/capital-protocol/DepositPool.sol#336)
	- rewardPoolData.totalVirtualDeposited = rewardPoolData.totalVirtualDeposited + virtualDeposited_ - userData.virtualDeposited (contracts/capital-protocol/DepositPool.sol#337-340)
	- rewardPoolsProtocolDetails[rewardPoolIndex_].distributedRewards += rewards_ (contracts/capital-protocol/DepositPool.sol#348)
	- userData.pendingRewards = _getCurrentUserReward(currentPoolRate_,userData) (contracts/capital-protocol/DepositPool.sol#324)
	- userData.virtualDeposited = userData.deposited (contracts/capital-protocol/DepositPool.sol#331)
	- userData.rate = currentPoolRate_ (contracts/capital-protocol/DepositPool.sol#343)
	- userData.virtualDeposited = virtualDeposited_ (contracts/capital-protocol/DepositPool.sol#344)
	- userData.claimLockStart = claimLockStart_ (contracts/capital-protocol/DepositPool.sol#345)
	- userData.claimLockEnd = claimLockEnd_ (contracts/capital-protocol/DepositPool.sol#346)
Reentrancy in DepositPool.manageUsersInPrivateRewardPool(uint256,address[],uint256[],uint128[],address[]) (contracts/capital-protocol/DepositPool.sol#188-225):
	External calls:
	- IDistributor(distributor).distributeRewards(rewardPoolIndex_) (contracts/capital-protocol/DepositPool.sol#203)
	State variables written after the call(s):
	- rewardPoolsProtocolDetails[rewardPoolIndex_].distributedRewards += rewards_ (contracts/capital-protocol/DepositPool.sol#207)
Reentrancy in BuilderSubnets.stake(bytes32,address,uint256) (contracts/builder-protocol/BuilderSubnets.sol#254-276):
	External calls:
	- IERC20(token).safeTransferFrom(_msgSender(),address(this),amount_) (contracts/builder-protocol/BuilderSubnets.sol#270)
	State variables written after the call(s):
	- _updateStorage(subnetId_,stakerAddress_,staked_) (contracts/builder-protocol/BuilderSubnets.sol#272)
		- allSubnetsData.lastCalculatedTimestamp = now_ (contracts/builder-protocol/BuilderSubnets.sol#369)
		- allSubnetsData.rate = rate_ (contracts/builder-protocol/BuilderSubnets.sol#370)
		- allSubnetsData.staked = allSubnetsData.staked + newStaked_ - staker.staked (contracts/builder-protocol/BuilderSubnets.sol#371)
		- allSubnetsData.undistributedRewards += undistributedRewards_ (contracts/builder-protocol/BuilderSubnets.sol#372)
	- _updateStorage(subnetId_,stakerAddress_,staked_) (contracts/builder-protocol/BuilderSubnets.sol#272)
		- subnetData.staked = subnetData.staked + newStaked_ - staker.staked (contracts/builder-protocol/BuilderSubnets.sol#375)
Reference: https://github.com/crytic/slither/wiki/Detector-Documentation#reentrancy-vulnerabilities-2
INFO:Detectors:
Reentrancy in L2MessageReceiver._blockingLzReceive(uint16,bytes,uint64,bytes) (contracts/capital-protocol/old/L2MessageReceiver.sol#69-88):
	External calls:
	- IL2MessageReceiver(address(this)).nonblockingLzReceive(senderChainId_,senderAndReceiverAddresses_,payload_) (contracts/capital-protocol/old/L2MessageReceiver.sol#75-87)
	Event emitted after the call(s):
	- MessageFailed(senderChainId_,senderAndReceiverAddresses_,nonce_,payload_,reason_) (contracts/capital-protocol/old/L2MessageReceiver.sol#86)
	- MessageSuccess(senderChainId_,senderAndReceiverAddresses_,nonce_,payload_) (contracts/capital-protocol/old/L2MessageReceiver.sol#82)
Reentrancy in DepositPool._claim(uint256,address,address) (contracts/capital-protocol/DepositPool.sol#513-574):
	External calls:
	- IDistributor(distributor).distributeRewards(rewardPoolIndex_) (contracts/capital-protocol/DepositPool.sol#531)
	- IDistributor(distributor).sendMintMessage{value: msg.value}(rewardPoolIndex_,receiver_,pendingRewards_,_msgSender()) (contracts/capital-protocol/DepositPool.sol#566-571)
	External calls sending eth:
	- IDistributor(distributor).sendMintMessage{value: msg.value}(rewardPoolIndex_,receiver_,pendingRewards_,_msgSender()) (contracts/capital-protocol/DepositPool.sol#566-571)
	Event emitted after the call(s):
	- UserClaimed(rewardPoolIndex_,user_,receiver_,pendingRewards_) (contracts/capital-protocol/DepositPool.sol#573)
Reentrancy in DistributionV6._claim(uint256,address,address) (contracts/capital-protocol/DistributionV6.sol#257-304):
	External calls:
	- IL1Sender(l1Sender).sendMintMessage{value: msg.value}(receiver_,pendingRewards_,_msgSender()) (contracts/capital-protocol/DistributionV6.sol#301)
	Event emitted after the call(s):
	- UserClaimed(poolId_,user_,receiver_,pendingRewards_) (contracts/capital-protocol/DistributionV6.sol#303)
Reentrancy in DepositPool._claimReferrerTier(uint256,address,address) (contracts/capital-protocol/DepositPool.sol#576-611):
	External calls:
	- IDistributor(distributor).distributeRewards(rewardPoolIndex_) (contracts/capital-protocol/DepositPool.sol#580)
	- IDistributor(distributor).sendMintMessage{value: msg.value}(rewardPoolIndex_,receiver_,pendingRewards_,_msgSender()) (contracts/capital-protocol/DepositPool.sol#603-608)
	External calls sending eth:
	- IDistributor(distributor).sendMintMessage{value: msg.value}(rewardPoolIndex_,receiver_,pendingRewards_,_msgSender()) (contracts/capital-protocol/DepositPool.sol#603-608)
	Event emitted after the call(s):
	- ReferrerClaimed(rewardPoolIndex_,referrer_,receiver_,pendingRewards_) (contracts/capital-protocol/DepositPool.sol#610)
Reentrancy in DistributionV6._claimReferrerTier(uint256,address,address) (contracts/capital-protocol/DistributionV6.sol#320-344):
	External calls:
	- IL1Sender(l1Sender).sendMintMessage{value: msg.value}(receiver_,pendingRewards_,_msgSender()) (contracts/capital-protocol/DistributionV6.sol#341)
	Event emitted after the call(s):
	- ReferrerClaimed(poolId_,referrer_,receiver_,pendingRewards_) (contracts/capital-protocol/DistributionV6.sol#343)
Reentrancy in OFTCore._lzReceive(Origin,bytes32,bytes,address,bytes) (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oft/OFTCore.sol#225-256):
	External calls:
	- endpoint.sendCompose(toAddress,_guid,0,composeMsg) (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oft/OFTCore.sol#252)
	Event emitted after the call(s):
	- OFTReceived(_guid,_origin.srcEid,toAddress,amountReceivedLD) (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oft/OFTCore.sol#255)
Reentrancy in BuildersV3._migrateUserStake(bytes32,address) (contracts/builder-protocol/BuildersV3.sol#447-457):
	External calls:
	- IBuilderSubnets(builderSubnets).stake(builderPoolId_,user_,userData.deposited) (contracts/builder-protocol/BuildersV3.sol#451)
	Event emitted after the call(s):
	- UserMigrated(builderPoolId_,user_) (contracts/builder-protocol/BuildersV3.sol#456)
Reentrancy in DepositPool._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/DepositPool.sol#353-432):
	External calls:
	- IERC20(depositToken).safeTransferFrom(_msgSender(),address(this),amount_) (contracts/capital-protocol/DepositPool.sol#381)
	- IDistributor(distributor).supply(rewardPoolIndex_,amount_) (contracts/capital-protocol/DepositPool.sol#386)
	Event emitted after the call(s):
	- UserClaimLocked(rewardPoolIndex_,user_,uint128(block.timestamp),claimLockEnd_) (contracts/capital-protocol/DepositPool.sol#431)
	- UserReferred(rewardPoolIndex_,user_,newReferrer_,newDeposited_) (contracts/capital-protocol/DepositPool.sol#638)
		- _applyReferrerTier(user_,rewardPoolIndex_,currentPoolRate_,userData.deposited,deposited_,userData.referrer,referrer_) (contracts/capital-protocol/DepositPool.sol#403-411)
	- UserReferred(rewardPoolIndex_,user_,newReferrer_,newDeposited_) (contracts/capital-protocol/DepositPool.sol#650)
		- _applyReferrerTier(user_,rewardPoolIndex_,currentPoolRate_,userData.deposited,deposited_,userData.referrer,referrer_) (contracts/capital-protocol/DepositPool.sol#403-411)
	- UserReferred(rewardPoolIndex_,user_,oldReferrer_,0) (contracts/capital-protocol/DepositPool.sol#660)
		- _applyReferrerTier(user_,rewardPoolIndex_,currentPoolRate_,userData.deposited,deposited_,userData.referrer,referrer_) (contracts/capital-protocol/DepositPool.sol#403-411)
	- UserReferred(rewardPoolIndex_,user_,newReferrer_,newDeposited_) (contracts/capital-protocol/DepositPool.sol#661)
		- _applyReferrerTier(user_,rewardPoolIndex_,currentPoolRate_,userData.deposited,deposited_,userData.referrer,referrer_) (contracts/capital-protocol/DepositPool.sol#403-411)
	- UserStaked(rewardPoolIndex_,user_,amount_) (contracts/capital-protocol/DepositPool.sol#430)
Reentrancy in DistributionV6._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/DistributionV6.sol#405-477):
	External calls:
	- IERC20(depositToken).safeTransferFrom(_msgSender(),address(this),amount_) (contracts/capital-protocol/DistributionV6.sol#431)
	Event emitted after the call(s):
	- UserClaimLocked(poolId_,user_,uint128(block.timestamp),claimLockEnd_) (contracts/capital-protocol/DistributionV6.sol#476)
	- UserReferred(poolId_,user_,newReferrer_,newDeposited_) (contracts/capital-protocol/DistributionV6.sol#584)
		- _applyReferrerTier(user_,poolId_,currentPoolRate_,userData.deposited,deposited_,userData.referrer,referrer_) (contracts/capital-protocol/DistributionV6.sol#451-459)
	- UserReferred(poolId_,user_,newReferrer_,newDeposited_) (contracts/capital-protocol/DistributionV6.sol#597)
		- _applyReferrerTier(user_,poolId_,currentPoolRate_,userData.deposited,deposited_,userData.referrer,referrer_) (contracts/capital-protocol/DistributionV6.sol#451-459)
	- UserReferred(poolId_,user_,oldReferrer_,0) (contracts/capital-protocol/DistributionV6.sol#607)
		- _applyReferrerTier(user_,poolId_,currentPoolRate_,userData.deposited,deposited_,userData.referrer,referrer_) (contracts/capital-protocol/DistributionV6.sol#451-459)
	- UserReferred(poolId_,user_,newReferrer_,newDeposited_) (contracts/capital-protocol/DistributionV6.sol#608)
		- _applyReferrerTier(user_,poolId_,currentPoolRate_,userData.deposited,deposited_,userData.referrer,referrer_) (contracts/capital-protocol/DistributionV6.sol#451-459)
	- UserStaked(poolId_,user_,amount_) (contracts/capital-protocol/DistributionV6.sol#475)
Reentrancy in Distribution._stake(address,uint256,uint256,uint256) (contracts/capital-protocol/old/Distribution.sol#196-229):
	External calls:
	- IERC20(depositToken).safeTransferFrom(_msgSender(),address(this),amount_) (contracts/capital-protocol/old/Distribution.sol#206)
	Event emitted after the call(s):
	- UserStaked(poolId_,user_,amount_) (contracts/capital-protocol/old/Distribution.sol#228)
Reentrancy in DistributionV2._stake(address,uint256,uint256,uint256,uint128) (contracts/capital-protocol/old/DistributionV2.sol#254-310):
	External calls:
	- IERC20(depositToken).safeTransferFrom(_msgSender(),address(this),amount_) (contracts/capital-protocol/old/DistributionV2.sol#275)
	Event emitted after the call(s):
	- UserClaimLocked(poolId_,user_,uint128(block.timestamp),claimLockEnd_) (contracts/capital-protocol/old/DistributionV2.sol#309)
	- UserStaked(poolId_,user_,amount_) (contracts/capital-protocol/old/DistributionV2.sol#308)
Reentrancy in DistributionV3._stake(address,uint256,uint256,uint256,uint128) (contracts/capital-protocol/old/DistributionV3.sol#254-310):
	External calls:
	- IERC20(depositToken).safeTransferFrom(_msgSender(),address(this),amount_) (contracts/capital-protocol/old/DistributionV3.sol#275)
	Event emitted after the call(s):
	- UserClaimLocked(poolId_,user_,uint128(block.timestamp),claimLockEnd_) (contracts/capital-protocol/old/DistributionV3.sol#309)
	- UserStaked(poolId_,user_,amount_) (contracts/capital-protocol/old/DistributionV3.sol#308)
Reentrancy in DistributionV4._stake(address,uint256,uint256,uint256,uint128) (contracts/capital-protocol/old/DistributionV4.sol#273-329):
	External calls:
	- IERC20(depositToken).safeTransferFrom(_msgSender(),address(this),amount_) (contracts/capital-protocol/old/DistributionV4.sol#294)
	Event emitted after the call(s):
	- UserClaimLocked(poolId_,user_,uint128(block.timestamp),claimLockEnd_) (contracts/capital-protocol/old/DistributionV4.sol#328)
	- UserStaked(poolId_,user_,amount_) (contracts/capital-protocol/old/DistributionV4.sol#327)
Reentrancy in DistributionV5._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/old/DistributionV5.sol#351-423):
	External calls:
	- IERC20(depositToken).safeTransferFrom(_msgSender(),address(this),amount_) (contracts/capital-protocol/old/DistributionV5.sol#377)
	Event emitted after the call(s):
	- UserClaimLocked(poolId_,user_,uint128(block.timestamp),claimLockEnd_) (contracts/capital-protocol/old/DistributionV5.sol#422)
	- UserReferred(poolId_,user_,newReferrer_,newDeposited_) (contracts/capital-protocol/old/DistributionV5.sol#530)
		- _applyReferrerTier(user_,poolId_,currentPoolRate_,userData.deposited,deposited_,userData.referrer,referrer_) (contracts/capital-protocol/old/DistributionV5.sol#397-405)
	- UserReferred(poolId_,user_,newReferrer_,newDeposited_) (contracts/capital-protocol/old/DistributionV5.sol#543)
		- _applyReferrerTier(user_,poolId_,currentPoolRate_,userData.deposited,deposited_,userData.referrer,referrer_) (contracts/capital-protocol/old/DistributionV5.sol#397-405)
	- UserReferred(poolId_,user_,oldReferrer_,0) (contracts/capital-protocol/old/DistributionV5.sol#553)
		- _applyReferrerTier(user_,poolId_,currentPoolRate_,userData.deposited,deposited_,userData.referrer,referrer_) (contracts/capital-protocol/old/DistributionV5.sol#397-405)
	- UserReferred(poolId_,user_,newReferrer_,newDeposited_) (contracts/capital-protocol/old/DistributionV5.sol#554)
		- _applyReferrerTier(user_,poolId_,currentPoolRate_,userData.deposited,deposited_,userData.referrer,referrer_) (contracts/capital-protocol/old/DistributionV5.sol#397-405)
	- UserStaked(poolId_,user_,amount_) (contracts/capital-protocol/old/DistributionV5.sol#421)
Reentrancy in DepositPool._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/DepositPool.sol#434-511):
	External calls:
	- IDistributor(distributor).withdraw(rewardPoolIndex_,amount_) (contracts/capital-protocol/DepositPool.sol#506)
	- IERC20(depositToken).safeTransfer(user_,amount_) (contracts/capital-protocol/DepositPool.sol#507)
	Event emitted after the call(s):
	- UserWithdrawn(rewardPoolIndex_,user_,amount_) (contracts/capital-protocol/DepositPool.sol#510)
Reentrancy in DistributionV6._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/DistributionV6.sol#479-557):
	External calls:
	- IERC20(depositToken).safeTransfer(user_,amount_) (contracts/capital-protocol/DistributionV6.sol#553)
	Event emitted after the call(s):
	- UserWithdrawn(poolId_,user_,amount_) (contracts/capital-protocol/DistributionV6.sol#556)
Reentrancy in Distribution._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/Distribution.sol#231-284):
	External calls:
	- IERC20(depositToken).safeTransfer(user_,amount_) (contracts/capital-protocol/old/Distribution.sol#280)
	Event emitted after the call(s):
	- UserWithdrawn(poolId_,user_,amount_) (contracts/capital-protocol/old/Distribution.sol#283)
Reentrancy in DistributionV2._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV2.sol#312-373):
	External calls:
	- IERC20(depositToken).safeTransfer(user_,amount_) (contracts/capital-protocol/old/DistributionV2.sol#369)
	Event emitted after the call(s):
	- UserWithdrawn(poolId_,user_,amount_) (contracts/capital-protocol/old/DistributionV2.sol#372)
Reentrancy in DistributionV3._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV3.sol#312-373):
	External calls:
	- IERC20(depositToken).safeTransfer(user_,amount_) (contracts/capital-protocol/old/DistributionV3.sol#369)
	Event emitted after the call(s):
	- UserWithdrawn(poolId_,user_,amount_) (contracts/capital-protocol/old/DistributionV3.sol#372)
Reentrancy in DistributionV4._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV4.sol#331-392):
	External calls:
	- IERC20(depositToken).safeTransfer(user_,amount_) (contracts/capital-protocol/old/DistributionV4.sol#388)
	Event emitted after the call(s):
	- UserWithdrawn(poolId_,user_,amount_) (contracts/capital-protocol/old/DistributionV4.sol#391)
Reentrancy in DistributionV5._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV5.sol#425-503):
	External calls:
	- IERC20(depositToken).safeTransfer(user_,amount_) (contracts/capital-protocol/old/DistributionV5.sol#499)
	Event emitted after the call(s):
	- UserWithdrawn(poolId_,user_,amount_) (contracts/capital-protocol/old/DistributionV5.sol#502)
Reentrancy in Distributor.addDepositPool(uint256,address,address,string,IDistributor.Strategy) (contracts/capital-protocol/Distributor.sol#192-248):
	External calls:
	- IERC20(token_).safeApprove(aavePool,type()(uint256).max) (contracts/capital-protocol/Distributor.sol#232)
	- IERC20(aToken_).approve(aavePool,type()(uint256).max) (contracts/capital-protocol/Distributor.sol#233)
	Event emitted after the call(s):
	- DepositPoolAdded(rewardPoolIndex_,depositPool_) (contracts/capital-protocol/Distributor.sol#247)
	- TokenPriceSet(depositPool.chainLinkPath,price_) (contracts/capital-protocol/Distributor.sol#277)
		- updateDepositTokensPrices(rewardPoolIndex_) (contracts/capital-protocol/Distributor.sol#244)
Reentrancy in DistributionV6.bridgeOverplus(uint256,uint256,uint256) (contracts/capital-protocol/DistributionV6.sol#743-762):
	External calls:
	- IERC20(depositToken).safeTransfer(l1Sender,overplus_) (contracts/capital-protocol/DistributionV6.sol#751)
	- bridgeMessageId_ = IL1Sender(l1Sender).sendDepositToken{value: msg.value}(gasLimit_,maxFeePerGas_,maxSubmissionCost_) (contracts/capital-protocol/DistributionV6.sol#753-757)
	External calls sending eth:
	- bridgeMessageId_ = IL1Sender(l1Sender).sendDepositToken{value: msg.value}(gasLimit_,maxFeePerGas_,maxSubmissionCost_) (contracts/capital-protocol/DistributionV6.sol#753-757)
	Event emitted after the call(s):
	- OverplusBridged(overplus_,bridgeMessageId_) (contracts/capital-protocol/DistributionV6.sol#759)
Reentrancy in Distribution.bridgeOverplus(uint256,uint256,uint256) (contracts/capital-protocol/old/Distribution.sol#321-340):
	External calls:
	- IERC20(depositToken).safeTransfer(l1Sender,overplus_) (contracts/capital-protocol/old/Distribution.sol#329)
	- bridgeMessageId_ = L1Sender(l1Sender).sendDepositToken{value: msg.value}(gasLimit_,maxFeePerGas_,maxSubmissionCost_) (contracts/capital-protocol/old/Distribution.sol#331-335)
	External calls sending eth:
	- bridgeMessageId_ = L1Sender(l1Sender).sendDepositToken{value: msg.value}(gasLimit_,maxFeePerGas_,maxSubmissionCost_) (contracts/capital-protocol/old/Distribution.sol#331-335)
	Event emitted after the call(s):
	- OverplusBridged(overplus_,bridgeMessageId_) (contracts/capital-protocol/old/Distribution.sol#337)
Reentrancy in DistributionV2.bridgeOverplus(uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV2.sol#475-494):
	External calls:
	- IERC20(depositToken).safeTransfer(l1Sender,overplus_) (contracts/capital-protocol/old/DistributionV2.sol#483)
	- bridgeMessageId_ = L1Sender(l1Sender).sendDepositToken{value: msg.value}(gasLimit_,maxFeePerGas_,maxSubmissionCost_) (contracts/capital-protocol/old/DistributionV2.sol#485-489)
	External calls sending eth:
	- bridgeMessageId_ = L1Sender(l1Sender).sendDepositToken{value: msg.value}(gasLimit_,maxFeePerGas_,maxSubmissionCost_) (contracts/capital-protocol/old/DistributionV2.sol#485-489)
	Event emitted after the call(s):
	- OverplusBridged(overplus_,bridgeMessageId_) (contracts/capital-protocol/old/DistributionV2.sol#491)
Reentrancy in DistributionV3.bridgeOverplus(uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV3.sol#475-494):
	External calls:
	- IERC20(depositToken).safeTransfer(l1Sender,overplus_) (contracts/capital-protocol/old/DistributionV3.sol#483)
	- bridgeMessageId_ = L1Sender(l1Sender).sendDepositToken{value: msg.value}(gasLimit_,maxFeePerGas_,maxSubmissionCost_) (contracts/capital-protocol/old/DistributionV3.sol#485-489)
	External calls sending eth:
	- bridgeMessageId_ = L1Sender(l1Sender).sendDepositToken{value: msg.value}(gasLimit_,maxFeePerGas_,maxSubmissionCost_) (contracts/capital-protocol/old/DistributionV3.sol#485-489)
	Event emitted after the call(s):
	- OverplusBridged(overplus_,bridgeMessageId_) (contracts/capital-protocol/old/DistributionV3.sol#491)
Reentrancy in DistributionV4.bridgeOverplus(uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV4.sol#494-513):
	External calls:
	- IERC20(depositToken).safeTransfer(l1Sender,overplus_) (contracts/capital-protocol/old/DistributionV4.sol#502)
	- bridgeMessageId_ = L1Sender(l1Sender).sendDepositToken{value: msg.value}(gasLimit_,maxFeePerGas_,maxSubmissionCost_) (contracts/capital-protocol/old/DistributionV4.sol#504-508)
	External calls sending eth:
	- bridgeMessageId_ = L1Sender(l1Sender).sendDepositToken{value: msg.value}(gasLimit_,maxFeePerGas_,maxSubmissionCost_) (contracts/capital-protocol/old/DistributionV4.sol#504-508)
	Event emitted after the call(s):
	- OverplusBridged(overplus_,bridgeMessageId_) (contracts/capital-protocol/old/DistributionV4.sol#510)
Reentrancy in DistributionV5.bridgeOverplus(uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV5.sol#689-708):
	External calls:
	- IERC20(depositToken).safeTransfer(l1Sender,overplus_) (contracts/capital-protocol/old/DistributionV5.sol#697)
	- bridgeMessageId_ = IL1Sender(l1Sender).sendDepositToken{value: msg.value}(gasLimit_,maxFeePerGas_,maxSubmissionCost_) (contracts/capital-protocol/old/DistributionV5.sol#699-703)
	External calls sending eth:
	- bridgeMessageId_ = IL1Sender(l1Sender).sendDepositToken{value: msg.value}(gasLimit_,maxFeePerGas_,maxSubmissionCost_) (contracts/capital-protocol/old/DistributionV5.sol#699-703)
	Event emitted after the call(s):
	- OverplusBridged(overplus_,bridgeMessageId_) (contracts/capital-protocol/old/DistributionV5.sol#705)
Reentrancy in BuilderSubnets.claim(bytes32,address) (contracts/builder-protocol/BuilderSubnets.sol#308-339):
	External calls:
	- IERC20(token).safeTransferFrom(treasury,protocolTreasury_,protocolFee_) (contracts/builder-protocol/BuilderSubnets.sol#320)
	Event emitted after the call(s):
	- FeePaid(subnetId_,stakerAddress_,protocolFee_,protocolTreasury_) (contracts/builder-protocol/BuilderSubnets.sol#323)
Reentrancy in BuilderSubnets.claim(bytes32,address) (contracts/builder-protocol/BuilderSubnets.sol#308-339):
	External calls:
	- IERC20(token).safeTransferFrom(treasury,protocolTreasury_,protocolFee_) (contracts/builder-protocol/BuilderSubnets.sol#320)
	- IERC20(token).safeTransferFrom(treasury,subnetTreasury_,subnetFee_) (contracts/builder-protocol/BuilderSubnets.sol#329)
	Event emitted after the call(s):
	- FeePaid(subnetId_,stakerAddress_,subnetFee_,subnetTreasury_) (contracts/builder-protocol/BuilderSubnets.sol#332)
Reentrancy in BuilderSubnets.claim(bytes32,address) (contracts/builder-protocol/BuilderSubnets.sol#308-339):
	External calls:
	- IERC20(token).safeTransferFrom(treasury,protocolTreasury_,protocolFee_) (contracts/builder-protocol/BuilderSubnets.sol#320)
	- IERC20(token).safeTransferFrom(treasury,subnetTreasury_,subnetFee_) (contracts/builder-protocol/BuilderSubnets.sol#329)
	- IERC20(token).safeTransferFrom(treasury,stakerAddress_,toClaim_) (contracts/builder-protocol/BuilderSubnets.sol#335)
	Event emitted after the call(s):
	- Claimed(subnetId_,stakerAddress_,staker,toClaim_) (contracts/builder-protocol/BuilderSubnets.sol#337)
Reentrancy in BuildersV2.claim(bytes32,address) (contracts/builder-protocol/BuildersV2.sol#183-221):
	External calls:
	- IBuildersTreasury(buildersTreasury).sendRewards(treasuryAddress_,fee_) (contracts/builder-protocol/BuildersV2.sol#213)
	- IBuildersTreasury(buildersTreasury).sendRewards(receiver_,pendingRewards_) (contracts/builder-protocol/BuildersV2.sol#217)
	Event emitted after the call(s):
	- AdminClaimed(builderPoolId_,receiver_,pendingRewards_) (contracts/builder-protocol/BuildersV2.sol#219)
	- FeePaid(user_,FEE_CLAIM_OPERATION,fee_,treasuryAddress_) (contracts/builder-protocol/BuildersV2.sol#220)
Reentrancy in BuildersV3.claim(bytes32,address) (contracts/builder-protocol/BuildersV3.sol#200-241):
	External calls:
	- IBuildersTreasury(buildersTreasury).sendRewards(treasuryAddress_,fee_) (contracts/builder-protocol/BuildersV3.sol#233)
	- IBuildersTreasury(buildersTreasury).sendRewards(receiver_,pendingRewards_) (contracts/builder-protocol/BuildersV3.sol#237)
	Event emitted after the call(s):
	- AdminClaimed(builderPoolId_,receiver_,pendingRewards_) (contracts/builder-protocol/BuildersV3.sol#239)
	- FeePaid(user_,FEE_CLAIM_OPERATION,fee_,treasuryAddress_) (contracts/builder-protocol/BuildersV3.sol#240)
Reentrancy in Builders.claim(bytes32,address) (contracts/builder-protocol/old/Builders.sol#183-221):
	External calls:
	- IBuildersTreasury(buildersTreasury).sendRewards(treasuryAddress_,fee_) (contracts/builder-protocol/old/Builders.sol#213)
	- IBuildersTreasury(buildersTreasury).sendRewards(receiver_,pendingRewards_) (contracts/builder-protocol/old/Builders.sol#217)
	Event emitted after the call(s):
	- AdminClaimed(builderPoolId_,receiver_,pendingRewards_) (contracts/builder-protocol/old/Builders.sol#219)
	- FeePaid(user_,FEE_CLAIM_OPERATION,fee_,treasuryAddress_) (contracts/builder-protocol/old/Builders.sol#220)
Reentrancy in Distribution.claim(uint256,address) (contracts/capital-protocol/old/Distribution.sol#154-179):
	External calls:
	- L1Sender(l1Sender).sendMintMessage{value: msg.value}(receiver_,pendingRewards_,user_) (contracts/capital-protocol/old/Distribution.sol#176)
	Event emitted after the call(s):
	- UserClaimed(poolId_,user_,receiver_,pendingRewards_) (contracts/capital-protocol/old/Distribution.sol#178)
Reentrancy in DistributionV2.claim(uint256,address) (contracts/capital-protocol/old/DistributionV2.sol#164-201):
	External calls:
	- L1Sender(l1Sender).sendMintMessage{value: msg.value}(receiver_,pendingRewards_,user_) (contracts/capital-protocol/old/DistributionV2.sol#198)
	Event emitted after the call(s):
	- UserClaimed(poolId_,user_,receiver_,pendingRewards_) (contracts/capital-protocol/old/DistributionV2.sol#200)
Reentrancy in DistributionV3.claim(uint256,address) (contracts/capital-protocol/old/DistributionV3.sol#164-201):
	External calls:
	- L1Sender(l1Sender).sendMintMessage{value: msg.value}(receiver_,pendingRewards_,user_) (contracts/capital-protocol/old/DistributionV3.sol#198)
	Event emitted after the call(s):
	- UserClaimed(poolId_,user_,receiver_,pendingRewards_) (contracts/capital-protocol/old/DistributionV3.sol#200)
Reentrancy in DistributionV4.claim(uint256,address) (contracts/capital-protocol/old/DistributionV4.sol#173-220):
	External calls:
	- L1Sender(l1Sender).sendMintMessage{value: msg.value}(receiver_,pendingRewards_,user_) (contracts/capital-protocol/old/DistributionV4.sol#217)
	Event emitted after the call(s):
	- UserClaimed(poolId_,user_,receiver_,pendingRewards_) (contracts/capital-protocol/old/DistributionV4.sol#219)
Reentrancy in DistributionV5.claim(uint256,address) (contracts/capital-protocol/old/DistributionV5.sol#213-262):
	External calls:
	- IL1Sender(l1Sender).sendMintMessage{value: msg.value}(receiver_,pendingRewards_,user_) (contracts/capital-protocol/old/DistributionV5.sol#259)
	Event emitted after the call(s):
	- UserClaimed(poolId_,user_,receiver_,pendingRewards_) (contracts/capital-protocol/old/DistributionV5.sol#261)
Reentrancy in Distributor.claimAaveRewards(address[],uint256,address,address) (contracts/capital-protocol/Distributor.sol#443-456):
	External calls:
	- claimedAmount = IRewardsController(aaveRewardsController).claimRewards(assets,amount,to,reward) (contracts/capital-protocol/Distributor.sol#453)
	Event emitted after the call(s):
	- AaveRewardsClaimed(assets,amount,to,reward,claimedAmount) (contracts/capital-protocol/Distributor.sol#455)
Reentrancy in DistributionV5.claimReferrerTier(uint256,address) (contracts/capital-protocol/old/DistributionV5.sol#264-290):
	External calls:
	- IL1Sender(l1Sender).sendMintMessage{value: msg.value}(receiver_,pendingRewards_,referrer_) (contracts/capital-protocol/old/DistributionV5.sol#287)
	Event emitted after the call(s):
	- ReferrerClaimed(poolId_,referrer_,receiver_,pendingRewards_) (contracts/capital-protocol/old/DistributionV5.sol#289)
Reentrancy in L2TokenReceiverV2.collectFees(uint256) (contracts/capital-protocol/L2TokenReceiverV2.sol#123-134):
	External calls:
	- (amount0_,amount1_) = INonfungiblePositionManager(nonfungiblePositionManager).collect(params_) (contracts/capital-protocol/L2TokenReceiverV2.sol#131)
	Event emitted after the call(s):
	- FeesCollected(tokenId_,amount0_,amount1_) (contracts/capital-protocol/L2TokenReceiverV2.sol#133)
Reentrancy in L2TokenReceiver.collectFees(uint256) (contracts/capital-protocol/old/L2TokenReceiver.sol#126-137):
	External calls:
	- (amount0_,amount1_) = INonfungiblePositionManager(nonfungiblePositionManager).collect(params_) (contracts/capital-protocol/old/L2TokenReceiver.sol#134)
	Event emitted after the call(s):
	- FeesCollected(tokenId_,amount0_,amount1_) (contracts/capital-protocol/old/L2TokenReceiver.sol#136)
Reentrancy in BuilderSubnets.createSubnet(IBuilderSubnets.Subnet,IBuilderSubnets.SubnetMetadata) (contracts/builder-protocol/BuilderSubnets.sol#167-195):
	External calls:
	- IERC20(token).safeTransferFrom(_msgSender(),subnetCreationFeeTreasury,subnetCreationFeeAmount) (contracts/builder-protocol/BuilderSubnets.sol#187)
	Event emitted after the call(s):
	- SubnetEdited(subnetId_,subnet_) (contracts/builder-protocol/BuilderSubnets.sol#193)
	- SubnetMetadataEdited(subnetId_,metadata_) (contracts/builder-protocol/BuilderSubnets.sol#194)
Reentrancy in BuildersV2.deposit(bytes32,uint256) (contracts/builder-protocol/BuildersV2.sol#129-148):
	External calls:
	- IERC20(depositToken).safeTransferFrom(_msgSender(),address(this),amount_) (contracts/builder-protocol/BuildersV2.sol#142)
	Event emitted after the call(s):
	- UserDeposited(builderPoolId_,user_,amount_) (contracts/builder-protocol/BuildersV2.sol#147)
Reentrancy in BuildersV3.deposit(bytes32,uint256) (contracts/builder-protocol/BuildersV3.sol#143-162):
	External calls:
	- IERC20(depositToken).safeTransferFrom(_msgSender(),address(this),amount_) (contracts/builder-protocol/BuildersV3.sol#156)
	Event emitted after the call(s):
	- UserDeposited(builderPoolId_,user_,amount_) (contracts/builder-protocol/BuildersV3.sol#161)
Reentrancy in Builders.deposit(bytes32,uint256) (contracts/builder-protocol/old/Builders.sol#129-148):
	External calls:
	- IERC20(depositToken).safeTransferFrom(_msgSender(),address(this),amount_) (contracts/builder-protocol/old/Builders.sol#142)
	Event emitted after the call(s):
	- UserDeposited(builderPoolId_,user_,amount_) (contracts/builder-protocol/old/Builders.sol#147)
Reentrancy in L2TokenReceiverV2.increaseLiquidityCurrentRange(uint256,uint256,uint256,uint256,uint256) (contracts/capital-protocol/L2TokenReceiverV2.sol#99-121):
	External calls:
	- (liquidity_,amount0_,amount1_) = INonfungiblePositionManager(nonfungiblePositionManager).increaseLiquidity(params_) (contracts/capital-protocol/L2TokenReceiverV2.sol#116-118)
	Event emitted after the call(s):
	- LiquidityIncreased(tokenId_,amount0_,amount1_,liquidity_,amountMin0_,amountMin1_) (contracts/capital-protocol/L2TokenReceiverV2.sol#120)
Reentrancy in L2TokenReceiver.increaseLiquidityCurrentRange(uint256,uint256,uint256,uint256,uint256) (contracts/capital-protocol/old/L2TokenReceiver.sol#82-124):
	External calls:
	- (liquidity_,amount0_,amount1_) = INonfungiblePositionManager(nonfungiblePositionManager).increaseLiquidity(params_) (contracts/capital-protocol/old/L2TokenReceiver.sol#119-121)
	Event emitted after the call(s):
	- LiquidityIncreased(tokenId_,amount0_,amount1_,liquidity_,amountMin0_,amountMin1_) (contracts/capital-protocol/old/L2TokenReceiver.sol#123)
Reentrancy in DepositPool.lockClaim(uint256,uint128) (contracts/capital-protocol/DepositPool.sol#307-351):
	External calls:
	- IDistributor(distributor).distributeRewards(rewardPoolIndex_) (contracts/capital-protocol/DepositPool.sol#313)
	Event emitted after the call(s):
	- UserClaimLocked(rewardPoolIndex_,user_,claimLockStart_,claimLockEnd_) (contracts/capital-protocol/DepositPool.sol#350)
Reentrancy in DepositPool.migrate(uint256) (contracts/capital-protocol/DepositPool.sol#137-160):
	External calls:
	- IERC20(depositToken).transfer(distributor,remainder_) (contracts/capital-protocol/DepositPool.sol#153)
	- IDistributor(distributor).supply(rewardPoolIndex_,totalDepositedInPublicPools) (contracts/capital-protocol/DepositPool.sol#155)
	Event emitted after the call(s):
	- Migrated(rewardPoolIndex_) (contracts/capital-protocol/DepositPool.sol#159)
Reentrancy in L2MessageReceiver.retryMessage(uint16,bytes,uint64,bytes) (contracts/capital-protocol/old/L2MessageReceiver.sol#52-67):
	External calls:
	- _nonblockingLzReceive(senderChainId_,senderAndReceiverAddresses_,payload_) (contracts/capital-protocol/old/L2MessageReceiver.sol#62)
		- IMOROFT(rewardToken).mint(user_,amount_) (contracts/capital-protocol/old/L2MessageReceiver.sol#105)
	Event emitted after the call(s):
	- RetryMessageSuccess(senderChainId_,senderAndReceiverAddresses_,nonce_,payload_) (contracts/capital-protocol/old/L2MessageReceiver.sol#66)
Reentrancy in L1SenderV2.sendMintMessage(address,uint256,address) (contracts/capital-protocol/L1SenderV2.sol#101-120):
	External calls:
	- ILayerZeroEndpoint(config.gateway).send{value: msg.value}(config.receiverChainId,receiverAndSenderAddresses_,payload_,address(refundTo_),config.zroPaymentAddress,config.adapterParams) (contracts/capital-protocol/L1SenderV2.sol#110-117)
	Event emitted after the call(s):
	- MintMessageSent(user_,amount_) (contracts/capital-protocol/L1SenderV2.sol#119)
Reentrancy in BuildersTreasury.sendRewards(address,uint256) (contracts/builder-protocol/BuildersTreasury.sol#48-54):
	External calls:
	- IERC20(rewardToken).transfer(receiver_,amount_) (contracts/builder-protocol/BuildersTreasury.sol#51)
	Event emitted after the call(s):
	- RewardSent(receiver_,amount_) (contracts/builder-protocol/BuildersTreasury.sol#53)
Reentrancy in L1SenderV2.sendWstETH(uint256,uint256,uint256) (contracts/capital-protocol/L1SenderV2.sol#154-183):
	External calls:
	- IWStETH(config_.wstETH).wrap(stETHBalance_) (contracts/capital-protocol/L1SenderV2.sol#164)
	- res_ = IGatewayRouter(config_.gateway).outboundTransfer{value: msg.value}(config_.wstETH,config_.receiver,amount_,gasLimit_,maxFeePerGas_,data_) (contracts/capital-protocol/L1SenderV2.sol#171-178)
	External calls sending eth:
	- res_ = IGatewayRouter(config_.gateway).outboundTransfer{value: msg.value}(config_.wstETH,config_.receiver,amount_,gasLimit_,maxFeePerGas_,data_) (contracts/capital-protocol/L1SenderV2.sol#171-178)
	Event emitted after the call(s):
	- WstETHSent(amount_,gasLimit_,maxFeePerGas_,maxSubmissionCost_,res_) (contracts/capital-protocol/L1SenderV2.sol#180)
Reentrancy in L1SenderV2.setArbitrumBridgeConfig(IL1SenderV2.ArbitrumBridgeConfig) (contracts/capital-protocol/L1SenderV2.sol#132-152):
	External calls:
	- IERC20(stETH).approve(oldConfig_.wstETH,0) (contracts/capital-protocol/L1SenderV2.sol#139)
	- IERC20(oldConfig_.wstETH).approve(IGatewayRouter(oldConfig_.gateway).getGateway(oldConfig_.wstETH),0) (contracts/capital-protocol/L1SenderV2.sol#140)
	- IERC20(stETH).approve(newConfig_.wstETH,type()(uint256).max) (contracts/capital-protocol/L1SenderV2.sol#143)
	- IERC20(newConfig_.wstETH).approve(IGatewayRouter(newConfig_.gateway).getGateway(newConfig_.wstETH),type()(uint256).max) (contracts/capital-protocol/L1SenderV2.sol#144-147)
	Event emitted after the call(s):
	- ArbitrumBridgeConfigSet(newConfig_) (contracts/capital-protocol/L1SenderV2.sol#151)
Reentrancy in BuildersV3.setBuilderSubnets(address) (contracts/builder-protocol/BuildersV3.sol#408-419):
	External calls:
	- IERC20(depositToken).approve(builderSubnets,0) (contracts/builder-protocol/BuildersV3.sol#412)
	- IERC20(depositToken).approve(value_,type()(uint256).max) (contracts/builder-protocol/BuildersV3.sol#414)
	Event emitted after the call(s):
	- BuilderSubnetsSet(value_) (contracts/builder-protocol/BuildersV3.sol#418)
Reentrancy in DepositPool.setDistributor(address) (contracts/capital-protocol/DepositPool.sol#101-112):
	External calls:
	- IERC20(depositToken).approve(distributor,0) (contracts/capital-protocol/DepositPool.sol#105)
	- IERC20(depositToken).approve(value_,type()(uint256).max) (contracts/capital-protocol/DepositPool.sol#107)
	Event emitted after the call(s):
	- DistributorSet(value_) (contracts/capital-protocol/DepositPool.sol#111)
Reentrancy in BuilderSubnets.stake(bytes32,address,uint256) (contracts/builder-protocol/BuilderSubnets.sol#254-276):
	External calls:
	- IERC20(token).safeTransferFrom(_msgSender(),address(this),amount_) (contracts/builder-protocol/BuilderSubnets.sol#270)
	Event emitted after the call(s):
	- Staked(subnetId_,stakerAddress_,staker) (contracts/builder-protocol/BuilderSubnets.sol#275)
Reentrancy in L2TokenReceiverV2.swap(uint256,uint256,uint256,bool) (contracts/capital-protocol/L2TokenReceiverV2.sol#73-97):
	External calls:
	- amountOut_ = ISwapRouter(router).exactInputSingle(swapParams_) (contracts/capital-protocol/L2TokenReceiverV2.sol#92)
	Event emitted after the call(s):
	- TokensSwapped(params_.tokenIn,params_.tokenOut,amountIn_,amountOut_,amountOutMinimum_) (contracts/capital-protocol/L2TokenReceiverV2.sol#94)
Reentrancy in L2TokenReceiver.swap(uint256,uint256,uint256) (contracts/capital-protocol/old/L2TokenReceiver.sol#57-80):
	External calls:
	- amountOut_ = ISwapRouter(router).exactInputSingle(swapParams_) (contracts/capital-protocol/old/L2TokenReceiver.sol#75)
	Event emitted after the call(s):
	- TokensSwapped(params_.tokenIn,params_.tokenOut,amountIn_,amountOut_,amountOutMinimum_) (contracts/capital-protocol/old/L2TokenReceiver.sol#77)
Reentrancy in L1SenderV2.swapExactInputMultihop(address[],uint24[],uint256,uint256,uint256) (contracts/capital-protocol/L1SenderV2.sol#198-232):
	External calls:
	- TransferHelper.safeApprove(tokens_[0],uniswapSwapRouter,amountIn_) (contracts/capital-protocol/L1SenderV2.sol#209)
	- amountOut_ = ISwapRouter(uniswapSwapRouter).exactInput(params_) (contracts/capital-protocol/L1SenderV2.sol#227)
	Event emitted after the call(s):
	- TokensSwapped(path_,amountIn_,amountOut_) (contracts/capital-protocol/L1SenderV2.sol#229)
Reentrancy in BuilderSubnets.withdraw(bytes32,uint256) (contracts/builder-protocol/BuilderSubnets.sol#278-306):
	External calls:
	- IERC20(token).safeTransfer(treasuryAddress_,fee_) (contracts/builder-protocol/BuilderSubnets.sol#297)
	Event emitted after the call(s):
	- FeePaid(subnetId_,stakerAddress_,fee_,treasuryAddress_) (contracts/builder-protocol/BuilderSubnets.sol#300)
Reentrancy in BuilderSubnets.withdraw(bytes32,uint256) (contracts/builder-protocol/BuilderSubnets.sol#278-306):
	External calls:
	- IERC20(token).safeTransfer(treasuryAddress_,fee_) (contracts/builder-protocol/BuilderSubnets.sol#297)
	- IERC20(token).safeTransfer(stakerAddress_,amount_) (contracts/builder-protocol/BuilderSubnets.sol#303)
	Event emitted after the call(s):
	- Withdrawn(subnetId_,stakerAddress_,staker,amount_) (contracts/builder-protocol/BuilderSubnets.sol#305)
Reentrancy in BuildersV2.withdraw(bytes32,uint256) (contracts/builder-protocol/BuildersV2.sol#150-181):
	External calls:
	- IERC20(depositToken).safeTransfer(treasuryAddress_,fee_) (contracts/builder-protocol/BuildersV2.sol#173)
	- IERC20(depositToken).safeTransfer(user_,amount_) (contracts/builder-protocol/BuildersV2.sol#177)
	Event emitted after the call(s):
	- FeePaid(user_,FEE_WITHDRAW_OPERATION,fee_,treasuryAddress_) (contracts/builder-protocol/BuildersV2.sol#180)
	- UserWithdrawn(builderPoolId_,user_,amount_) (contracts/builder-protocol/BuildersV2.sol#179)
Reentrancy in BuildersV3.withdraw(bytes32,uint256) (contracts/builder-protocol/BuildersV3.sol#164-198):
	External calls:
	- IERC20(depositToken).safeTransfer(treasuryAddress_,fee_) (contracts/builder-protocol/BuildersV3.sol#190)
	- IERC20(depositToken).safeTransfer(user_,amount_) (contracts/builder-protocol/BuildersV3.sol#194)
	Event emitted after the call(s):
	- FeePaid(user_,FEE_WITHDRAW_OPERATION,fee_,treasuryAddress_) (contracts/builder-protocol/BuildersV3.sol#197)
	- UserWithdrawn(builderPoolId_,user_,amount_) (contracts/builder-protocol/BuildersV3.sol#196)
Reentrancy in Builders.withdraw(bytes32,uint256) (contracts/builder-protocol/old/Builders.sol#150-181):
	External calls:
	- IERC20(depositToken).safeTransfer(treasuryAddress_,fee_) (contracts/builder-protocol/old/Builders.sol#173)
	- IERC20(depositToken).safeTransfer(user_,amount_) (contracts/builder-protocol/old/Builders.sol#177)
	Event emitted after the call(s):
	- FeePaid(user_,FEE_WITHDRAW_OPERATION,fee_,treasuryAddress_) (contracts/builder-protocol/old/Builders.sol#180)
	- UserWithdrawn(builderPoolId_,user_,amount_) (contracts/builder-protocol/old/Builders.sol#179)
Reference: https://github.com/crytic/slither/wiki/Detector-Documentation#reentrancy-vulnerabilities-3
INFO:Detectors:
BuilderSubnets.createSubnet(IBuilderSubnets.Subnet,IBuilderSubnets.SubnetMetadata) (contracts/builder-protocol/BuilderSubnets.sol#167-195) uses timestamp for comparisons
	Dangerous comparisons:
	- require(bool,string)(subnet_.startsAt > block.timestamp,BS: invalid starts at timestamp) (contracts/builder-protocol/BuilderSubnets.sol#183)
BuilderSubnets.stake(bytes32,address,uint256) (contracts/builder-protocol/BuilderSubnets.sol#254-276) uses timestamp for comparisons
	Dangerous comparisons:
	- require(bool,string)(block.timestamp >= subnet.startsAt,BS: stake isn't started) (contracts/builder-protocol/BuilderSubnets.sol#263)
BuilderSubnets.withdraw(bytes32,uint256) (contracts/builder-protocol/BuilderSubnets.sol#278-306) uses timestamp for comparisons
	Dangerous comparisons:
	- require(bool,string)(block.timestamp > minAllowedWithdrawalTimestamp_,BS: user withdraw is locked) (contracts/builder-protocol/BuilderSubnets.sol#284)
BuilderSubnets.collectPendingRewards(uint128) (contracts/builder-protocol/BuilderSubnets.sol#346-357) uses timestamp for comparisons
	Dangerous comparisons:
	- require(bool,string)(to_ > allSubnetsData.lastCalculatedTimestamp,BS: `to_` is too low) (contracts/builder-protocol/BuilderSubnets.sol#347)
	- to_ > block.timestamp (contracts/builder-protocol/BuilderSubnets.sol#348)
BuilderSubnets.collectRewardRate(uint256,uint128,uint128) (contracts/builder-protocol/BuilderSubnets.sol#398-433) uses timestamp for comparisons
	Dangerous comparisons:
	- to_ <= from_ (contracts/builder-protocol/BuilderSubnets.sol#402)
	- i <= periods_ (contracts/builder-protocol/BuilderSubnets.sol#411)
	- toForPeriod_ > to_ (contracts/builder-protocol/BuilderSubnets.sol#413)
	- emissionToPeriodEnd_ == 0 (contracts/builder-protocol/BuilderSubnets.sol#418)
	- from_ < rewardCalculationStartsAt (contracts/builder-protocol/BuilderSubnets.sol#401)
BuilderSubnets._getCurrentRewardRate(uint128) (contracts/builder-protocol/BuilderSubnets.sol#447-475) uses timestamp for comparisons
	Dangerous comparisons:
	- block.timestamp < rewardCalculationStartsAt (contracts/builder-protocol/BuilderSubnets.sol#448)
	- totalEmission_ < staked_ (contracts/builder-protocol/BuilderSubnets.sol#464)
	- lastCalculatedTimestamp_ < rewardCalculationStartsAt (contracts/builder-protocol/BuilderSubnets.sol#453-455)
BuildersV2.editBuilderPool(IBuilders.BuilderPool) (contracts/builder-protocol/BuildersV2.sol#109-127) uses timestamp for comparisons
	Dangerous comparisons:
	- require(bool,string)(block.timestamp + editPoolDeadline < poolStart_,BU: pool edit deadline is over) (contracts/builder-protocol/BuildersV2.sol#121)
BuildersV2.deposit(bytes32,uint256) (contracts/builder-protocol/BuildersV2.sol#129-148) uses timestamp for comparisons
	Dangerous comparisons:
	- require(bool,string)(block.timestamp >= builderPool.poolStart,BU: pool isn't started) (contracts/builder-protocol/BuildersV2.sol#135)
BuildersV2.withdraw(bytes32,uint256) (contracts/builder-protocol/BuildersV2.sol#150-181) uses timestamp for comparisons
	Dangerous comparisons:
	- amount_ > userData.deposited (contracts/builder-protocol/BuildersV2.sol#156)
	- require(bool,string)(amount_ > 0,BU: nothing to withdraw) (contracts/builder-protocol/BuildersV2.sol#159)
	- require(bool,string)(block.timestamp > userData.lastDeposit + builderPool.withdrawLockPeriodAfterDeposit,BU: user withdraw is locked) (contracts/builder-protocol/BuildersV2.sol#161-164)
	- fee_ > 0 (contracts/builder-protocol/BuildersV2.sol#172)
BuildersV2.claim(bytes32,address) (contracts/builder-protocol/BuildersV2.sol#183-221) uses timestamp for comparisons
	Dangerous comparisons:
	- require(bool,string)(block.timestamp > builderPool.claimLockEnd,BU: claim is locked) (contracts/builder-protocol/BuildersV2.sol#189)
	- require(bool,string)(pendingRewards_ > 0,BU: nothing to claim) (contracts/builder-protocol/BuildersV2.sol#195)
	- fee_ > 0 (contracts/builder-protocol/BuildersV2.sol#212)
BuildersV2._validateBuilderPool(IBuilders.BuilderPool) (contracts/builder-protocol/BuildersV2.sol#294-299) uses timestamp for comparisons
	Dangerous comparisons:
	- require(bool,string)(builderPool_.poolStart > block.timestamp,BU: invalid pool start value) (contracts/builder-protocol/BuildersV2.sol#298)
BuildersV2._getCurrentRate() (contracts/builder-protocol/BuildersV2.sol#312-320) uses timestamp for comparisons
	Dangerous comparisons:
	- totalPoolData.totalVirtualDeposited == 0 (contracts/builder-protocol/BuildersV2.sol#313)
BuildersV3.editBuilderPool(IBuildersV3.BuilderPool) (contracts/builder-protocol/BuildersV3.sol#123-141) uses timestamp for comparisons
	Dangerous comparisons:
	- require(bool,string)(block.timestamp + editPoolDeadline < poolStart_,BU: pool edit deadline is over) (contracts/builder-protocol/BuildersV3.sol#135)
BuildersV3.deposit(bytes32,uint256) (contracts/builder-protocol/BuildersV3.sol#143-162) uses timestamp for comparisons
	Dangerous comparisons:
	- require(bool,string)(block.timestamp >= builderPool.poolStart,BU: pool isn't started) (contracts/builder-protocol/BuildersV3.sol#149)
BuildersV3.withdraw(bytes32,uint256) (contracts/builder-protocol/BuildersV3.sol#164-198) uses timestamp for comparisons
	Dangerous comparisons:
	- amount_ > userData.deposited (contracts/builder-protocol/BuildersV3.sol#173)
	- require(bool,string)(amount_ > 0,BU: nothing to withdraw) (contracts/builder-protocol/BuildersV3.sol#176)
	- require(bool,string)(block.timestamp > userData.lastDeposit + builderPool.withdrawLockPeriodAfterDeposit,BU: user withdraw is locked) (contracts/builder-protocol/BuildersV3.sol#178-181)
	- fee_ > 0 (contracts/builder-protocol/BuildersV3.sol#189)
BuildersV3.claim(bytes32,address) (contracts/builder-protocol/BuildersV3.sol#200-241) uses timestamp for comparisons
	Dangerous comparisons:
	- require(bool,string)(block.timestamp > builderPool.claimLockEnd,BU: claim is locked) (contracts/builder-protocol/BuildersV3.sol#209)
	- require(bool,string)(pendingRewards_ > 0,BU: nothing to claim) (contracts/builder-protocol/BuildersV3.sol#215)
	- fee_ > 0 (contracts/builder-protocol/BuildersV3.sol#232)
BuildersV3._validateBuilderPool(IBuildersV3.BuilderPool) (contracts/builder-protocol/BuildersV3.sol#314-319) uses timestamp for comparisons
	Dangerous comparisons:
	- require(bool,string)(builderPool_.poolStart > block.timestamp,BU: invalid pool start value) (contracts/builder-protocol/BuildersV3.sol#318)
BuildersV3._getCurrentRate() (contracts/builder-protocol/BuildersV3.sol#332-340) uses timestamp for comparisons
	Dangerous comparisons:
	- totalPoolData.totalVirtualDeposited == 0 (contracts/builder-protocol/BuildersV3.sol#333)
Builders.editBuilderPool(IBuilders.BuilderPool) (contracts/builder-protocol/old/Builders.sol#109-127) uses timestamp for comparisons
	Dangerous comparisons:
	- require(bool,string)(block.timestamp + editPoolDeadline < poolStart_,BU: pool edit deadline is over) (contracts/builder-protocol/old/Builders.sol#121)
Builders.deposit(bytes32,uint256) (contracts/builder-protocol/old/Builders.sol#129-148) uses timestamp for comparisons
	Dangerous comparisons:
	- require(bool,string)(block.timestamp >= builderPool.poolStart,BU: pool isn't started) (contracts/builder-protocol/old/Builders.sol#135)
Builders.withdraw(bytes32,uint256) (contracts/builder-protocol/old/Builders.sol#150-181) uses timestamp for comparisons
	Dangerous comparisons:
	- amount_ > userData.deposited (contracts/builder-protocol/old/Builders.sol#156)
	- require(bool,string)(amount_ > 0,BU: nothing to withdraw) (contracts/builder-protocol/old/Builders.sol#159)
	- require(bool,string)(block.timestamp > userData.lastDeposit + builderPool.withdrawLockPeriodAfterDeposit,BU: user withdraw is locked) (contracts/builder-protocol/old/Builders.sol#161-164)
	- fee_ > 0 (contracts/builder-protocol/old/Builders.sol#172)
Builders.claim(bytes32,address) (contracts/builder-protocol/old/Builders.sol#183-221) uses timestamp for comparisons
	Dangerous comparisons:
	- require(bool,string)(block.timestamp > builderPool.claimLockEnd,BU: claim is locked) (contracts/builder-protocol/old/Builders.sol#189)
	- require(bool,string)(pendingRewards_ > 0,BU: nothing to claim) (contracts/builder-protocol/old/Builders.sol#195)
	- fee_ > 0 (contracts/builder-protocol/old/Builders.sol#212)
Builders._validateBuilderPool(IBuilders.BuilderPool) (contracts/builder-protocol/old/Builders.sol#294-299) uses timestamp for comparisons
	Dangerous comparisons:
	- require(bool,string)(builderPool_.poolStart > block.timestamp,BU: invalid pool start value) (contracts/builder-protocol/old/Builders.sol#298)
Builders._getCurrentRate() (contracts/builder-protocol/old/Builders.sol#312-320) uses timestamp for comparisons
	Dangerous comparisons:
	- totalPoolData.totalVirtualDeposited == 0 (contracts/builder-protocol/old/Builders.sol#313)
ChainLinkDataConsumer.getChainLinkDataFeedLatestAnswer(bytes32) (contracts/capital-protocol/ChainLinkDataConsumer.sol#78-107) uses timestamp for comparisons
	Dangerous comparisons:
	- block.timestamp < updatedAt_ || block.timestamp - updatedAt_ > allowedPriceUpdateDelay (contracts/capital-protocol/ChainLinkDataConsumer.sol#91)
DepositPool.lockClaim(uint256,uint128) (contracts/capital-protocol/DepositPool.sol#307-351) uses timestamp for comparisons
	Dangerous comparisons:
	- require(bool,string)(claimLockEnd_ > block.timestamp,DS: invalid lock end value (1)) (contracts/capital-protocol/DepositPool.sol#311)
	- require(bool,string)(userData.deposited > 0,DS: user isn't staked) (contracts/capital-protocol/DepositPool.sol#321)
	- require(bool,string)(claimLockEnd_ > userData.claimLockEnd,DS: invalid lock end value (2)) (contracts/capital-protocol/DepositPool.sol#322)
DepositPool._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/DepositPool.sol#353-432) uses timestamp for comparisons
	Dangerous comparisons:
	- claimLockEnd_ == 0 (contracts/capital-protocol/DepositPool.sol#367)
	- require(bool,string)(claimLockEnd_ >= userData.claimLockEnd,DS: invalid claim lock end) (contracts/capital-protocol/DepositPool.sol#370)
	- require(bool,string)(userData.deposited + amount_ >= rewardPoolProtocolDetails.minimalStake,DS: amount too low) (contracts/capital-protocol/DepositPool.sol#388)
	- userData.claimLockEnd > block.timestamp (contracts/capital-protocol/DepositPool.sol#368)
DepositPool._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/DepositPool.sol#434-511) uses timestamp for comparisons
	Dangerous comparisons:
	- require(bool,string)(block.timestamp > userData.lastStake + rewardPoolProtocolDetails.withdrawLockPeriodAfterStake,DS: pool withdraw is locked) (contracts/capital-protocol/DepositPool.sol#450-453)
DepositPool._claim(uint256,address,address) (contracts/capital-protocol/DepositPool.sol#513-574) uses timestamp for comparisons
	Dangerous comparisons:
	- require(bool,string)(block.timestamp > userData.lastStake + rewardPoolsProtocolDetails[rewardPoolIndex_].claimLockPeriodAfterStake,DS: pool claim is locked (S)) (contracts/capital-protocol/DepositPool.sol#519-523)
	- require(bool,string)(block.timestamp > userData.lastClaim + rewardPoolsProtocolDetails[rewardPoolIndex_].claimLockPeriodAfterClaim,DS: pool claim is locked (C)) (contracts/capital-protocol/DepositPool.sol#524-528)
	- require(bool,string)(block.timestamp > userData.claimLockEnd,DS: user claim is locked) (contracts/capital-protocol/DepositPool.sol#529)
DepositPool._claimReferrerTier(uint256,address,address) (contracts/capital-protocol/DepositPool.sol#576-611) uses timestamp for comparisons
	Dangerous comparisons:
	- require(bool,string)(block.timestamp > referrerData.lastClaim + rewardPoolProtocolDetails.claimLockPeriodAfterClaim,DS: pool claim is locked (C)) (contracts/capital-protocol/DepositPool.sol#587-590)
DistributionV6.createPool(IDistributionV5.Pool) (contracts/capital-protocol/DistributionV6.sol#93-100) uses timestamp for comparisons
	Dangerous comparisons:
	- require(bool,string)(pool_.payoutStart > block.timestamp,DS: invalid payout start value) (contracts/capital-protocol/DistributionV6.sol#94)
DistributionV6._claim(uint256,address,address) (contracts/capital-protocol/DistributionV6.sol#257-304) uses timestamp for comparisons
	Dangerous comparisons:
	- require(bool,string)(block.timestamp > pool.payoutStart + pool.claimLockPeriod,DS: pool claim is locked (1)) (contracts/capital-protocol/DistributionV6.sol#263)
	- require(bool,string)(block.timestamp > userData.lastStake + poolLimits.claimLockPeriodAfterStake,DS: pool claim is locked (S)) (contracts/capital-protocol/DistributionV6.sol#264-267)
	- require(bool,string)(block.timestamp > userData.lastClaim + poolLimits.claimLockPeriodAfterClaim,DS: pool claim is locked (C)) (contracts/capital-protocol/DistributionV6.sol#268-271)
	- require(bool,string)(block.timestamp > userData.claimLockEnd,DS: user claim is locked) (contracts/capital-protocol/DistributionV6.sol#272)
	- require(bool,string)(pendingRewards_ > 0,DS: nothing to claim) (contracts/capital-protocol/DistributionV6.sol#276)
DistributionV6._claimReferrerTier(uint256,address,address) (contracts/capital-protocol/DistributionV6.sol#320-344) uses timestamp for comparisons
	Dangerous comparisons:
	- require(bool,string)(block.timestamp > pool.payoutStart + pool.claimLockPeriod,DS: pool claim is locked) (contracts/capital-protocol/DistributionV6.sol#327)
	- require(bool,string)(block.timestamp > referrerData.lastClaim + poolLimits.claimLockPeriodAfterClaim,DS: pool claim is locked (C)) (contracts/capital-protocol/DistributionV6.sol#328-331)
DistributionV6.lockClaim(uint256,uint128) (contracts/capital-protocol/DistributionV6.sol#350-384) uses timestamp for comparisons
	Dangerous comparisons:
	- require(bool,string)(claimLockEnd_ > block.timestamp,DS: invalid lock end value (1)) (contracts/capital-protocol/DistributionV6.sol#351)
	- require(bool,string)(userData.deposited > 0,DS: user isn't staked) (contracts/capital-protocol/DistributionV6.sol#359)
	- require(bool,string)(claimLockEnd_ > userData.claimLockEnd,DS: invalid lock end value (2)) (contracts/capital-protocol/DistributionV6.sol#360)
	- userData.virtualDeposited == 0 (contracts/capital-protocol/DistributionV6.sol#368)
	- userData.claimLockStart > 0 (contracts/capital-protocol/DistributionV6.sol#364)
DistributionV6._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/DistributionV6.sol#405-477) uses timestamp for comparisons
	Dangerous comparisons:
	- claimLockEnd_ == 0 (contracts/capital-protocol/DistributionV6.sol#417)
	- require(bool,string)(claimLockEnd_ >= userData.claimLockEnd,DS: invalid claim lock end) (contracts/capital-protocol/DistributionV6.sol#420)
	- require(bool,string)(userData.deposited + amount_ >= pool.minimalStake,DS: amount too low) (contracts/capital-protocol/DistributionV6.sol#436)
	- userData.virtualDeposited == 0 (contracts/capital-protocol/DistributionV6.sol#447)
	- userData.claimLockEnd > block.timestamp (contracts/capital-protocol/DistributionV6.sol#418)
DistributionV6._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/DistributionV6.sol#479-557) uses timestamp for comparisons
	Dangerous comparisons:
	- require(bool,string)(block.timestamp < pool.payoutStart || (block.timestamp > pool.payoutStart + pool.withdrawLockPeriod && block.timestamp > userData.lastStake + pool.withdrawLockPeriodAfterStake),DS: pool withdraw is locked) (contracts/capital-protocol/DistributionV6.sol#493-498)
	- userData.virtualDeposited == 0 (contracts/capital-protocol/DistributionV6.sol#525)
DistributionV6._getClaimLockPeriodMultiplier(uint128,uint128) (contracts/capital-protocol/DistributionV6.sol#692-717) uses timestamp for comparisons
	Dangerous comparisons:
	- start_ >= end_ (contracts/capital-protocol/DistributionV6.sol#705)
	- end_ > periodEnd_ (contracts/capital-protocol/DistributionV6.sol#702)
	- start_ < periodStart_ (contracts/capital-protocol/DistributionV6.sol#703)
	- multiplier_ > maximalMultiplier_ (contracts/capital-protocol/DistributionV6.sol#713)
	- multiplier_ < minimalMultiplier_ (contracts/capital-protocol/DistributionV6.sol#714)
Distributor.setRewardPoolLastCalculatedTimestamp(uint256,uint128) (contracts/capital-protocol/Distributor.sol#179-186) uses timestamp for comparisons
	Dangerous comparisons:
	- require(bool,string)(value_ <= block.timestamp,DR: invalid last calculated timestamp) (contracts/capital-protocol/Distributor.sol#181)
Distributor.distributeRewards(uint256) (contracts/capital-protocol/Distributor.sol#330-410) uses timestamp for comparisons
	Dangerous comparisons:
	- require(bool,string)(lastCalculatedTimestamp_ != 0,DR: `rewardPoolLastCalculatedTimestamp` isn't set) (contracts/capital-protocol/Distributor.sol#336)
	- rewards_ == 0 (contracts/capital-protocol/Distributor.sol#346)
	- block.timestamp <= lastCalculatedTimestamp_ + minRewardsDistributePeriod (contracts/capital-protocol/Distributor.sol#360)
Distributor.withdrawUndistributedRewards(address,address) (contracts/capital-protocol/Distributor.sol#426-432) uses timestamp for comparisons
	Dangerous comparisons:
	- require(bool,string)(undistributedRewards > 0,DR: nothing to withdraw) (contracts/capital-protocol/Distributor.sol#427)
Distribution.createPool(IDistribution.Pool) (contracts/capital-protocol/old/Distribution.sol#73-80) uses timestamp for comparisons
	Dangerous comparisons:
	- require(bool,string)(pool_.payoutStart > block.timestamp,DS: invalid payout start value) (contracts/capital-protocol/old/Distribution.sol#74)
Distribution.claim(uint256,address) (contracts/capital-protocol/old/Distribution.sol#154-179) uses timestamp for comparisons
	Dangerous comparisons:
	- require(bool,string)(block.timestamp > pool.payoutStart + pool.claimLockPeriod,DS: pool claim is locked) (contracts/capital-protocol/old/Distribution.sol#161)
	- require(bool,string)(pendingRewards_ > 0,DS: nothing to claim) (contracts/capital-protocol/old/Distribution.sol#165)
Distribution._stake(address,uint256,uint256,uint256) (contracts/capital-protocol/old/Distribution.sol#196-229) uses timestamp for comparisons
	Dangerous comparisons:
	- require(bool,string)(userData.deposited + amount_ >= pool.minimalStake,DS: amount too low) (contracts/capital-protocol/old/Distribution.sol#211)
Distribution._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/Distribution.sol#231-284) uses timestamp for comparisons
	Dangerous comparisons:
	- require(bool,string)(block.timestamp < pool.payoutStart || (block.timestamp > pool.payoutStart + pool.withdrawLockPeriod && block.timestamp > userData.lastStake + pool.withdrawLockPeriodAfterStake),DS: pool withdraw is locked) (contracts/capital-protocol/old/Distribution.sol#245-250)
DistributionV2.createPool(IDistributionV2.Pool) (contracts/capital-protocol/old/DistributionV2.sol#77-84) uses timestamp for comparisons
	Dangerous comparisons:
	- require(bool,string)(pool_.payoutStart > block.timestamp,DS: invalid payout start value) (contracts/capital-protocol/old/DistributionV2.sol#78)
DistributionV2.claim(uint256,address) (contracts/capital-protocol/old/DistributionV2.sol#164-201) uses timestamp for comparisons
	Dangerous comparisons:
	- require(bool,string)(block.timestamp > pool.payoutStart + pool.claimLockPeriod,DS: pool claim is locked) (contracts/capital-protocol/old/DistributionV2.sol#171)
	- require(bool,string)(block.timestamp > userData.claimLockEnd,DS: user claim is locked) (contracts/capital-protocol/old/DistributionV2.sol#172)
	- require(bool,string)(pendingRewards_ > 0,DS: nothing to claim) (contracts/capital-protocol/old/DistributionV2.sol#176)
DistributionV2.lockClaim(uint256,uint128) (contracts/capital-protocol/old/DistributionV2.sol#207-241) uses timestamp for comparisons
	Dangerous comparisons:
	- require(bool,string)(claimLockEnd_ > block.timestamp,DS: invalid lock end value (1)) (contracts/capital-protocol/old/DistributionV2.sol#208)
	- require(bool,string)(userData.deposited > 0,DS: user isn't staked) (contracts/capital-protocol/old/DistributionV2.sol#216)
	- require(bool,string)(claimLockEnd_ > userData.claimLockEnd,DS: invalid lock end value (2)) (contracts/capital-protocol/old/DistributionV2.sol#217)
	- userData.virtualDeposited == 0 (contracts/capital-protocol/old/DistributionV2.sol#225)
	- userData.claimLockStart > 0 (contracts/capital-protocol/old/DistributionV2.sol#221)
DistributionV2._stake(address,uint256,uint256,uint256,uint128) (contracts/capital-protocol/old/DistributionV2.sol#254-310) uses timestamp for comparisons
	Dangerous comparisons:
	- claimLockEnd_ == 0 (contracts/capital-protocol/old/DistributionV2.sol#265)
	- require(bool,string)(claimLockEnd_ >= userData.claimLockEnd,DS: invalid claim lock end) (contracts/capital-protocol/old/DistributionV2.sol#271)
	- require(bool,string)(userData.deposited + amount_ >= pool.minimalStake,DS: amount too low) (contracts/capital-protocol/old/DistributionV2.sol#280)
	- userData.virtualDeposited == 0 (contracts/capital-protocol/old/DistributionV2.sol#291)
	- userData.claimLockEnd > block.timestamp (contracts/capital-protocol/old/DistributionV2.sol#266)
DistributionV2._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV2.sol#312-373) uses timestamp for comparisons
	Dangerous comparisons:
	- require(bool,string)(block.timestamp < pool.payoutStart || (block.timestamp > pool.payoutStart + pool.withdrawLockPeriod && block.timestamp > userData.lastStake + pool.withdrawLockPeriodAfterStake),DS: pool withdraw is locked) (contracts/capital-protocol/old/DistributionV2.sol#326-331)
	- userData.virtualDeposited == 0 (contracts/capital-protocol/old/DistributionV2.sol#351)
DistributionV2._getClaimLockPeriodMultiplier(uint128,uint128) (contracts/capital-protocol/old/DistributionV2.sol#435-460) uses timestamp for comparisons
	Dangerous comparisons:
	- start_ >= end_ (contracts/capital-protocol/old/DistributionV2.sol#448)
	- end_ > periodEnd_ (contracts/capital-protocol/old/DistributionV2.sol#445)
	- start_ < periodStart_ (contracts/capital-protocol/old/DistributionV2.sol#446)
	- multiplier_ > maximalMultiplier_ (contracts/capital-protocol/old/DistributionV2.sol#456)
	- multiplier_ < minimalMultiplier_ (contracts/capital-protocol/old/DistributionV2.sol#457)
DistributionV3.createPool(IDistributionV3.Pool) (contracts/capital-protocol/old/DistributionV3.sol#77-84) uses timestamp for comparisons
	Dangerous comparisons:
	- require(bool,string)(pool_.payoutStart > block.timestamp,DS: invalid payout start value) (contracts/capital-protocol/old/DistributionV3.sol#78)
DistributionV3.claim(uint256,address) (contracts/capital-protocol/old/DistributionV3.sol#164-201) uses timestamp for comparisons
	Dangerous comparisons:
	- require(bool,string)(block.timestamp > pool.payoutStart + pool.claimLockPeriod,DS: pool claim is locked) (contracts/capital-protocol/old/DistributionV3.sol#171)
	- require(bool,string)(block.timestamp > userData.claimLockEnd,DS: user claim is locked) (contracts/capital-protocol/old/DistributionV3.sol#172)
	- require(bool,string)(pendingRewards_ > 0,DS: nothing to claim) (contracts/capital-protocol/old/DistributionV3.sol#176)
DistributionV3.lockClaim(uint256,uint128) (contracts/capital-protocol/old/DistributionV3.sol#207-241) uses timestamp for comparisons
	Dangerous comparisons:
	- require(bool,string)(claimLockEnd_ > block.timestamp,DS: invalid lock end value (1)) (contracts/capital-protocol/old/DistributionV3.sol#208)
	- require(bool,string)(userData.deposited > 0,DS: user isn't staked) (contracts/capital-protocol/old/DistributionV3.sol#216)
	- require(bool,string)(claimLockEnd_ > userData.claimLockEnd,DS: invalid lock end value (2)) (contracts/capital-protocol/old/DistributionV3.sol#217)
	- userData.virtualDeposited == 0 (contracts/capital-protocol/old/DistributionV3.sol#225)
	- userData.claimLockStart > 0 (contracts/capital-protocol/old/DistributionV3.sol#221)
DistributionV3._stake(address,uint256,uint256,uint256,uint128) (contracts/capital-protocol/old/DistributionV3.sol#254-310) uses timestamp for comparisons
	Dangerous comparisons:
	- claimLockEnd_ == 0 (contracts/capital-protocol/old/DistributionV3.sol#265)
	- require(bool,string)(claimLockEnd_ >= userData.claimLockEnd,DS: invalid claim lock end) (contracts/capital-protocol/old/DistributionV3.sol#268)
	- require(bool,string)(userData.deposited + amount_ >= pool.minimalStake,DS: amount too low) (contracts/capital-protocol/old/DistributionV3.sol#280)
	- userData.virtualDeposited == 0 (contracts/capital-protocol/old/DistributionV3.sol#291)
	- userData.claimLockEnd > block.timestamp (contracts/capital-protocol/old/DistributionV3.sol#266)
DistributionV3._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV3.sol#312-373) uses timestamp for comparisons
	Dangerous comparisons:
	- require(bool,string)(block.timestamp < pool.payoutStart || (block.timestamp > pool.payoutStart + pool.withdrawLockPeriod && block.timestamp > userData.lastStake + pool.withdrawLockPeriodAfterStake),DS: pool withdraw is locked) (contracts/capital-protocol/old/DistributionV3.sol#326-331)
	- userData.virtualDeposited == 0 (contracts/capital-protocol/old/DistributionV3.sol#351)
DistributionV3._getClaimLockPeriodMultiplier(uint128,uint128) (contracts/capital-protocol/old/DistributionV3.sol#435-460) uses timestamp for comparisons
	Dangerous comparisons:
	- start_ >= end_ (contracts/capital-protocol/old/DistributionV3.sol#448)
	- end_ > periodEnd_ (contracts/capital-protocol/old/DistributionV3.sol#445)
	- start_ < periodStart_ (contracts/capital-protocol/old/DistributionV3.sol#446)
	- multiplier_ > maximalMultiplier_ (contracts/capital-protocol/old/DistributionV3.sol#456)
	- multiplier_ < minimalMultiplier_ (contracts/capital-protocol/old/DistributionV3.sol#457)
DistributionV4.createPool(IDistributionV4.Pool) (contracts/capital-protocol/old/DistributionV4.sol#80-87) uses timestamp for comparisons
	Dangerous comparisons:
	- require(bool,string)(pool_.payoutStart > block.timestamp,DS: invalid payout start value) (contracts/capital-protocol/old/DistributionV4.sol#81)
DistributionV4.claim(uint256,address) (contracts/capital-protocol/old/DistributionV4.sol#173-220) uses timestamp for comparisons
	Dangerous comparisons:
	- require(bool,string)(block.timestamp > pool.payoutStart + pool.claimLockPeriod,DS: pool claim is locked (1)) (contracts/capital-protocol/old/DistributionV4.sol#181)
	- require(bool,string)(block.timestamp > userData.lastStake + poolLimits.claimLockPeriodAfterStake,DS: pool claim is locked (S)) (contracts/capital-protocol/old/DistributionV4.sol#182-185)
	- require(bool,string)(block.timestamp > userData.lastClaim + poolLimits.claimLockPeriodAfterClaim,DS: pool claim is locked (C)) (contracts/capital-protocol/old/DistributionV4.sol#186-189)
	- require(bool,string)(block.timestamp > userData.claimLockEnd,DS: user claim is locked) (contracts/capital-protocol/old/DistributionV4.sol#190)
	- require(bool,string)(pendingRewards_ > 0,DS: nothing to claim) (contracts/capital-protocol/old/DistributionV4.sol#194)
DistributionV4.lockClaim(uint256,uint128) (contracts/capital-protocol/old/DistributionV4.sol#226-260) uses timestamp for comparisons
	Dangerous comparisons:
	- require(bool,string)(claimLockEnd_ > block.timestamp,DS: invalid lock end value (1)) (contracts/capital-protocol/old/DistributionV4.sol#227)
	- require(bool,string)(userData.deposited > 0,DS: user isn't staked) (contracts/capital-protocol/old/DistributionV4.sol#235)
	- require(bool,string)(claimLockEnd_ > userData.claimLockEnd,DS: invalid lock end value (2)) (contracts/capital-protocol/old/DistributionV4.sol#236)
	- userData.virtualDeposited == 0 (contracts/capital-protocol/old/DistributionV4.sol#244)
	- userData.claimLockStart > 0 (contracts/capital-protocol/old/DistributionV4.sol#240)
DistributionV4._stake(address,uint256,uint256,uint256,uint128) (contracts/capital-protocol/old/DistributionV4.sol#273-329) uses timestamp for comparisons
	Dangerous comparisons:
	- claimLockEnd_ == 0 (contracts/capital-protocol/old/DistributionV4.sol#284)
	- require(bool,string)(claimLockEnd_ >= userData.claimLockEnd,DS: invalid claim lock end) (contracts/capital-protocol/old/DistributionV4.sol#287)
	- require(bool,string)(userData.deposited + amount_ >= pool.minimalStake,DS: amount too low) (contracts/capital-protocol/old/DistributionV4.sol#299)
	- userData.virtualDeposited == 0 (contracts/capital-protocol/old/DistributionV4.sol#310)
	- userData.claimLockEnd > block.timestamp (contracts/capital-protocol/old/DistributionV4.sol#285)
DistributionV4._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV4.sol#331-392) uses timestamp for comparisons
	Dangerous comparisons:
	- require(bool,string)(block.timestamp < pool.payoutStart || (block.timestamp > pool.payoutStart + pool.withdrawLockPeriod && block.timestamp > userData.lastStake + pool.withdrawLockPeriodAfterStake),DS: pool withdraw is locked) (contracts/capital-protocol/old/DistributionV4.sol#345-350)
	- userData.virtualDeposited == 0 (contracts/capital-protocol/old/DistributionV4.sol#370)
DistributionV4._getClaimLockPeriodMultiplier(uint128,uint128) (contracts/capital-protocol/old/DistributionV4.sol#454-479) uses timestamp for comparisons
	Dangerous comparisons:
	- start_ >= end_ (contracts/capital-protocol/old/DistributionV4.sol#467)
	- end_ > periodEnd_ (contracts/capital-protocol/old/DistributionV4.sol#464)
	- start_ < periodStart_ (contracts/capital-protocol/old/DistributionV4.sol#465)
	- multiplier_ > maximalMultiplier_ (contracts/capital-protocol/old/DistributionV4.sol#475)
	- multiplier_ < minimalMultiplier_ (contracts/capital-protocol/old/DistributionV4.sol#476)
DistributionV5.createPool(IDistributionV5.Pool) (contracts/capital-protocol/old/DistributionV5.sol#87-94) uses timestamp for comparisons
	Dangerous comparisons:
	- require(bool,string)(pool_.payoutStart > block.timestamp,DS: invalid payout start value) (contracts/capital-protocol/old/DistributionV5.sol#88)
DistributionV5.claim(uint256,address) (contracts/capital-protocol/old/DistributionV5.sol#213-262) uses timestamp for comparisons
	Dangerous comparisons:
	- require(bool,string)(block.timestamp > pool.payoutStart + pool.claimLockPeriod,DS: pool claim is locked (1)) (contracts/capital-protocol/old/DistributionV5.sol#221)
	- require(bool,string)(block.timestamp > userData.lastStake + poolLimits.claimLockPeriodAfterStake,DS: pool claim is locked (S)) (contracts/capital-protocol/old/DistributionV5.sol#222-225)
	- require(bool,string)(block.timestamp > userData.lastClaim + poolLimits.claimLockPeriodAfterClaim,DS: pool claim is locked (C)) (contracts/capital-protocol/old/DistributionV5.sol#226-229)
	- require(bool,string)(block.timestamp > userData.claimLockEnd,DS: user claim is locked) (contracts/capital-protocol/old/DistributionV5.sol#230)
	- require(bool,string)(pendingRewards_ > 0,DS: nothing to claim) (contracts/capital-protocol/old/DistributionV5.sol#234)
DistributionV5.claimReferrerTier(uint256,address) (contracts/capital-protocol/old/DistributionV5.sol#264-290) uses timestamp for comparisons
	Dangerous comparisons:
	- require(bool,string)(block.timestamp > pool.payoutStart + pool.claimLockPeriod,DS: pool claim is locked) (contracts/capital-protocol/old/DistributionV5.sol#273)
	- require(bool,string)(block.timestamp > referrerData.lastClaim + poolLimits.claimLockPeriodAfterClaim,DS: pool claim is locked (C)) (contracts/capital-protocol/old/DistributionV5.sol#274-277)
DistributionV5.lockClaim(uint256,uint128) (contracts/capital-protocol/old/DistributionV5.sol#296-330) uses timestamp for comparisons
	Dangerous comparisons:
	- require(bool,string)(claimLockEnd_ > block.timestamp,DS: invalid lock end value (1)) (contracts/capital-protocol/old/DistributionV5.sol#297)
	- require(bool,string)(userData.deposited > 0,DS: user isn't staked) (contracts/capital-protocol/old/DistributionV5.sol#305)
	- require(bool,string)(claimLockEnd_ > userData.claimLockEnd,DS: invalid lock end value (2)) (contracts/capital-protocol/old/DistributionV5.sol#306)
	- userData.virtualDeposited == 0 (contracts/capital-protocol/old/DistributionV5.sol#314)
	- userData.claimLockStart > 0 (contracts/capital-protocol/old/DistributionV5.sol#310)
DistributionV5._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/old/DistributionV5.sol#351-423) uses timestamp for comparisons
	Dangerous comparisons:
	- claimLockEnd_ == 0 (contracts/capital-protocol/old/DistributionV5.sol#363)
	- require(bool,string)(claimLockEnd_ >= userData.claimLockEnd,DS: invalid claim lock end) (contracts/capital-protocol/old/DistributionV5.sol#366)
	- require(bool,string)(userData.deposited + amount_ >= pool.minimalStake,DS: amount too low) (contracts/capital-protocol/old/DistributionV5.sol#382)
	- userData.virtualDeposited == 0 (contracts/capital-protocol/old/DistributionV5.sol#393)
	- userData.claimLockEnd > block.timestamp (contracts/capital-protocol/old/DistributionV5.sol#364)
DistributionV5._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV5.sol#425-503) uses timestamp for comparisons
	Dangerous comparisons:
	- require(bool,string)(block.timestamp < pool.payoutStart || (block.timestamp > pool.payoutStart + pool.withdrawLockPeriod && block.timestamp > userData.lastStake + pool.withdrawLockPeriodAfterStake),DS: pool withdraw is locked) (contracts/capital-protocol/old/DistributionV5.sol#439-444)
	- userData.virtualDeposited == 0 (contracts/capital-protocol/old/DistributionV5.sol#471)
DistributionV5._getClaimLockPeriodMultiplier(uint128,uint128) (contracts/capital-protocol/old/DistributionV5.sol#638-663) uses timestamp for comparisons
	Dangerous comparisons:
	- start_ >= end_ (contracts/capital-protocol/old/DistributionV5.sol#651)
	- end_ > periodEnd_ (contracts/capital-protocol/old/DistributionV5.sol#648)
	- start_ < periodStart_ (contracts/capital-protocol/old/DistributionV5.sol#649)
	- multiplier_ > maximalMultiplier_ (contracts/capital-protocol/old/DistributionV5.sol#659)
	- multiplier_ < minimalMultiplier_ (contracts/capital-protocol/old/DistributionV5.sol#660)
Reference: https://github.com/crytic/slither/wiki/Detector-Documentation#block-timestamp
INFO:Detectors:
L2MessageReceiver._nonblockingLzReceive(uint16,bytes,bytes) (contracts/capital-protocol/old/L2MessageReceiver.sol#90-106) uses assembly
	- INLINE ASM (contracts/capital-protocol/old/L2MessageReceiver.sol#98-100)
Reference: https://github.com/crytic/slither/wiki/Detector-Documentation#assembly-usage
INFO:Detectors:
BuilderSubnets.createSubnet(IBuilderSubnets.Subnet,IBuilderSubnets.SubnetMetadata) (contracts/builder-protocol/BuilderSubnets.sol#167-195) compares to a boolean constant:
	-isMigrationOver != true (contracts/builder-protocol/BuilderSubnets.sol#170)
DepositPool.lockClaim(uint256,uint128) (contracts/capital-protocol/DepositPool.sol#307-351) compares to a boolean constant:
	-require(bool,string)(isMigrationOver == true,DS: migration isn't over) (contracts/capital-protocol/DepositPool.sol#308)
DepositPool._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/DepositPool.sol#353-432) compares to a boolean constant:
	-require(bool,string)(isMigrationOver == true,DS: migration isn't over) (contracts/capital-protocol/DepositPool.sol#361)
DepositPool._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/DepositPool.sol#434-511) compares to a boolean constant:
	-require(bool,string)(isMigrationOver == true,DS: migration isn't over) (contracts/capital-protocol/DepositPool.sol#435)
DepositPool._claim(uint256,address,address) (contracts/capital-protocol/DepositPool.sol#513-574) compares to a boolean constant:
	-require(bool,string)(isMigrationOver == true,DS: migration isn't over) (contracts/capital-protocol/DepositPool.sol#514)
DepositPool._claimReferrerTier(uint256,address,address) (contracts/capital-protocol/DepositPool.sol#576-611) compares to a boolean constant:
	-require(bool,string)(isMigrationOver == true,DS: migration isn't over) (contracts/capital-protocol/DepositPool.sol#577)
Reference: https://github.com/crytic/slither/wiki/Detector-Documentation#boolean-equality
INFO:Detectors:
BuildersV3._migrateUserStake(bytes32,address) (contracts/builder-protocol/BuildersV3.sol#447-457) has costly operations inside a loop:
	- totalDepositsMigrated += userData.deposited (contracts/builder-protocol/BuildersV3.sol#454)
	Calls stack containing the loop:
		BuildersV3.migrateUsersStake(bytes32[],address[])
DepositPool._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/DepositPool.sol#353-432) has costly operations inside a loop:
	- totalDepositedInPublicPools += amount_ (contracts/capital-protocol/DepositPool.sol#390)
	Calls stack containing the loop:
		DepositPool.manageUsersInPrivateRewardPool(uint256,address[],uint256[],uint128[],address[])
DepositPool._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/DepositPool.sol#434-511) has costly operations inside a loop:
	- totalDepositedInPublicPools -= amount_ (contracts/capital-protocol/DepositPool.sol#504)
	Calls stack containing the loop:
		DepositPool.manageUsersInPrivateRewardPool(uint256,address[],uint256[],uint128[],address[])
DistributionV6._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/DistributionV6.sol#405-477) has costly operations inside a loop:
	- totalDepositedInPublicPools += amount_ (contracts/capital-protocol/DistributionV6.sol#438)
	Calls stack containing the loop:
		DistributionV6.manageUsersInPrivatePool(uint256,address[],uint256[],uint128[],address[])
DistributionV6._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/DistributionV6.sol#479-557) has costly operations inside a loop:
	- totalDepositedInPublicPools -= amount_ (contracts/capital-protocol/DistributionV6.sol#551)
	Calls stack containing the loop:
		DistributionV6.manageUsersInPrivatePool(uint256,address[],uint256[],uint128[],address[])
Distribution._stake(address,uint256,uint256,uint256) (contracts/capital-protocol/old/Distribution.sol#196-229) has costly operations inside a loop:
	- totalDepositedInPublicPools += amount_ (contracts/capital-protocol/old/Distribution.sol#213)
	Calls stack containing the loop:
		Distribution.manageUsersInPrivatePool(uint256,address[],uint256[])
Distribution._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/Distribution.sol#231-284) has costly operations inside a loop:
	- totalDepositedInPublicPools -= amount_ (contracts/capital-protocol/old/Distribution.sol#278)
	Calls stack containing the loop:
		Distribution.manageUsersInPrivatePool(uint256,address[],uint256[])
DistributionV2._stake(address,uint256,uint256,uint256,uint128) (contracts/capital-protocol/old/DistributionV2.sol#254-310) has costly operations inside a loop:
	- totalDepositedInPublicPools += amount_ (contracts/capital-protocol/old/DistributionV2.sol#282)
	Calls stack containing the loop:
		DistributionV2.manageUsersInPrivatePool(uint256,address[],uint256[],uint128[])
DistributionV2._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV2.sol#312-373) has costly operations inside a loop:
	- totalDepositedInPublicPools -= amount_ (contracts/capital-protocol/old/DistributionV2.sol#367)
	Calls stack containing the loop:
		DistributionV2.manageUsersInPrivatePool(uint256,address[],uint256[],uint128[])
DistributionV3._stake(address,uint256,uint256,uint256,uint128) (contracts/capital-protocol/old/DistributionV3.sol#254-310) has costly operations inside a loop:
	- totalDepositedInPublicPools += amount_ (contracts/capital-protocol/old/DistributionV3.sol#282)
	Calls stack containing the loop:
		DistributionV3.manageUsersInPrivatePool(uint256,address[],uint256[],uint128[])
DistributionV3._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV3.sol#312-373) has costly operations inside a loop:
	- totalDepositedInPublicPools -= amount_ (contracts/capital-protocol/old/DistributionV3.sol#367)
	Calls stack containing the loop:
		DistributionV3.manageUsersInPrivatePool(uint256,address[],uint256[],uint128[])
DistributionV4._stake(address,uint256,uint256,uint256,uint128) (contracts/capital-protocol/old/DistributionV4.sol#273-329) has costly operations inside a loop:
	- totalDepositedInPublicPools += amount_ (contracts/capital-protocol/old/DistributionV4.sol#301)
	Calls stack containing the loop:
		DistributionV4.manageUsersInPrivatePool(uint256,address[],uint256[],uint128[])
DistributionV4._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV4.sol#331-392) has costly operations inside a loop:
	- totalDepositedInPublicPools -= amount_ (contracts/capital-protocol/old/DistributionV4.sol#386)
	Calls stack containing the loop:
		DistributionV4.manageUsersInPrivatePool(uint256,address[],uint256[],uint128[])
DistributionV5._stake(address,uint256,uint256,uint256,uint128,address) (contracts/capital-protocol/old/DistributionV5.sol#351-423) has costly operations inside a loop:
	- totalDepositedInPublicPools += amount_ (contracts/capital-protocol/old/DistributionV5.sol#384)
	Calls stack containing the loop:
		DistributionV5.manageUsersInPrivatePool(uint256,address[],uint256[],uint128[],address[])
DistributionV5._withdraw(address,uint256,uint256,uint256) (contracts/capital-protocol/old/DistributionV5.sol#425-503) has costly operations inside a loop:
	- totalDepositedInPublicPools -= amount_ (contracts/capital-protocol/old/DistributionV5.sol#497)
	Calls stack containing the loop:
		DistributionV5.manageUsersInPrivatePool(uint256,address[],uint256[],uint128[],address[])
Reference: https://github.com/crytic/slither/wiki/Detector-Documentation#costly-operations-inside-a-loop
INFO:Detectors:
LogExpMath._ln(int256) (contracts/libs/LogExpMath.sol#321-453) has a high cyclomatic complexity (13).
Reference: https://github.com/crytic/slither/wiki/Detector-Documentation#cyclomatic-complexity
INFO:Detectors:
Version constraint ^0.8.10 contains known severe issues (https://solidity.readthedocs.io/en/latest/bugs.html)
	- VerbatimInvalidDeduplication
	- FullInlinerNonExpressionSplitArgumentEvaluationOrder
	- MissingSideEffectsOnSelectorAccess
	- AbiReencodingHeadOverflowWithStaticArrayCleanup
	- DirtyBytesArrayToStorage
	- DataLocationChangeInInternalOverride
	- NestedCalldataArrayAbiReencodingSizeValidation.
It is used by:
	- ^0.8.10 (contracts/interfaces/aave/IRewardsController.sol#2)
Reference: https://github.com/crytic/slither/wiki/Detector-Documentation#incorrect-versions-of-solidity
INFO:Detectors:
Parameter DVNOptions.groupDVNOptionsByIdx(bytes)._options (contracts/@layerzerolabs/lz-evm-messagelib-v2/contracts/uln/libs/DVNOptions.sol#28) is not in mixedCase
Parameter DVNOptions.getNumDVNs(bytes)._options (contracts/@layerzerolabs/lz-evm-messagelib-v2/contracts/uln/libs/DVNOptions.sol#120) is not in mixedCase
Parameter DVNOptions.nextDVNOption(bytes,uint256)._options (contracts/@layerzerolabs/lz-evm-messagelib-v2/contracts/uln/libs/DVNOptions.sol#160) is not in mixedCase
Parameter DVNOptions.nextDVNOption(bytes,uint256)._cursor (contracts/@layerzerolabs/lz-evm-messagelib-v2/contracts/uln/libs/DVNOptions.sol#161) is not in mixedCase
Parameter OAppCore.setPeer(uint32,bytes32)._eid (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oapp/OAppCore.sol#43) is not in mixedCase
Parameter OAppCore.setPeer(uint32,bytes32)._peer (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oapp/OAppCore.sol#43) is not in mixedCase
Parameter OAppCore.setDelegate(address)._delegate (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oapp/OAppCore.sol#67) is not in mixedCase
Parameter OAppReceiver.lzReceive(Origin,bytes32,bytes,address,bytes)._origin (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oapp/OAppReceiver.sol#86) is not in mixedCase
Parameter OAppReceiver.lzReceive(Origin,bytes32,bytes,address,bytes)._guid (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oapp/OAppReceiver.sol#87) is not in mixedCase
Parameter OAppReceiver.lzReceive(Origin,bytes32,bytes,address,bytes)._message (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oapp/OAppReceiver.sol#88) is not in mixedCase
Parameter OAppReceiver.lzReceive(Origin,bytes32,bytes,address,bytes)._executor (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oapp/OAppReceiver.sol#89) is not in mixedCase
Parameter OAppReceiver.lzReceive(Origin,bytes32,bytes,address,bytes)._extraData (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oapp/OAppReceiver.sol#90) is not in mixedCase
Parameter OAppOptionsType3.setEnforcedOptions(EnforcedOptionParam[])._enforcedOptions (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oapp/libs/OAppOptionsType3.sol#28) is not in mixedCase
Parameter OAppOptionsType3.combineOptions(uint32,uint16,bytes)._eid (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oapp/libs/OAppOptionsType3.sol#51) is not in mixedCase
Parameter OAppOptionsType3.combineOptions(uint32,uint16,bytes)._msgType (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oapp/libs/OAppOptionsType3.sol#52) is not in mixedCase
Parameter OAppOptionsType3.combineOptions(uint32,uint16,bytes)._extraOptions (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oapp/libs/OAppOptionsType3.sol#53) is not in mixedCase
Parameter OptionsBuilder.addExecutorLzReceiveOption(bytes,uint128,uint128)._options (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oapp/libs/OptionsBuilder.sol#54) is not in mixedCase
Parameter OptionsBuilder.addExecutorLzReceiveOption(bytes,uint128,uint128)._gas (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oapp/libs/OptionsBuilder.sol#55) is not in mixedCase
Parameter OptionsBuilder.addExecutorLzReceiveOption(bytes,uint128,uint128)._value (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oapp/libs/OptionsBuilder.sol#56) is not in mixedCase
Parameter OptionsBuilder.addExecutorNativeDropOption(bytes,uint128,bytes32)._options (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oapp/libs/OptionsBuilder.sol#72) is not in mixedCase
Parameter OptionsBuilder.addExecutorNativeDropOption(bytes,uint128,bytes32)._amount (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oapp/libs/OptionsBuilder.sol#73) is not in mixedCase
Parameter OptionsBuilder.addExecutorNativeDropOption(bytes,uint128,bytes32)._receiver (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oapp/libs/OptionsBuilder.sol#74) is not in mixedCase
Parameter OptionsBuilder.addExecutorLzComposeOption(bytes,uint16,uint128,uint128)._options (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oapp/libs/OptionsBuilder.sol#93) is not in mixedCase
Parameter OptionsBuilder.addExecutorLzComposeOption(bytes,uint16,uint128,uint128)._index (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oapp/libs/OptionsBuilder.sol#94) is not in mixedCase
Parameter OptionsBuilder.addExecutorLzComposeOption(bytes,uint16,uint128,uint128)._gas (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oapp/libs/OptionsBuilder.sol#95) is not in mixedCase
Parameter OptionsBuilder.addExecutorLzComposeOption(bytes,uint16,uint128,uint128)._value (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oapp/libs/OptionsBuilder.sol#96) is not in mixedCase
Parameter OptionsBuilder.addExecutorOrderedExecutionOption(bytes)._options (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oapp/libs/OptionsBuilder.sol#108) is not in mixedCase
Parameter OptionsBuilder.addDVNPreCrimeOption(bytes,uint8)._options (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oapp/libs/OptionsBuilder.sol#120) is not in mixedCase
Parameter OptionsBuilder.addDVNPreCrimeOption(bytes,uint8)._dvnIdx (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oapp/libs/OptionsBuilder.sol#121) is not in mixedCase
Parameter OptionsBuilder.addExecutorOption(bytes,uint8,bytes)._options (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oapp/libs/OptionsBuilder.sol#134) is not in mixedCase
Parameter OptionsBuilder.addExecutorOption(bytes,uint8,bytes)._optionType (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oapp/libs/OptionsBuilder.sol#135) is not in mixedCase
Parameter OptionsBuilder.addExecutorOption(bytes,uint8,bytes)._option (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oapp/libs/OptionsBuilder.sol#136) is not in mixedCase
Parameter OptionsBuilder.addDVNOption(bytes,uint8,uint8,bytes)._options (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oapp/libs/OptionsBuilder.sol#157) is not in mixedCase
Parameter OptionsBuilder.addDVNOption(bytes,uint8,uint8,bytes)._dvnIdx (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oapp/libs/OptionsBuilder.sol#158) is not in mixedCase
Parameter OptionsBuilder.addDVNOption(bytes,uint8,uint8,bytes)._optionType (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oapp/libs/OptionsBuilder.sol#159) is not in mixedCase
Parameter OptionsBuilder.addDVNOption(bytes,uint8,uint8,bytes)._option (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oapp/libs/OptionsBuilder.sol#160) is not in mixedCase
Parameter OptionsBuilder.encodeLegacyOptionsType1(uint256)._executionGas (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oapp/libs/OptionsBuilder.sol#178) is not in mixedCase
Parameter OptionsBuilder.encodeLegacyOptionsType2(uint256,uint256,bytes)._executionGas (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oapp/libs/OptionsBuilder.sol#191) is not in mixedCase
Parameter OptionsBuilder.encodeLegacyOptionsType2(uint256,uint256,bytes)._nativeForDst (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oapp/libs/OptionsBuilder.sol#192) is not in mixedCase
Parameter OptionsBuilder.encodeLegacyOptionsType2(uint256,uint256,bytes)._receiver (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oapp/libs/OptionsBuilder.sol#193) is not in mixedCase
Parameter OFTCore.setMsgInspector(address)._msgInspector (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oft/OFTCore.sol#80) is not in mixedCase
Parameter OFTCore.quoteOFT(SendParam)._sendParam (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oft/OFTCore.sol#93) is not in mixedCase
Parameter OFTCore.quoteSend(SendParam,bool)._sendParam (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oft/OFTCore.sol#130) is not in mixedCase
Parameter OFTCore.quoteSend(SendParam,bool)._payInLzToken (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oft/OFTCore.sol#131) is not in mixedCase
Parameter OFTCore.send(SendParam,MessagingFee,address)._sendParam (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oft/OFTCore.sol#160) is not in mixedCase
Parameter OFTCore.send(SendParam,MessagingFee,address)._fee (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oft/OFTCore.sol#161) is not in mixedCase
Parameter OFTCore.send(SendParam,MessagingFee,address)._refundAddress (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oft/OFTCore.sol#162) is not in mixedCase
Parameter OFTCore.isPeer(uint32,bytes32)._eid (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oft/OFTCore.sol#290) is not in mixedCase
Parameter OFTCore.isPeer(uint32,bytes32)._peer (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oft/OFTCore.sol#290) is not in mixedCase
Parameter OFTComposeMsgCodec.encode(uint64,uint32,uint256,bytes)._nonce (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oft/libs/OFTComposeMsgCodec.sol#21) is not in mixedCase
Parameter OFTComposeMsgCodec.encode(uint64,uint32,uint256,bytes)._srcEid (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oft/libs/OFTComposeMsgCodec.sol#22) is not in mixedCase
Parameter OFTComposeMsgCodec.encode(uint64,uint32,uint256,bytes)._amountLD (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oft/libs/OFTComposeMsgCodec.sol#23) is not in mixedCase
Parameter OFTComposeMsgCodec.encode(uint64,uint32,uint256,bytes)._composeMsg (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oft/libs/OFTComposeMsgCodec.sol#24) is not in mixedCase
Parameter OFTComposeMsgCodec.nonce(bytes)._msg (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oft/libs/OFTComposeMsgCodec.sol#34) is not in mixedCase
Parameter OFTComposeMsgCodec.srcEid(bytes)._msg (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oft/libs/OFTComposeMsgCodec.sol#43) is not in mixedCase
Parameter OFTComposeMsgCodec.amountLD(bytes)._msg (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oft/libs/OFTComposeMsgCodec.sol#52) is not in mixedCase
Parameter OFTComposeMsgCodec.composeFrom(bytes)._msg (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oft/libs/OFTComposeMsgCodec.sol#61) is not in mixedCase
Parameter OFTComposeMsgCodec.composeMsg(bytes)._msg (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oft/libs/OFTComposeMsgCodec.sol#70) is not in mixedCase
Parameter OFTComposeMsgCodec.addressToBytes32(address)._addr (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oft/libs/OFTComposeMsgCodec.sol#79) is not in mixedCase
Parameter OFTComposeMsgCodec.bytes32ToAddress(bytes32)._b (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oft/libs/OFTComposeMsgCodec.sol#88) is not in mixedCase
Parameter OFTMsgCodec.encode(bytes32,uint64,bytes)._sendTo (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oft/libs/OFTMsgCodec.sol#19) is not in mixedCase
Parameter OFTMsgCodec.encode(bytes32,uint64,bytes)._amountShared (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oft/libs/OFTMsgCodec.sol#20) is not in mixedCase
Parameter OFTMsgCodec.encode(bytes32,uint64,bytes)._composeMsg (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oft/libs/OFTMsgCodec.sol#21) is not in mixedCase
Parameter OFTMsgCodec.isComposed(bytes)._msg (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oft/libs/OFTMsgCodec.sol#35) is not in mixedCase
Parameter OFTMsgCodec.sendTo(bytes)._msg (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oft/libs/OFTMsgCodec.sol#44) is not in mixedCase
Parameter OFTMsgCodec.amountSD(bytes)._msg (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oft/libs/OFTMsgCodec.sol#53) is not in mixedCase
Parameter OFTMsgCodec.composeMsg(bytes)._msg (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oft/libs/OFTMsgCodec.sol#62) is not in mixedCase
Parameter OFTMsgCodec.addressToBytes32(address)._addr (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oft/libs/OFTMsgCodec.sol#71) is not in mixedCase
Parameter OFTMsgCodec.bytes32ToAddress(bytes32)._b (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/oft/libs/OFTMsgCodec.sol#80) is not in mixedCase
Parameter OAppPreCrimeSimulator.setPreCrime(address)._preCrime (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/precrime/OAppPreCrimeSimulator.sol#32) is not in mixedCase
Parameter OAppPreCrimeSimulator.lzReceiveAndRevert(InboundPacket[])._packets (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/precrime/OAppPreCrimeSimulator.sol#45) is not in mixedCase
Parameter OAppPreCrimeSimulator.lzReceiveSimulate(Origin,bytes32,bytes,address,bytes)._origin (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/precrime/OAppPreCrimeSimulator.sol#85) is not in mixedCase
Parameter OAppPreCrimeSimulator.lzReceiveSimulate(Origin,bytes32,bytes,address,bytes)._guid (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/precrime/OAppPreCrimeSimulator.sol#86) is not in mixedCase
Parameter OAppPreCrimeSimulator.lzReceiveSimulate(Origin,bytes32,bytes,address,bytes)._message (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/precrime/OAppPreCrimeSimulator.sol#87) is not in mixedCase
Parameter OAppPreCrimeSimulator.lzReceiveSimulate(Origin,bytes32,bytes,address,bytes)._executor (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/precrime/OAppPreCrimeSimulator.sol#88) is not in mixedCase
Parameter OAppPreCrimeSimulator.lzReceiveSimulate(Origin,bytes32,bytes,address,bytes)._extraData (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/precrime/OAppPreCrimeSimulator.sol#89) is not in mixedCase
Parameter PacketDecoder.decode(bytes)._packet (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/precrime/libs/Packet.sol#35) is not in mixedCase
Parameter PacketDecoder.decode(bytes[],uint256[])._packets (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/precrime/libs/Packet.sol#50) is not in mixedCase
Parameter PacketDecoder.decode(bytes[],uint256[])._packetMsgValues (contracts/@layerzerolabs/lz-evm-oapp-v2/contracts/precrime/libs/Packet.sol#51) is not in mixedCase
Function BuilderSubnets.BuilderSubnets_init(address,address,address,uint256,address) (contracts/builder-protocol/BuilderSubnets.sol#82-100) is not in mixedCase
Function BuildersTreasury.BuildersTreasury_init(address,address) (contracts/builder-protocol/BuildersTreasury.sol#28-34) is not in mixedCase
Function BuildersV2.BuildersV2_init(address,address,address,uint128,uint256) (contracts/builder-protocol/BuildersV2.sol#45-60) is not in mixedCase
Function BuildersV3.BuildersV3_init(address,address,address,uint128,uint256) (contracts/builder-protocol/BuildersV3.sol#59-74) is not in mixedCase
Function FeeConfig.FeeConfig_init(address,uint256) (contracts/builder-protocol/FeeConfig.sol#26-31) is not in mixedCase
Function Builders.Builders_init(address,address,address,uint128,uint256) (contracts/builder-protocol/old/Builders.sol#45-60) is not in mixedCase
Function ChainLinkDataConsumer.ChainLinkDataConsumer_init() (contracts/capital-protocol/ChainLinkDataConsumer.sol#32-35) is not in mixedCase
Function DepositPool.DepositPool_init(address,address) (contracts/capital-protocol/DepositPool.sol#85-91) is not in mixedCase
Function DistributionV6.Distribution_init(address,address,IDistributionV5.Pool[]) (contracts/capital-protocol/DistributionV6.sol#74-88) is not in mixedCase
Function Distributor.Distributor_init(address,address,address,address,address) (contracts/capital-protocol/Distributor.sol#88-103) is not in mixedCase
Function L1SenderV2.L1SenderV2__init() (contracts/capital-protocol/L1SenderV2.sol#43-46) is not in mixedCase
Function L2TokenReceiverV2.L2TokenReceiver__init(address,address,IL2TokenReceiverV2.SwapParams) (contracts/capital-protocol/L2TokenReceiverV2.sol#27-41) is not in mixedCase
Function RewardPool.RewardPool_init(IRewardPool.RewardPool[]) (contracts/capital-protocol/RewardPool.sol#22-29) is not in mixedCase
Function Distribution.Distribution_init(address,address,IDistribution.Pool[]) (contracts/capital-protocol/old/Distribution.sol#54-68) is not in mixedCase
Function DistributionV2.Distribution_init(address,address,IDistributionV2.Pool[]) (contracts/capital-protocol/old/DistributionV2.sol#58-72) is not in mixedCase
Function DistributionV3.Distribution_init(address,address,IDistributionV3.Pool[]) (contracts/capital-protocol/old/DistributionV3.sol#58-72) is not in mixedCase
Function DistributionV4.Distribution_init(address,address,IDistributionV4.Pool[]) (contracts/capital-protocol/old/DistributionV4.sol#61-75) is not in mixedCase
Function DistributionV5.Distribution_init(address,address,IDistributionV5.Pool[]) (contracts/capital-protocol/old/DistributionV5.sol#68-82) is not in mixedCase
Function L1Sender.L1Sender__init(address,IL1Sender.RewardTokenConfig,IL1Sender.DepositTokenConfig) (contracts/capital-protocol/old/L1Sender.sol#31-42) is not in mixedCase
Function L2MessageReceiver.L2MessageReceiver__init() (contracts/capital-protocol/old/L2MessageReceiver.sol#21-24) is not in mixedCase
Function L2TokenReceiver.L2TokenReceiver__init(address,address,IL2TokenReceiver.SwapParams) (contracts/capital-protocol/old/L2TokenReceiver.sol#23-35) is not in mixedCase
Function DistributionExt.DistributionExt_init(address,uint256[]) (contracts/extensions/DistributionExt.sol#18-24) is not in mixedCase
Function IBuilderSubnets.BuilderSubnets_init(address,address,address,uint256,address) (contracts/interfaces/builder-protocol/IBuilderSubnets.sol#240-246) is not in mixedCase
Function IFeeConfig.FeeConfig_init(address,uint256) (contracts/interfaces/builder-protocol/IFeeConfig.sol#56) is not in mixedCase
Function IChainLinkDataConsumer.ChainLinkDataConsumer_init() (contracts/interfaces/capital-protocol/IChainLinkDataConsumer.sol#21) is not in mixedCase
Function IDepositPool.DepositPool_init(address,address) (contracts/interfaces/capital-protocol/IDepositPool.sol#220) is not in mixedCase
Event IL1SenderV2.stETHSet(address) (contracts/interfaces/capital-protocol/IL1SenderV2.sol#11) is not in CapWords
Function IDistribution.Distribution_init(address,address,IDistribution.Pool[]) (contracts/interfaces/capital-protocol/old/IDistribution.sol#109) is not in mixedCase
Function IDistributionV2.Distribution_init(address,address,IDistributionV2.Pool[]) (contracts/interfaces/capital-protocol/old/IDistributionV2.sol#125) is not in mixedCase
Function IDistributionV3.Distribution_init(address,address,IDistributionV3.Pool[]) (contracts/interfaces/capital-protocol/old/IDistributionV3.sol#125) is not in mixedCase
Function IDistributionV4.Distribution_init(address,address,IDistributionV4.Pool[]) (contracts/interfaces/capital-protocol/old/IDistributionV4.sol#144) is not in mixedCase
Function IDistributionV5.Distribution_init(address,address,IDistributionV5.Pool[]) (contracts/interfaces/capital-protocol/old/IDistributionV5.sol#173) is not in mixedCase
Function LogExpMath._ln_36(int256) (contracts/libs/LogExpMath.sol#461-508) is not in mixedCase
Constant LogExpMath.x0 (contracts/libs/LogExpMath.sol#59) is not in UPPER_CASE_WITH_UNDERSCORES
Constant LogExpMath.a0 (contracts/libs/LogExpMath.sol#60) is not in UPPER_CASE_WITH_UNDERSCORES
Constant LogExpMath.x1 (contracts/libs/LogExpMath.sol#61) is not in UPPER_CASE_WITH_UNDERSCORES
Constant LogExpMath.a1 (contracts/libs/LogExpMath.sol#62) is not in UPPER_CASE_WITH_UNDERSCORES
Constant LogExpMath.x2 (contracts/libs/LogExpMath.sol#65) is not in UPPER_CASE_WITH_UNDERSCORES
Constant LogExpMath.a2 (contracts/libs/LogExpMath.sol#66) is not in UPPER_CASE_WITH_UNDERSCORES
Constant LogExpMath.x3 (contracts/libs/LogExpMath.sol#67) is not in UPPER_CASE_WITH_UNDERSCORES
Constant LogExpMath.a3 (contracts/libs/LogExpMath.sol#68) is not in UPPER_CASE_WITH_UNDERSCORES
Constant LogExpMath.x4 (contracts/libs/LogExpMath.sol#69) is not in UPPER_CASE_WITH_UNDERSCORES
Constant LogExpMath.a4 (contracts/libs/LogExpMath.sol#70) is not in UPPER_CASE_WITH_UNDERSCORES
Constant LogExpMath.x5 (contracts/libs/LogExpMath.sol#71) is not in UPPER_CASE_WITH_UNDERSCORES
Constant LogExpMath.a5 (contracts/libs/LogExpMath.sol#72) is not in UPPER_CASE_WITH_UNDERSCORES
Constant LogExpMath.x6 (contracts/libs/LogExpMath.sol#73) is not in UPPER_CASE_WITH_UNDERSCORES
Constant LogExpMath.a6 (contracts/libs/LogExpMath.sol#74) is not in UPPER_CASE_WITH_UNDERSCORES
Constant LogExpMath.x7 (contracts/libs/LogExpMath.sol#75) is not in UPPER_CASE_WITH_UNDERSCORES
Constant LogExpMath.a7 (contracts/libs/LogExpMath.sol#76) is not in UPPER_CASE_WITH_UNDERSCORES
Constant LogExpMath.x8 (contracts/libs/LogExpMath.sol#77) is not in UPPER_CASE_WITH_UNDERSCORES
Constant LogExpMath.a8 (contracts/libs/LogExpMath.sol#78) is not in UPPER_CASE_WITH_UNDERSCORES
Constant LogExpMath.x9 (contracts/libs/LogExpMath.sol#79) is not in UPPER_CASE_WITH_UNDERSCORES
Constant LogExpMath.a9 (contracts/libs/LogExpMath.sol#80) is not in UPPER_CASE_WITH_UNDERSCORES
Constant LogExpMath.x10 (contracts/libs/LogExpMath.sol#81) is not in UPPER_CASE_WITH_UNDERSCORES
Constant LogExpMath.a10 (contracts/libs/LogExpMath.sol#82) is not in UPPER_CASE_WITH_UNDERSCORES
Constant LogExpMath.x11 (contracts/libs/LogExpMath.sol#83) is not in UPPER_CASE_WITH_UNDERSCORES
Constant LogExpMath.a11 (contracts/libs/LogExpMath.sol#84) is not in UPPER_CASE_WITH_UNDERSCORES
Reference: https://github.com/crytic/slither/wiki/Detector-Documentation#conformance-to-solidity-naming-conventions
INFO:Detectors:
LogExpMath.slitherConstructorConstantVariables() (contracts/libs/LogExpMath.sol#29-509) uses literals with too many digits:
	- x0 = 128000000000000000000 (contracts/libs/LogExpMath.sol#59)
LogExpMath.slitherConstructorConstantVariables() (contracts/libs/LogExpMath.sol#29-509) uses literals with too many digits:
	- a0 = 38877084059945950922200000000000000000000000000000000000 (contracts/libs/LogExpMath.sol#60)
LogExpMath.slitherConstructorConstantVariables() (contracts/libs/LogExpMath.sol#29-509) uses literals with too many digits:
	- x1 = 64000000000000000000 (contracts/libs/LogExpMath.sol#61)
LogExpMath.slitherConstructorConstantVariables() (contracts/libs/LogExpMath.sol#29-509) uses literals with too many digits:
	- a1 = 6235149080811616882910000000 (contracts/libs/LogExpMath.sol#62)
LogExpMath.slitherConstructorConstantVariables() (contracts/libs/LogExpMath.sol#29-509) uses literals with too many digits:
	- x2 = 3200000000000000000000 (contracts/libs/LogExpMath.sol#65)
LogExpMath.slitherConstructorConstantVariables() (contracts/libs/LogExpMath.sol#29-509) uses literals with too many digits:
	- a2 = 7896296018268069516100000000000000 (contracts/libs/LogExpMath.sol#66)
LogExpMath.slitherConstructorConstantVariables() (contracts/libs/LogExpMath.sol#29-509) uses literals with too many digits:
	- x3 = 1600000000000000000000 (contracts/libs/LogExpMath.sol#67)
LogExpMath.slitherConstructorConstantVariables() (contracts/libs/LogExpMath.sol#29-509) uses literals with too many digits:
	- a3 = 888611052050787263676000000 (contracts/libs/LogExpMath.sol#68)
LogExpMath.slitherConstructorConstantVariables() (contracts/libs/LogExpMath.sol#29-509) uses literals with too many digits:
	- x4 = 800000000000000000000 (contracts/libs/LogExpMath.sol#69)
LogExpMath.slitherConstructorConstantVariables() (contracts/libs/LogExpMath.sol#29-509) uses literals with too many digits:
	- x5 = 400000000000000000000 (contracts/libs/LogExpMath.sol#71)
LogExpMath.slitherConstructorConstantVariables() (contracts/libs/LogExpMath.sol#29-509) uses literals with too many digits:
	- x6 = 200000000000000000000 (contracts/libs/LogExpMath.sol#73)
LogExpMath.slitherConstructorConstantVariables() (contracts/libs/LogExpMath.sol#29-509) uses literals with too many digits:
	- x7 = 100000000000000000000 (contracts/libs/LogExpMath.sol#75)
LogExpMath.slitherConstructorConstantVariables() (contracts/libs/LogExpMath.sol#29-509) uses literals with too many digits:
	- x8 = 50000000000000000000 (contracts/libs/LogExpMath.sol#77)
LogExpMath.slitherConstructorConstantVariables() (contracts/libs/LogExpMath.sol#29-509) uses literals with too many digits:
	- x9 = 25000000000000000000 (contracts/libs/LogExpMath.sol#79)
LogExpMath.slitherConstructorConstantVariables() (contracts/libs/LogExpMath.sol#29-509) uses literals with too many digits:
	- x10 = 12500000000000000000 (contracts/libs/LogExpMath.sol#81)
LogExpMath.slitherConstructorConstantVariables() (contracts/libs/LogExpMath.sol#29-509) uses literals with too many digits:
	- x11 = 6250000000000000000 (contracts/libs/LogExpMath.sol#83)
Reference: https://github.com/crytic/slither/wiki/Detector-Documentation#too-many-digits
INFO:Detectors:
DepositPool.DECIMAL (contracts/capital-protocol/DepositPool.sol#22) is never used in DepositPool (contracts/capital-protocol/DepositPool.sol#17-772)
Reference: https://github.com/crytic/slither/wiki/Detector-Documentation#unused-state-variable
INFO:Detectors:
DepositPool.unusedStorage0 (contracts/capital-protocol/DepositPool.sol#33) should be constant 
Reference: https://github.com/crytic/slither/wiki/Detector-Documentation#state-variables-that-could-be-declared-constant
INFO:Slither:. analyzed (187 contracts with 100 detectors), 570 result(s) found
