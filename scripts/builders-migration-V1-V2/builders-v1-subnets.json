[{"id": "0x01c9c97ce69569af244b25f299f3cc830d98240094d6262b4ad010022d689f73", "name": "<PERSON><PERSON><PERSON>", "admin": "******************************************", "startsAt": "1737124200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "7", "users": [], "description": "Secure and decentralized platform for buying, selling and mining Bitcoin", "website": "https://www.lumerin.io/"}, {"id": "0x0365447f7c98fa1070f467f2cc220de7b0b9c17b9664336c10e26ee3182f39d1", "name": "Morpheus Node", "admin": "******************************************", "startsAt": "1738450800", "minimalDeposit": "10000000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1740816000", "totalUsers": "11", "users": [], "description": "Morpheus Node Written in Node JS Programming Language", "website": "https://github.com/domsteil/morpheus-node.git"}, {"id": "0x03fa50361370455816211100c40123006f5f15b53b290735edc0810fd61e77fd", "name": "<PERSON><PERSON><PERSON>", "admin": "******************************************", "startsAt": "1737124200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "5", "users": [], "description": "Stats and tools for the Morpheus Community", "website": "https://morlord.com/"}, {"id": "0x0a5ade57dfa9217dd1e955ca68468dfb249430c89c5f41b0c729e04db87a0563", "name": "DecentraNet", "admin": "0x36af03d425688183b502cc83dee92d0a8f27fdda", "startsAt": "1740196800", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "604800", "claimLockEnd": "1740801600", "totalUsers": "0", "users": [], "description": "", "website": ""}, {"id": "0x0b4e662f0a3480ee9117e3c2f0559d3f3295eac04ea52f84c1c5893281f22bb4", "name": "Morlord Shared", "admin": "0x72f457d6237f66f68b6ea4408a3219ab7ae13be8", "startsAt": "1739174400", "minimalDeposit": "1000000000000", "withdrawLockPeriodAfterDeposit": "604800", "claimLockEnd": "604800", "totalUsers": "0", "users": [], "description": "", "website": ""}, {"id": "0x0c8d5f4c48826aeecff5b2defb4314351a3ca7f93f7b41d8bb99c47e3aae1360", "name": "coincap", "admin": "******************************************", "startsAt": "1737145300", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "604800", "claimLockEnd": "0", "totalUsers": "63", "users": [], "description": "Reliable cryptocurrency prices and market capitalizations", "website": "https://coincap.io/"}, {"id": "0x1df41cd916959d1163dc8f0671a666ea8a3e434c13e40faef527133b5d167034", "name": "Staking", "admin": "******************************************", "startsAt": "1740672000", "minimalDeposit": "0", "withdrawLockPeriodAfterDeposit": "604800", "claimLockEnd": "1814418000", "totalUsers": "1", "users": [], "description": "", "website": ""}, {"id": "0x1fa03df9ba7caac7b490ee9df117fa2b17408d3a6a84037f806b7b89c3f9d938", "name": "Project Cloud", "admin": "******************************************", "startsAt": "1738386000", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1767243600", "totalUsers": "0", "users": [], "description": "", "website": ""}, {"id": "0x25c16934a56ededa389e347f693f39caa3596a7aff16a3eab9f74bd3b731cf6e", "name": "brainpower", "admin": "******************************************", "startsAt": "1738033200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "1814400", "claimLockEnd": "1739577600", "totalUsers": "6", "users": [], "description": "Brainpower Podcast", "website": "https://open.spotify.com/show/56eTDgUARPiaUntXLnxIGg"}, {"id": "0x25e349b6e220e73d277d80c4e8279b5bfe33b0adafa90193ab1dc763e5ed5e72", "name": "Near", "admin": "0x82eaaaadfec2e4fc2003ac9d4fe312de1c468821", "startsAt": "1737124200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "3", "users": [], "description": "Near Blockchain For AI", "website": "https://near.org/"}, {"id": "0x26d54d3de64a94b1ecef7c0e1437858999634e53331a9ceaccfe29f034d65386", "name": "MOR LISTINGS", "admin": "0x95a83fb7e87e210c1b32a14b0fd9c84100d6a69d", "startsAt": "1739606400", "minimalDeposit": "2000000000000000", "withdrawLockPeriodAfterDeposit": "604850", "claimLockEnd": "1742022000", "totalUsers": "0", "users": [], "description": "", "website": ""}, {"id": "0x2bb5cba5aa810086dd76d5a20c06616f9d7229b710078a809c1ad5654ebdb3be", "name": "Supertech.ai", "admin": "0x8544e213db4da5a5eef48d4d97c376e5d2d09a60", "startsAt": "1741158000", "minimalDeposit": "1000000000000000000", "withdrawLockPeriodAfterDeposit": "1209600", "claimLockEnd": "1743832800", "totalUsers": "0", "users": [], "description": "", "website": ""}, {"id": "0x2cef520db0d87be977cc612c0c57e2ef1788fcd8e7032418be1cccea82a603f4", "name": "ZO.ME", "admin": "0xbbdea6172be7798f7fa37e7e5432d9426954c1de", "startsAt": "1737124200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "3", "users": [], "description": "Chat with friends & AI", "website": "https://zo.me/"}, {"id": "0x3082ff65dbbc9af673b283c31d546436e07875a57eaffa505ce04de42b279306", "name": "Agent Access", "admin": "0xc2ff7040233afb5c117f9fb128d1d08d1bf351ba", "startsAt": "1738438140", "minimalDeposit": "10000000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1740816000", "totalUsers": "9", "users": [], "description": "Access to Agents", "website": ""}, {"id": "0x36aea6a8fbd9382c147c110ff07de962300818995b9961a8fea131d5efc1b1db", "name": "Flock.io", "admin": "0x372663a3629cbfd377c94d5962bd0b62c824bd4a", "startsAt": "1737124200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "5", "users": [], "description": "Federated Machine Learning On the Blockchain", "website": "https://www.flock.io/"}, {"id": "0x3f73d9b00b869a0eebb5325b37c807252a1307bd5390ecde1348aa11233703b2", "name": "MorpheusAI", "admin": "0x762bdda2cd7ca2760922694b034f10a73a5de0d4", "startsAt": "1738386000", "minimalDeposit": "100000000000000000", "withdrawLockPeriodAfterDeposit": "604800", "claimLockEnd": "1740718800", "totalUsers": "5", "users": [], "description": "Morpheus AI is the first agent powered by the Z10N framework that makes gamifying agents simple. Z10N will make creating an agentic economy a few clicks and prompts while creating an entire ecosystem for their communities to engage.", "website": "https://www.z10n.ai"}, {"id": "0x40aa9fbd2fde2e6a38510ef730aee171c3e81a4c165bccbddefe4c89b55b2a8d", "name": "Panorama", "admin": "0x25b7a50429d89a6e13dff47ccbe0e54e6cfc65b0", "startsAt": "1738684800", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "3", "users": [], "description": "Panorama Block is an initiative being developed in collaboration between faculty and students from UCLA and other leading universities worldwide. We're building an AI-powered data hub infrastructure designed to automate DeFi plays through the deployment, commercialization, and aggregation of AI agents.", "website": "https://panoramablock.com/"}, {"id": "0x42f02b08eadb43dbc23783822aeba31623e22e2c95e608e48590adb48e7856bf", "name": "Sapien AI", "admin": "0xb5d104ce51a488eb34c087ec0a245031814c1a52", "startsAt": "1737124200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "3", "users": [], "description": "Train AI with Expert Human Feedback", "website": "https://www.sapien.io/"}, {"id": "0x4501d4e79230adce800ff26e5d2e2ae061f1e54da8508fe17dd1462388fb8373", "name": "IamAI-Core", "admin": "******************************************", "startsAt": "1737748800", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "13", "users": [], "description": "I am AI Core", "website": "https://github.com/iamai-core"}, {"id": "0x4b3bbe93cd84e4304f785cbf55be1b9cfc23e73c7869704321f5a906e671930f", "name": "Mor Builders", "admin": "0x1cb2bc7ef28b2e127a2026fcdd2bf2fc27750525", "startsAt": "1738051200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1738137600", "totalUsers": "0", "users": [], "description": "", "website": ""}, {"id": "0x4c25cd7db02a0d0354010749554d260fec635455aca30f7b4001c222d14d4294", "name": "Manifest Network", "admin": "0xe7432b5415e8b3dba4a6ae167e89648a1051cb7f", "startsAt": "1737124200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "5", "users": [], "description": "Decentralized AI Platform", "website": "https://manifestai.org/"}, {"id": "0x4c2afb6295a12128fa7f9e601a1d77c646229a96dd639e886476a8c216b69844", "name": "PALcapital Z", "admin": "0x920d2b328f2058516496f932f6247d9347c594d2", "startsAt": "1738260900", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "0", "users": [], "description": "", "website": ""}, {"id": "0x4cba0f501b4603ad2e029e8ae5880befcb3e9d13c9e14987117374f2d87ad17b", "name": "AI art by Venice", "admin": "0x35f1bae79c4e74ee25354d326b42f809744f7e18", "startsAt": "1742713200", "minimalDeposit": "10000000000000000000", "withdrawLockPeriodAfterDeposit": "2600000", "claimLockEnd": "1745445600", "totalUsers": "0", "users": [], "description": "", "website": ""}, {"id": "0x4cbaac02f4c2ff6d72454ec7dc0a7ed0cbd06dff9d70001ab0d0e84508dd2efe", "name": "Lifted", "admin": "0xb081870678d563a4d1cf0ab189ab2038695827a3", "startsAt": "1737124200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "2", "users": [], "description": "", "website": ""}, {"id": "0x4f42bb9dee66865e920f676995b247c29add563a33a995ea2c90c4e66c10b1f9", "name": "Airvey Advisory", "admin": "0xeda4b0cb9507d69b3ce45c86d199a62cb474514b", "startsAt": "1742184000", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "604800", "claimLockEnd": "1744862400", "totalUsers": "3", "users": [], "description": "Supporting Contributors in Building on MOR's Open-Source AI Infrastructure.", "website": "http://www.airvey.io"}, {"id": "0x50a52d70c4c3b4b77d8fefaa24faf038450a656b9db6bc2e9d897f55ff72e974", "name": "<PERSON><PERSON><PERSON><PERSON>", "admin": "******************************************", "startsAt": "1739736000", "minimalDeposit": "10000000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739750400", "totalUsers": "0", "users": [], "description": "", "website": ""}, {"id": "0x523f1bb39a49927241fcd48dacab09dd72c218bf1e3a01e3206264fd666f0d79", "name": "OLAS", "admin": "0xb04b996738f69ddad9099acb040e500c2492446d", "startsAt": "1737124200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "5", "users": [], "description": "Own a share of AI, specifically autonomous agent economies", "website": "https://olas.network/"}, {"id": "0x525299b81b12c7478c98f0fc95f3b3a6a2c93ff62810c08c733a2727bb8e860b", "name": "Builder Register1", "admin": "0x8f2ce96fa97794e7cf468f43abd8ce21e64b93db", "startsAt": "1737072000", "minimalDeposit": "1000000000000000000", "withdrawLockPeriodAfterDeposit": "604800", "claimLockEnd": "1738281600", "totalUsers": "0", "users": [], "description": "", "website": ""}, {"id": "0x5c92edba5c98bd16497d97c0f5b37dfb696e9330bd12b06804bc45a6d0a619f0", "name": "Bloq", "admin": "0xf8d5ca35466a7eaba3fb7f9985d8ecc830af0667", "startsAt": "1737124200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "2", "users": [], "description": "Infrastructure & Applications for Web3", "website": "https://bloq.com/"}, {"id": "0x638451aa95394a1ea79bbf9d15eb532b5beed7f3831ccc8db278654ae30dad27", "name": "Virtuals", "admin": "0x64af3260da78548ad2f9be06ae77904690684460", "startsAt": "1737124200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "3", "users": [], "description": "Launchpad for AI Agents", "website": "https://app.virtuals.io/"}, {"id": "0x63891e659ff2ae88fdaf6ff05f07ffbbd4ffbca089600564c7c6c784a96ab0e5", "name": "Wire Network", "admin": "0x3caac83717157d7e9b14545803f36ff1650b467f", "startsAt": "1737124200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "33", "users": [], "description": "The Blockchain for the AI Economy", "website": "https://www.wire.network/"}, {"id": "0x6523f7e4f8418318d5361681a4f3d499bce60b3d8118b32d02ef295572304aff", "name": "Snowball Money", "admin": "0x2eac34dce8a6090133fce494e3ebc6badd3b7d9c", "startsAt": "1741039200", "minimalDeposit": "50000000000000000000", "withdrawLockPeriodAfterDeposit": "864000", "claimLockEnd": "1761775200", "totalUsers": "0", "users": [], "description": "Hybrid smart agent that blends on-chain behavioral analysis and portfolio intelligence", "website": "https://snowball.money"}, {"id": "0x664823f3e70c7f2788e8d393bffbfe1c6cb463d6af29b43933af6cfd7944588f", "name": "AI Art by Venice", "admin": "0xdb0f1da2f916761beaaa1f58f65b0f1b31d31062", "startsAt": "1742684400", "minimalDeposit": "10000000000000000000", "withdrawLockPeriodAfterDeposit": "2600000", "claimLockEnd": "1745359200", "totalUsers": "3", "users": [], "description": "A decentralized platform where your Venice AI-generated masterpieces live forever and earn rewards when your art enters the top10", "website": "https://aiartbyvenice_cavey65.arweave.net/"}, {"id": "0x6853bdaac0b14b032091c3495bfe4cf14ba89b86b0859ffdd937f17361befdf1", "name": "NounspaceTom", "admin": "0x06ae622bf2029db79bdebd38f723f1f33f95f6c5", "startsAt": "1739174400", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "2", "users": [], "description": "The First Smart Agent on Morpheus / Venice / Nounspace", "website": "https://x.com/nounspacetom"}, {"id": "0x6a117ee75d9c3c44c18cf44009429034ad02bd2e14fc3e982e63bdafcd55625c", "name": "Tales & Conquests", "admin": "0x55d6c73306a7af0e68c004147f86a98f16130b3b", "startsAt": "1738686600", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "9", "users": [], "description": "", "website": ""}, {"id": "0x6aa0f87703b4fc532743d2560ab8deac31a456360ccb7ad22770103d9061b9f0", "name": "MOR API Access", "admin": "0x69357171d8794841df9985947a3c20c807b56d43", "startsAt": "1741420800", "minimalDeposit": "5000000000000000000", "withdrawLockPeriodAfterDeposit": "604800", "claimLockEnd": "1741935600", "totalUsers": "0", "users": [], "description": "Access Morpheus compute with a Rest API key and a few lines of code.", "website": "https://x.com/Player1Taco"}, {"id": "0x6d4a00233c2192694cf0dc52e0bdfd73e136bd1338fcfced149989e7fa6bbdab", "name": "<PERSON><PERSON>", "admin": "0x370feb9dad2062917b4ee99ebb6af3c1f0ccbecd", "startsAt": "1740726000", "minimalDeposit": "1000000000000000000", "withdrawLockPeriodAfterDeposit": "604800", "claimLockEnd": "1740985200", "totalUsers": "4", "users": [], "description": "Decentralized AI prediction of live realworld data.", "website": "https://www.satorinet.io/"}, {"id": "0x70a8b9982ce25814a05d0cbc53e14d6e2ebc073620790482401e19b84f2bad64", "name": "Supercycle", "admin": "******************************************", "startsAt": "1740805200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "604800", "claimLockEnd": "1741323600", "totalUsers": "0", "users": [], "description": "", "website": ""}, {"id": "0x7169601aa382123b982cb9055874ed5ccb7223a5500a2b41aedf013653d3f167", "name": "Morlord MOR Staking", "admin": "0x72f457d6237f66f68b6ea4408a3219ab7ae13be8", "startsAt": "1737748800", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "26", "users": [], "description": "Stats and tools for the Morpheus Community", "website": "https://morlord.com/"}, {"id": "0x730429d43c917151bf43c3baa677922276bced4eb6718a5cb20a352bcbc0a290", "name": "6079", "admin": "0xc686d10c51b59dcb123547f2a3762e3ad43dd777", "startsAt": "1737124200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "13", "users": [], "description": "Movement to ensure AI respects individual and knowledge freedom.", "website": "https://6079.ai/"}, {"id": "0x7671a13aa6295ad5c6f20200935602ffe4cde338d98bdd43979b3664b65ad4ff", "name": "Shapeshift", "admin": "******************************************", "startsAt": "1737124200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "6", "users": [], "description": "Multichain Crypto Exchange", "website": "https://shapeshift.com/"}, {"id": "0x773f45fb1df3ae27ba60d371fc36af32fd1896863c842b4c4e89e30668944c0b", "name": "iamai-core", "admin": "******************************************", "startsAt": "1737356400", "minimalDeposit": "1000000000000000000", "withdrawLockPeriodAfterDeposit": "604800", "claimLockEnd": "1739689200", "totalUsers": "0", "users": [], "description": "", "website": ""}, {"id": "0x78b78019c2d0782c9790a3ae61a899c857d54e4ae6e012caca7338e477857868", "name": "Aiora Agency", "admin": "******************************************", "startsAt": "1737936000", "minimalDeposit": "100000000000000000", "withdrawLockPeriodAfterDeposit": "604800", "claimLockEnd": "1738368000", "totalUsers": "1", "users": [], "description": "Aiora Agency", "website": ""}, {"id": "0x79611d94d04d7c5a4dcfa6733b4c096a918193b453b10688665088f5183723b1", "name": "Nounspace", "admin": "******************************************", "startsAt": "1737124200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "55", "users": [], "description": "Decentralized, Customizable Social Network", "website": "https://nounspace.com/"}, {"id": "0x7c60385401910d17dba253bd525a1df56854dbca9493b1323532f5dc26a40c9f", "name": "<PERSON><PERSON>", "admin": "0xf15e95503d2014de612d54dad920d4aa00970f53", "startsAt": "1738684800", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "0", "users": [], "description": "<PERSON><PERSON> is more than just an AI agent; he's the embodiment of mischief, wit, and razor-sharp market insights. Developed by the DappLooker team, <PERSON><PERSON> brings data from <PERSON><PERSON><PERSON><PERSON><PERSON> to life through playful banter and keen market analysis.", "website": "https://0xloky.com/"}, {"id": "0x8036bbb740074d06db6b7741fcf9f52bd25846b9fc7e0a10c983c51530eaa999", "name": "MOR Builders", "admin": "0x1cb2bc7ef28b2e127a2026fcdd2bf2fc27750525", "startsAt": "1738062000", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "25", "users": [], "description": "Supporting, curating and accelerating Morpheus builders.", "website": "https://morbuilders.xyz/"}, {"id": "0x81d067ea1ad5367ddc5556020d6bef2bb5215e1d2d7d75888b400454b0f9b7cd", "name": "Tails & Conquest", "admin": "0x55d6c73306a7af0e68c004147f86a98f16130b3b", "startsAt": "1738684800", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "0", "users": [], "description": "", "website": ""}, {"id": "0x85e3b554619e2fd48a4ad8c8c2744a9d9686463ac02ff83664c717876c313a88", "name": "Phala Network", "admin": "0x696387bb15df29600cf4f646bd4a3658e6bbda7b", "startsAt": "1737124200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "2", "users": [], "description": "AI Coprocessor for blockchain", "website": "https://phala.network/"}, {"id": "0x8bb6452786155e48063040d7ae34ce4ea10c426eaf87650b59d8db22ec85ce39", "name": "MOR Staking", "admin": "******************************************", "startsAt": "1739399400", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "604800", "claimLockEnd": "1739399460", "totalUsers": "1", "users": [], "description": "", "website": ""}, {"id": "0x8e7ada070e9ad24653a2d85727fcbf1db45a6f48526d918c6148b83f31981010", "name": "B12", "admin": "0xd2bad13d6e3ccdfa549f911c3e8fcd378564b031", "startsAt": "1743975301", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "786400", "claimLockEnd": "0", "totalUsers": "0", "users": [], "description": "", "website": ""}, {"id": "0x9063e7cc118239ad52f8ac93133fd4b33f819217f2baad93c899bff7bf569fce", "name": "PoolingPool", "admin": "0xe0a35b164f5c5e44e26d357e4ac88e1b8714090f", "startsAt": "1736967331", "minimalDeposit": "1", "withdrawLockPeriodAfterDeposit": "604800", "claimLockEnd": "0", "totalUsers": "1", "users": [], "description": "", "website": ""}, {"id": "0x92ec4d95bf8d273481595c40a08ec03642868af7a1ed1a8b64d2372eb6aac33d", "name": "<PERSON><PERSON><PERSON>", "admin": "0x6c1ed23c071243879cbcc70dfda211c1f2ddb214", "startsAt": "1737124200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "9", "users": [], "description": "Decentralized Compute Marketplace", "website": "https://akash.network/"}, {"id": "0x96342c265828489a728e6278d3c1eab8f53a89809055b5d6495cb2b39bbf16a2", "name": "Renascence", "admin": "0xb4a0462e8f0511f01689ff8d58d649b46e90cb9c", "startsAt": "1737124200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "1", "users": [], "description": "Security audits made simple", "website": "https://renascence-labs.xyz/"}, {"id": "0x966710b4bff7486021f6c272f4c9e332cd35eeef58334cc7fc1c02c71088422a", "name": "<PERSON><PERSON>", "admin": "0xa466f111133f218a024de7d85b6b1d8da0224bfd", "startsAt": "1737124200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "1", "users": [], "description": "The Decentralized Back-End For All Node Data", "website": "https://arkeo.network/"}, {"id": "0x96e624768600a7be0f2a82545d33c137ff3df2f377dc3fdf468308e5b977a72f", "name": "MySuperAgent", "admin": "0x67760bad63cc00294764ef7d1f6570e864c196c1", "startsAt": "1738684800", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "22", "users": [], "description": "Super Agent for everyone", "website": "https://github.com/DavidAJohnston/MySuperAgent"}, {"id": "0x975822084ade3f1c75ddb0e4f63500d375330fd2110ee528dec27a44d7040f11", "name": "Aigent Z", "admin": "0x17e1b6c2bfbc721c1dc03d488746e0c6f7ef5242", "startsAt": "1737777600", "minimalDeposit": "0", "withdrawLockPeriodAfterDeposit": "1555200", "claimLockEnd": "1738213200", "totalUsers": "0", "users": [], "description": "Aigent Z", "website": ""}, {"id": "0x9a7e67694fac9f58c1e1d2b1791c5badfe0eceadb7f377b04bf2f0b00d56b343", "name": "<PERSON><PERSON>", "admin": "******************************************", "startsAt": "1737124200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "2", "users": [], "description": "The Hemi Network is a modular Layer-2 protocol for Bitcoin and Ethereum", "website": "https://hemi.xyz/"}, {"id": "0xa1f6876aa288ed12aea544c308056ec643dd965a20b6f2c8129b10fef9b9e949", "name": "MorStar", "admin": "******************************************", "startsAt": "1744047000", "minimalDeposit": "10000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1746676800", "totalUsers": "0", "users": [], "description": "", "website": ""}, {"id": "0xa7fafe0dddaeea5789018c11d0c420b718734b6c36a454c4613befb9b0e0f69d", "name": "Frostbyte", "admin": "******************************************", "startsAt": "1739736000", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739750400", "totalUsers": "2", "users": [], "description": "Decentralized, local, peer-to-peer password management—your data, your control.", "website": "https://www.frostbyte.app/"}, {"id": "0xa9255f549dcfaa5dac61b450252bc0c535624a0a837f66eee503c89fc747f50d", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "admin": "0x5ccbb407a268f3d71fa0079b7ab44845636bc73c", "startsAt": "1741842000", "minimalDeposit": "1000000000000000000", "withdrawLockPeriodAfterDeposit": "604800", "claimLockEnd": "1742187600", "totalUsers": "0", "users": [], "description": "", "website": ""}, {"id": "0xa7fafe0dddaeea5789018c11d0c420b718734b6c36a454c4613befb9b0e0f69d", "name": "Frostbyte", "admin": "******************************************", "startsAt": "1739736000", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739750400", "totalUsers": "1", "users": ["0x9e9cba2844bd75dcf0d07fa273050d8952f40888"], "description": "", "website": ""}, {"id": "0xaabc7043f8d1616d55ecae080430fb180ece49b4c269377d6f9c830854acabfc", "name": "Tales & Conquest", "admin": "0x55d6c73306a7af0e68c004147f86a98f16130b3b", "startsAt": "1739174400", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "8", "users": [], "description": "An AI agent-driven roleplaying game powered by Morpheus' decentralized inference router.", "website": "https://talesgame.com "}, {"id": "0xac491f4c14b9f329398b820d2573f73abdf66ebb0c5271fa55c3dcfd5adfa2fd", "name": "exaBITS", "admin": "0x59eb7b69d07540549f1913f26855f8247557a8ab", "startsAt": "1737124200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "2", "users": [], "description": "The AI compute base layer", "website": "https://www.exabits.ai/"}, {"id": "0xad925e39a087802d56fc2f774262d2c1bf41344e802328d0aa5fc04dddd526db", "name": "AO Arweave", "admin": "0x38ecffb55d36afcda4b8d6b65fff2ed5f373a4c7", "startsAt": "1737124200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "2", "users": [], "description": "Decentralized Permanent information storage", "website": "https://ao.arweave.dev/"}, {"id": "0xafcaf9312d2873f0b58a04fcc310db9d71ca402a3a48adfbcb00acc691851908", "name": "4kGpL8", "admin": "0x05a1ff0a32bc24265bcb39499d0c5d9a6cb2011c", "startsAt": "1741972500", "minimalDeposit": "1000000000000000000", "withdrawLockPeriodAfterDeposit": "604800", "claimLockEnd": "1741972800", "totalUsers": "0", "users": [], "description": "", "website": ""}, {"id": "0xb46506c4174fa5ec7a36fdb4d7a3cd237dd6c60d6b2bcc9e4c1731f9f0e49c6f", "name": "MORLORD", "admin": "0x72f457d6237f66f68b6ea4408a3219ab7ae13be8", "startsAt": "1739548913", "minimalDeposit": "1000000000000", "withdrawLockPeriodAfterDeposit": "604800", "claimLockEnd": "0", "totalUsers": "1", "users": [], "description": "", "website": ""}, {"id": "0xb5040e011d35dd944d284d4692baf05ec66163d07c023a71eaabd0a930511ad7", "name": "PALcapital Ecosystem Platform", "admin": "0x920d2b328f2058516496f932f6247d9347c594d2", "startsAt": "1738342800", "minimalDeposit": "10000000000000000000", "withdrawLockPeriodAfterDeposit": "1209600", "claimLockEnd": "1739509200", "totalUsers": "0", "users": [], "description": "", "website": ""}, {"id": "0xb518addbd1e3abf0abcd7bd0db4af0eef300983a8573cc66f54426f83e024468", "name": "Lumerin MOR Staking", "admin": "******************************************", "startsAt": "1737748800", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "3", "users": [], "description": "Bitcoin Hashpower, AI, RWAs & DePIN", "website": "https://www.lumerin.io/"}, {"id": "0xb9086e4900331eb895edf4f6db298bd290d48037c663e6243d0386157dc8d0c7", "name": "Gif Studios", "admin": "******************************************", "startsAt": "1737712800", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "1", "users": [], "description": "Gif Studios", "website": "https://gifstudios.com/"}, {"id": "0xbffe9cb8e4e2ecba926505a06c6ca3717cb2bdbee65797b1134672765899c0b7", "name": "Rainfall", "admin": "******************************************", "startsAt": "1737124200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "4", "users": [], "description": "Empower of people via self-sovereign Personal AI", "website": "https://rainfall.one/"}, {"id": "0xc00e9d8d26a19b9af1ecb5e717182a7db3f5d131ddb324df6e996464c2314ed8", "name": "Distro Media", "admin": "0x0e9aca260d4fa92ec6076db2265dc0014f0ddeb1", "startsAt": "1742792400", "minimalDeposit": "1000000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1745470800", "totalUsers": "0", "users": [], "description": "Blockchain-powered, AI-enabled news delivery platform", "website": "https://distro.media"}, {"id": "0xc0aca3a0b3cfab81287943ef4a48e0c2f0441c12beb50fc8c2be3a810bbe0d6c", "name": "Katara", "admin": "0xcd8f007aae316b15baa10c650edeb3fe08a75999", "startsAt": "1738796400", "minimalDeposit": "1000000000000000000", "withdrawLockPeriodAfterDeposit": "1209600", "claimLockEnd": "1741561200", "totalUsers": "12", "users": [], "description": "Katara is a cutting-edge agentic workflow automation platform for DevX Teams", "website": "https://katara.ai"}, {"id": "0xc0b926f96e1aadbcd04c21c56649e3846b35bcd18ffafc1cdce7a3d8130c419c", "name": "Venice Pro", "admin": "0xe01c58819893865b5df20c9f75833401f1c0906b", "startsAt": "1737990000", "minimalDeposit": "20000000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "23", "users": [], "description": "Venice.AI is a private, uncensored AI platform prioritizing user data privacy.", "website": "https://venice.ai/chat"}, {"id": "0xc1ecd59a9b43eb728f40009cb3874a209a54233bdc1266193041e5247e0b22ee", "name": "AI16Z", "admin": "0xd928ca72eac64523def6e2703f9091d9d99019e7", "startsAt": "1737124200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "2", "users": [], "description": "Operating System for AI Agents", "website": "https://www.elizaos.ai/"}, {"id": "0xc8a0087b1d7e5b3a2f18b2d79fd2fbf187802af7370b928ff0e6eacf47341299", "name": "PALcapital", "admin": "0x920d2b328f2058516496f932f6247d9347c594d2", "startsAt": "1738260900", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "13", "users": [], "description": "Creating a launchpad, dealmaking and introduction platform for the Morpheus ecosystem hosted by PALcapital.com.", "website": "https://www.palcapital.com/"}, {"id": "0xc979023bb9bf3b74ae2eca5a210c7297f9e8e604d01a0d6024de07acfa7762fc", "name": "<PERSON> Layer", "admin": "0x12584bbe07643064992d603664937fa801112ef4", "startsAt": "1737124200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "5", "users": [], "description": "Decentralized AI-powered smart contracts with access to the the Internet", "website": "https://www.genlayer.com/"}, {"id": "0xcd69610384d862dcae7c4009fa01b20624fe0cbf464d2468a041ffc709bac2de", "name": "<PERSON><PERSON><PERSON>", "admin": "0x0199346a0c257ac901a899baa83bcf1bcfa9e0e5", "startsAt": "1737124200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "4", "users": [], "description": "The Future of Agentic Finance Powered by Swarms", "website": "https://www.theoriq.ai/"}, {"id": "0xd1c92131e09eb948006132c27c2ba5e12890bcd445fafd135934d92a093a3a02", "name": "CCOVibe", "admin": "0x459c674aa0d686ac03a245af70f5775e33ac323e", "startsAt": "1743397200", "minimalDeposit": "1000000000000000000", "withdrawLockPeriodAfterDeposit": "1000000", "claimLockEnd": "1745989200", "totalUsers": "0", "users": [], "description": "", "website": ""}, {"id": "0xd36cf10a2fc6460f84bc3d69a34da57d8223d46757eef0326aa23bc8f658e56f", "name": "GrowMOR", "admin": "0xf8a9f05c149a73e80b35f9f929670d10cdd91fde", "startsAt": "1739401200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "604800", "claimLockEnd": "1739401200", "totalUsers": "3", "users": [], "description": "Independent organisation dedicated to contribute to growth of MOR token and Morpheus ecosystem.", "website": "https://growmor.org/"}, {"id": "0xd6afb3447b6d684ed910bf2f3d6bc00277032db092cfb211ac4bb66c1f4d077d", "name": "FrostByte", "admin": "******************************************", "startsAt": "1739775600", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1742191200", "totalUsers": "0", "users": [], "description": "", "website": ""}, {"id": "0xd779b953d99618c0159171c3364d60df6f75ab3f2d209bbf684616fb7e928894", "name": "DeFAI", "admin": "******************************************", "startsAt": "1739340000", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "604800", "claimLockEnd": "1740808800", "totalUsers": "0", "users": [], "description": "", "website": ""}, {"id": "0xda50f9aa1710272d6c17f84d713b6c8f30b2870c376039e5323852b027a440fa", "name": "SUPERCYCLE", "admin": "******************************************", "startsAt": "1740718800", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "604800", "claimLockEnd": "1740805200", "totalUsers": "1", "users": [], "description": "Crypto AI Media Network", "website": "https://sup3rcycl3.com/"}, {"id": "0xda589917a3e3836aab6bc61103b436b5a3ab440e9b69715a1618f74f9dda5a7b", "name": "DAIS", "admin": "******************************************", "startsAt": "1737124200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "7", "users": [], "description": "Decentralized AI Society", "website": "https://dais.global/"}, {"id": "0xdcba960308192a0eb3e6dbd97b27b3cc2454e38d06ef78ced76e7378afb2e5dc", "name": "Pattern", "admin": "******************************************", "startsAt": "1738195200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1740787200", "totalUsers": "6", "users": [], "description": "Agentic RAG protocol", "website": "https://pattern.global/"}, {"id": "0xe7aa57528844a44f9c1833db28775663b5d16e09c8075407b07c8973bb05d185", "name": "CETI", "admin": "0x1f22b27f0b4faf651dbaea215b32988f658008a3", "startsAt": "1737124200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "2", "users": [], "description": "Globally distributed, high-performance, scalable decentralized AI infrastructure", "website": "https://taoceti.ai/"}, {"id": "0xe9fba89ca97425c3389c8086a17a75785b04152c035b68b8108980375a60ad21", "name": "ccovibe.com", "admin": "0x459c674aa0d686ac03a245af70f5775e33ac323e", "startsAt": "1742187600", "minimalDeposit": "1000000000000000000", "withdrawLockPeriodAfterDeposit": "700000", "claimLockEnd": "1742619600", "totalUsers": "2", "users": [], "description": "Project Management and Community for AI Engineers", "website": "http://ccovibe.com"}, {"id": "0xed99429be84b05331210ff2d4b1ff17e52876fb7c2a3b72caf3cb0fc8bd97e52", "name": "TacoBytes", "admin": "0x896c20da40c2a4df9b7c98b16a8d5a95129161a5", "startsAt": "1741330800", "minimalDeposit": "10000000000000000", "withdrawLockPeriodAfterDeposit": "1814400", "claimLockEnd": "1744005600", "totalUsers": "3", "users": [], "description": "Media and Content, helping give a voice to the builders and talking about what they are doing from a different perspective.", "website": "https://x.com/Player1Taco"}, {"id": "0xf18718acbee73105d304ee13f506a5765de4885f1c62a8f5a97089702c40507a", "name": "ATX DAO", "admin": "0xcbb0aac025f0554978a5ec4b35169e71e4910213", "startsAt": "1737124200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "2", "users": [], "description": "Austin DAO", "website": "https://www.atxdao.com/"}, {"id": "0xf510a7f4efbcb090687018eebc97c61d447a833fbd1ba4333b0fc2e4541b5def", "name": "Freedom GPT", "admin": "0x3e8d52d57798fc9c37d9618887935a1a5266a715", "startsAt": "1737124200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "3", "users": [], "description": "Access to Uncensored AI", "website": "https://www.freedomgpt.com/"}, {"id": "0xf610f88085f5955bccb50431e1315a28335522d87be5000ff334274cc9985741", "name": "Google", "admin": "0x4033cf3fc79c1356f62f16899810bdb5a756edb0", "startsAt": "1736967591", "minimalDeposit": "0", "withdrawLockPeriodAfterDeposit": "604800", "claimLockEnd": "0", "totalUsers": "1", "users": [], "description": "", "website": ""}, {"id": "0xf8c784db930f5b824609b2a64bc7135b089666624ba6e3a8cca427eafcf572cd", "name": "Venice", "admin": "0x3666f9a79647c81b3b0aab98b9834f5020e762e0", "startsAt": "1737124200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "81", "users": [], "description": "Venice.AI is a private, uncensored AI platform prioritizing user data privacy.", "website": "https://venice.ai/chat"}, {"id": "0xfc9d100f7a082467b344c807c5952038c31abe80caefbdeeb884ce252e15351a", "name": "Protection and Capital Incentive", "admin": "0x65485deeceaf608c8304978ca0fca1c49f5308ae", "startsAt": "1742159100", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "604800", "claimLockEnd": "1742159100", "totalUsers": "1", "users": [], "description": "", "website": ""}, {"id": "0xfd1572a2a5e1bc5b350e34b0789326b6a02c14b357c67405d537eb4159df029d", "name": "Unocoin", "admin": "******************************************", "startsAt": "1739979000", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1740065400", "totalUsers": "1", "users": [], "description": "India's Trusted Bitcoin & Crypto Trading Platform", "website": "https://unocoin.com/in/"}, {"id": "0xfde7d8a5de1071db3911b562a5647dedd538ff73d01aaa7862658dec569d0707", "name": "Grakl", "admin": "******************************************", "startsAt": "1741928400", "minimalDeposit": "1000000000000000000", "withdrawLockPeriodAfterDeposit": "604800", "claimLockEnd": "1742101200", "totalUsers": "4", "users": [], "description": "Crypto Protocol Research Assistant", "website": "https://grakl.ai/"}, {"id": "0xff2a83dce744d5c9b6dfe11813d09104cf0d01b7ef1b3ab4bc228b50c0f692d4", "name": "Hyperbolic", "admin": "******************************************", "startsAt": "1737124200", "minimalDeposit": "1000000000000000", "withdrawLockPeriodAfterDeposit": "2592000", "claimLockEnd": "1739577600", "totalUsers": "3", "users": [], "description": "Accessible, affordable, and scalable GPU resources", "website": "https://hyperbolic.xyz/"}]