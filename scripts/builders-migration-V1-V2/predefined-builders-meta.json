[{"id": "dc70d793-3b34-4a37-b730-8b5d34be00cd", "name": "6079", "description": "Movement to ensure AI respects individual and knowledge freedom.", "longDescription": "", "image": "https://prod-files-secure.s3.us-west-2.amazonaws.com/3a01387c-361e-45b8-93e6-cdb5818741cc/f5122fa3-79b7-4578-8c48-4f6bfd3d44be/Frame_427321695.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45FSPPWI6X%2F20250114%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20250114T124517Z&X-Amz-Expires=3600&X-Amz-Signature=ddc10ea062ad93172d9d9daf24d47d3f682119b6e1130969b6967c965a9d697b&X-Amz-SignedHeaders=host&x-id=GetObject", "localImage": "images/6079-dc70d793-3b34-4a37-b730-8b5d34be00cd.png", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "To Be Announced", "website": "https://6079.ai/"}, {"id": "f2e0e447-26b5-4467-960a-280e4f14cae", "name": "<PERSON><PERSON><PERSON>", "description": "Decentralized Compute Marketplace", "longDescription": "", "image": "https://prod-files-secure.s3.us-west-2.amazonaws.com/3a01387c-361e-45b8-93e6-cdb5818741cc/f125a69e-32a3-49d5-9a34-b714f7575c31/Frame_427321695.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45FSPPWI6X%2F20250114%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20250114T124517Z&X-Amz-Expires=3600&X-Amz-Signature=bc654a06bc6082d6102958866c3da04de476df07b3fdb4ea9a3509ec15599e3b&X-Amz-SignedHeaders=host&x-id=GetObject", "localImage": "images/akash-network-f2e0e447-26b5-4467-960a-280e4f14ca6e.png", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "To Be Announced", "website": "https://akash.network/"}, {"id": "452f9d53-230c-48f1-bfa8-2da0c89bcefa", "name": "AO Arweave", "description": "Decentralized Permanent information storage", "longDescription": "", "image": "https://prod-files-secure.s3.us-west-2.amazonaws.com/3a01387c-361e-45b8-93e6-cdb5818741cc/a2e42c47-db9c-426d-8ec0-2fe5db42f028/Frame_427321695.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45FSPPWI6X%2F20250114%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20250114T124517Z&X-Amz-Expires=3600&X-Amz-Signature=cbc82a22f6125feae51033679ba463ecaa76f8a92b5728922c5582989139b342&X-Amz-SignedHeaders=host&x-id=GetObject", "localImage": "images/ao-arweave-452f9d53-230c-48f1-bfa8-2da0c89bcefa.png", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "To Be Announced", "website": "https://ao.arweave.dev/"}, {"id": "e3c17d9f-f1f0-4f09-b8a0-7d4ce329f374", "name": "<PERSON><PERSON>", "description": "The Decentralized Back-End For All Node Data", "longDescription": "", "image": "https://prod-files-secure.s3.us-west-2.amazonaws.com/3a01387c-361e-45b8-93e6-cdb5818741cc/f234a1c5-f844-4950-bf64-b5b0043a5d17/arkeo.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45FSPPWI6X%2F20250114%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20250114T124517Z&X-Amz-Expires=3600&X-Amz-Signature=78fa23caf0eab3b3d0c2c0ef4eb9c01d06515f50824816060579e1e136a67e09&X-Amz-SignedHeaders=host&x-id=GetObject", "localImage": "images/arkeo-e3c17d9f-f1f0-4f09-b8a0-7d4ce329f374.jpg", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "To Be Announced", "website": "https://arkeo.network/"}, {"id": "8c95ab4f-3028-453c-8aad-f25d3b0cbbb7", "name": "Assisterr AI", "description": "Network of Small Language Models", "longDescription": "", "image": "https://prod-files-secure.s3.us-west-2.amazonaws.com/3a01387c-361e-45b8-93e6-cdb5818741cc/24052fbb-ca54-4fb6-ad83-b4638eae66c3/assisterr.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45FSPPWI6X%2F20250114%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20250114T124517Z&X-Amz-Expires=3600&X-Amz-Signature=e5b2fabb470597a86a39b2920e74c3d5722ade15678c9038827512fc6aa437b8&X-Amz-SignedHeaders=host&x-id=GetObject", "localImage": "images/assisterr-ai-8c95ab4f-3028-453c-8aad-f25d3b0cbbb7.png", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "To Be Announced", "website": ""}, {"id": "0e9c31fb-0b39-4f83-8145-7cafc23a3685", "name": "ATX DAO", "description": "Austin DAO", "longDescription": "", "image": "https://prod-files-secure.s3.us-west-2.amazonaws.com/3a01387c-361e-45b8-93e6-cdb5818741cc/0ba75b40-dcb0-4e2d-bf03-d4b5c5f8831a/atx-dao.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45FSPPWI6X%2F20250114%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20250114T124517Z&X-Amz-Expires=3600&X-Amz-Signature=07e5d03eb6117b98a10a544c96240466ec10a77ac69aded3d15c16d271556a5b&X-Amz-SignedHeaders=host&x-id=GetObject", "localImage": "images/atx-dao-0e9c31fb-0b39-4f83-8145-7cafc23a3685.jpg", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "To Be Announced", "website": "https://www.atxdao.com/"}, {"id": "b872895b-8566-4def-a059-5a270687e746", "name": "Based Agent", "description": "", "longDescription": "", "image": "https://prod-files-secure.s3.us-west-2.amazonaws.com/3a01387c-361e-45b8-93e6-cdb5818741cc/32172c5f-f8c6-4b5c-878c-fc1e22e6d8d3/based.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45FSPPWI6X%2F20250114%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20250114T124517Z&X-Amz-Expires=3600&X-Amz-Signature=e60cc53e6b4e7188f63dbce2d6c9b4e12f4ed00e79fc38479d7dc2d6c45bd08e&X-Amz-SignedHeaders=host&x-id=GetObject", "localImage": "images/based-agent-b872895b-8566-4def-a059-5a270687e746.jpg", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "To Be Announced", "website": ""}, {"id": "f4688b7f-ed79-43bc-9e64-2a5ec6c15745", "name": "Biconomy", "description": "Cross-chain relayer to improve user experience and developer adoption in the Web3 space", "longDescription": "", "image": "https://prod-files-secure.s3.us-west-2.amazonaws.com/3a01387c-361e-45b8-93e6-cdb5818741cc/a8ea8cc9-29d8-4c33-b844-1e238b26b8e7/biconomy.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45FSPPWI6X%2F20250114%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20250114T124517Z&X-Amz-Expires=3600&X-Amz-Signature=ba64549f9fd05226100d043ea91b1bb16cbdfb71132daede4b61498206ea14f0&X-Amz-SignedHeaders=host&x-id=GetObject", "localImage": "images/biconomy-f4688b7f-ed79-43bc-9e64-2a5ec6c15745.png", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "To Be Announced", "website": "https://mee.biconomy.io/"}, {"id": "fe57c776-c03e-4223-96db-b43e78138684", "name": "Bittensor", "description": "Decentralized production of artificial intelligence", "longDescription": "", "image": "https://prod-files-secure.s3.us-west-2.amazonaws.com/3a01387c-361e-45b8-93e6-cdb5818741cc/d18064f0-0b41-4aee-bb01-8000db72d49d/bittensor.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45FSPPWI6X%2F20250114%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20250114T124517Z&X-Amz-Expires=3600&X-Amz-Signature=33656deef9eaacdbdf15d040c3fdbda7aa013b6ccd9c7ba3e2d627502dc9d61e&X-Amz-SignedHeaders=host&x-id=GetObject", "localImage": "images/bittensor-fe57c776-c03e-4223-96db-b43e78138684.jpg", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "To Be Announced", "website": ""}, {"id": "d728bdc6-968d-4168-97a5-fc0b4a6a8644", "name": "CETI", "description": "Globally distributed, high-performance, scalable decentralized AI infrastructure", "longDescription": "", "image": "https://prod-files-secure.s3.us-west-2.amazonaws.com/3a01387c-361e-45b8-93e6-cdb5818741cc/f83bd325-be0f-472f-9bb0-f7a19dc9d331/ceti.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45FSPPWI6X%2F20250114%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20250114T124517Z&X-Amz-Expires=3600&X-Amz-Signature=42c7fc3ac032692b163bc5f727bfafb4af25de426a11178e5f72226ed5abba41&X-Amz-SignedHeaders=host&x-id=GetObject", "localImage": "images/ceti-d728bdc6-968d-4168-97a5-fc0b4a6a8644.jpg", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "To Be Announced", "website": "https://taoceti.ai/"}, {"id": "0ce5d1bd-fe44-4d64-8755-bd997fa423b6", "name": "coincap", "description": "Reliable cryptocurrency prices and market capitalizations", "longDescription": "", "image": "https://prod-files-secure.s3.us-west-2.amazonaws.com/3a01387c-361e-45b8-93e6-cdb5818741cc/9debd5bf-38eb-4090-a606-fbbef2d188e1/Frame_427321696.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45FSPPWI6X%2F20250114%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20250114T124517Z&X-Amz-Expires=3600&X-Amz-Signature=38fd1d562763aab4012f4ec2ce1b4d5ea96d0f06a58fab180f59bbd4f9eba0dc&X-Amz-SignedHeaders=host&x-id=GetObject", "localImage": "images/coincap-0ce5d1bd-fe44-4d64-8755-bd997fa423b6.png", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "20% APR / API Access", "website": "https://coincap.io/"}, {"id": "ccd5c537-6c5b-47b4-8028-0b3193da32ee", "name": "DAIS", "description": "Decentralized AI Society", "longDescription": "", "image": "https://prod-files-secure.s3.us-west-2.amazonaws.com/3a01387c-361e-45b8-93e6-cdb5818741cc/f33b5f67-7dc8-4756-ab87-63968bd1f333/ai-s.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45FSPPWI6X%2F20250114%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20250114T124517Z&X-Amz-Expires=3600&X-Amz-Signature=0296329a972533cd7c2c60e7d8865a8332ca9a9cc195fac0490553df5058e918&X-Amz-SignedHeaders=host&x-id=GetObject", "localImage": "images/dais-ccd5c537-6c5b-47b4-8028-0b3193da32ee.jpg", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "To Be Announced", "website": "https://dais.global/"}, {"id": "111b6519-0d6f-808b-95d1-e101dea41c15", "name": "Dueiq", "description": "Real-time insights and analysis to make smarter, safer decisions.", "longDescription": "", "image": "", "localImage": "", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "To Be Announced", "website": ""}, {"id": "f54cff5d-bb3c-4ec9-9d9a-d2b32e192e6d", "name": "exaBITS", "description": "The AI compute base layer", "longDescription": "", "image": "https://prod-files-secure.s3.us-west-2.amazonaws.com/3a01387c-361e-45b8-93e6-cdb5818741cc/07a4c588-8204-4643-b793-55bd713afa54/exabits.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45FSPPWI6X%2F20250114%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20250114T124517Z&X-Amz-Expires=3600&X-Amz-Signature=b768e7a61eb6a5be57a7998d4ede884799ccc081f7e72afd12e57088f127eabe&X-Amz-SignedHeaders=host&x-id=GetObject", "localImage": "images/exabits-f54cff5d-bb3c-4ec9-9d9a-d2b32e192e6d.png", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "To Be Announced", "website": "https://www.exabits.ai/"}, {"id": "b91e4daa-0c34-4a22-b933-896e7834f9bf", "name": "Filecoin", "description": "Decentralized storage network", "longDescription": "", "image": "https://prod-files-secure.s3.us-west-2.amazonaws.com/3a01387c-361e-45b8-93e6-cdb5818741cc/ae5e70b0-de8e-4b45-93e5-fc40bdc9ca90/Frame_427321693.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45FSPPWI6X%2F20250114%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20250114T124517Z&X-Amz-Expires=3600&X-Amz-Signature=88fe30a65b59f57de1c5fda3bb11f606a5049d7d97a808226a82b098a1e3fca6&X-Amz-SignedHeaders=host&x-id=GetObject", "localImage": "images/filecoin-b91e4daa-0c34-4a22-b933-896e7834f9bf.png", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "To Be Announced", "website": ""}, {"id": "82b61046-6fab-4dec-8d11-880595b550b0", "name": "Flock.io", "description": "Federated Machine Learning On the Blockchain", "longDescription": "", "image": "https://prod-files-secure.s3.us-west-2.amazonaws.com/3a01387c-361e-45b8-93e6-cdb5818741cc/6942f308-cd04-47a8-afde-cc01c58f757a/flockio.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45FSPPWI6X%2F20250114%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20250114T124517Z&X-Amz-Expires=3600&X-Amz-Signature=ad9fa4c8fd25645286d80276d647c26e0703f6cd4ac8a03d32baf26a11cf1504&X-Amz-SignedHeaders=host&x-id=GetObject", "localImage": "images/flock-io-82b61046-6fab-4dec-8d11-880595b550b0.png", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "To Be Announced", "website": "https://www.flock.io/"}, {"id": "0036a155-c812-4b24-8f22-39e55768df38", "name": "Freedom GPT", "description": "Access to Uncensored AI", "longDescription": "", "image": "https://prod-files-secure.s3.us-west-2.amazonaws.com/3a01387c-361e-45b8-93e6-cdb5818741cc/ab433ef9-ba5e-42dd-a8c1-662ad4887c0a/freedom.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45FSPPWI6X%2F20250114%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20250114T124517Z&X-Amz-Expires=3600&X-Amz-Signature=9ce964777dbbf3360679d291017be05a29972b5ad0ccb6526193d41bcb514d06&X-Amz-SignedHeaders=host&x-id=GetObject", "localImage": "images/freedom-gpt-0036a155-c812-4b24-8f22-39e55768df38.jpg", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "To Be Announced", "website": "https://www.freedomgpt.com/"}, {"id": "6cfd3133-a237-42e4-9e06-fc26e24f93ad", "name": "<PERSON> Layer", "description": "Decentralized AI-powered smart contracts with access to the the Internet", "longDescription": "", "image": "https://prod-files-secure.s3.us-west-2.amazonaws.com/3a01387c-361e-45b8-93e6-cdb5818741cc/7796fbd4-46d3-47e1-b872-5b7a89637a9a/genlayer.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45FSPPWI6X%2F20250114%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20250114T124517Z&X-Amz-Expires=3600&X-Amz-Signature=9bde9aef1324b5ea3fa94628b939d52af79c021b82f013484c81226f6aaeed45&X-Amz-SignedHeaders=host&x-id=GetObject", "localImage": "images/gen-layer-6cfd3133-a237-42e4-9e06-fc26e24f93ad.jpg", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "To Be Announced", "website": "https://www.genlayer.com/"}, {"id": "c9bc21bc-c466-4fdc-a8c4-b19ce0f51983", "name": "Gensyn", "description": "The network for machine intelligence", "longDescription": "", "image": "https://prod-files-secure.s3.us-west-2.amazonaws.com/3a01387c-361e-45b8-93e6-cdb5818741cc/a301079a-c4fc-41d5-922b-4cfd170ec879/gensyn.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45FSPPWI6X%2F20250114%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20250114T124517Z&X-Amz-Expires=3600&X-Amz-Signature=2d36c229adb5ed27a0471effc3f583250a52c3d89d1ee2b843c1cf1d777fa935&X-Amz-SignedHeaders=host&x-id=GetObject", "localImage": "images/gensyn-c9bc21bc-c466-4fdc-a8c4-b19ce0f51983.png", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "To Be Announced", "website": ""}, {"id": "3ae359b6-6264-49d8-acc7-a019ecf50db1", "name": "Hyperbolic", "description": "Accessible, affordable, and scalable GPU resources", "longDescription": "", "image": "https://prod-files-secure.s3.us-west-2.amazonaws.com/3a01387c-361e-45b8-93e6-cdb5818741cc/ab72bdef-d6f9-480e-afd6-74905c565d46/hyperbolic.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45FSPPWI6X%2F20250114%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20250114T124517Z&X-Amz-Expires=3600&X-Amz-Signature=125349ed11d88c6b7deec9ec77f1cae02bbc17813314c87d739c70e9f3cc6d25&X-Amz-SignedHeaders=host&x-id=GetObject", "localImage": "images/hyperbolic-3ae359b6-6264-49d8-acc7-a019ecf50db1.jpg", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "To Be Announced", "website": "https://hyperbolic.xyz/"}, {"id": "de4b7d87-2a88-4cc6-9d6c-3ccdf94cc916", "name": "Lifted", "description": "", "longDescription": "", "image": "https://prod-files-secure.s3.us-west-2.amazonaws.com/3a01387c-361e-45b8-93e6-cdb5818741cc/8e00e72d-5ea5-4221-b779-0ec165c4a129/lifted.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45FSPPWI6X%2F20250114%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20250114T124517Z&X-Amz-Expires=3600&X-Amz-Signature=440a5402e85f4fe75783533667e97e8f75b9ee61ccd650dc8ea3e2b865210671&X-Amz-SignedHeaders=host&x-id=GetObject", "localImage": "images/lifted-de4b7d87-2a88-4cc6-9d6c-3ccdf94cc916.jpg", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "To Be Announced", "website": ""}, {"id": "e7f42dc5-7e66-486f-ae3f-c631533c5541", "name": "Llama Network AI", "description": "", "longDescription": "", "image": "https://prod-files-secure.s3.us-west-2.amazonaws.com/3a01387c-361e-45b8-93e6-cdb5818741cc/02d128ce-13b9-4317-90a0-36b542140ca8/Llama-net.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45FSPPWI6X%2F20250114%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20250114T124517Z&X-Amz-Expires=3600&X-Amz-Signature=533923ba8ac9d205cea28a500431d9ab89064f3621d66e739f1314835841b6aa&X-Amz-SignedHeaders=host&x-id=GetObject", "localImage": "images/llama-network-ai-e7f42dc5-7e66-486f-ae3f-c631533c5541.jpg", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "To Be Announced", "website": ""}, {"id": "6fd5736d-11a2-4fb1-986e-68bea06af328", "name": "<PERSON><PERSON><PERSON>", "description": "Secure and decentralized platform for buying, selling and mining Bitcoin", "longDescription": "", "image": "https://prod-files-secure.s3.us-west-2.amazonaws.com/3a01387c-361e-45b8-93e6-cdb5818741cc/d523b08f-021b-4467-81b6-3cc164938973/lumerin.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45FSPPWI6X%2F20250114%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20250114T124517Z&X-Amz-Expires=3600&X-Amz-Signature=977fd9ec0e7659bda120477919047b49e68538df4686e4c9a1eb1dc729af99fb&X-Amz-SignedHeaders=host&x-id=GetObject", "localImage": "images/lumerin-6fd5736d-11a2-4fb1-986e-68bea06af328.png", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "To Be Announced", "website": "https://www.lumerin.io/"}, {"id": "f9576476-5a01-4aa9-90f6-60774fee4a6e", "name": "Manifest Network", "description": "Decentralized AI Platform", "longDescription": "", "image": "https://prod-files-secure.s3.us-west-2.amazonaws.com/3a01387c-361e-45b8-93e6-cdb5818741cc/91030777-51df-4a66-b7e7-754b00eeb9ce/manifest.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45FSPPWI6X%2F20250114%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20250114T124517Z&X-Amz-Expires=3600&X-Amz-Signature=a12af19ac2b8584cbf11ffa2668c0952e5070fe29640f1be28310c7a995d5cc9&X-Amz-SignedHeaders=host&x-id=GetObject", "localImage": "images/manifest-ai-f9576476-5a01-4aa9-90f6-60774fee4a6e.jpg", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "To Be Announced", "website": "https://manifestai.org/"}, {"id": "faba9889-7550-41fe-8af3-81bf696bd412", "name": "<PERSON><PERSON><PERSON>", "description": "Stats and tools for the Morpheus Community", "longDescription": "", "image": "https://prod-files-secure.s3.us-west-2.amazonaws.com/3a01387c-361e-45b8-93e6-cdb5818741cc/539abbde-2fd9-45b3-8022-92258a344d07/morlord.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45FSPPWI6X%2F20250114%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20250114T124517Z&X-Amz-Expires=3600&X-Amz-Signature=01e533f928c231d07438de30fd4f5d9fb91c724cd568dec666657f5a890e0d4d&X-Amz-SignedHeaders=host&x-id=GetObject", "localImage": "images/morlord-faba9889-7550-41fe-8af3-81bf696bd412.jpg", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "To Be Announced", "website": "https://morlord.com/"}, {"id": "1f37f91c-63fa-47b5-ae4e-eda78e689072", "name": "Near", "description": "Near Blockchain For AI", "longDescription": "", "image": "https://prod-files-secure.s3.us-west-2.amazonaws.com/3a01387c-361e-45b8-93e6-cdb5818741cc/5b1423c2-6a4a-4bab-84b3-45a28e998bb7/near.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45FSPPWI6X%2F20250114%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20250114T124517Z&X-Amz-Expires=3600&X-Amz-Signature=62cd95d64edc3756d5d59455ba375452eaf851a658875e3c9d6d41011d12ae49&X-Amz-SignedHeaders=host&x-id=GetObject", "localImage": "images/near-1f37f91c-63fa-47b5-ae4e-eda78e689072.jpg", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "To Be Announced", "website": "https://near.org/"}, {"id": "55207887-7fea-4e01-96e0-a8c2cf60b340", "name": "Nounspace", "description": "Decentralized, Customizable Social Network", "longDescription": "", "image": "https://prod-files-secure.s3.us-west-2.amazonaws.com/3a01387c-361e-45b8-93e6-cdb5818741cc/aeaf64a8-1e74-4ef9-8e5e-f496afede790/Frame_427321695.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45FSPPWI6X%2F20250114%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20250114T124517Z&X-Amz-Expires=3600&X-Amz-Signature=5f21ada5a78a55c5a30188cdaf1f08aa9ab1a6ed59f4e92a4a1fa8644659e608&X-Amz-SignedHeaders=host&x-id=GetObject", "localImage": "images/nounspace-55207887-7fea-4e01-96e0-a8c2cf60b340.png", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "Special nOGs NFT", "website": "https://nounspace.com/"}, {"id": "3b76d19e-6035-462a-b4e8-9482614b1d4b", "name": "OLAS", "description": "Own a share of AI, specifically autonomous agent economies", "longDescription": "", "image": "https://prod-files-secure.s3.us-west-2.amazonaws.com/3a01387c-361e-45b8-93e6-cdb5818741cc/50b9a7b3-5c02-4ae3-8794-757279e93094/olas-network.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45FSPPWI6X%2F20250114%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20250114T124517Z&X-Amz-Expires=3600&X-Amz-Signature=a5a6ce4b0e45b7da47ec9f85cd9a49082f1d6e4c2a9e53e63bc482023a9cff97&X-Amz-SignedHeaders=host&x-id=GetObject", "localImage": "images/olas-3b76d19e-6035-462a-b4e8-9482614b1d4b.jpg", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "To Be Announced", "website": "https://olas.network/"}, {"id": "606b8f89-7375-4bba-b9ea-2594a190ea55", "name": "Phala Network", "description": "AI Coprocessor for blockchain", "longDescription": "", "image": "https://prod-files-secure.s3.us-west-2.amazonaws.com/3a01387c-361e-45b8-93e6-cdb5818741cc/e2c71a5e-5a1d-4dc7-8d24-70565b1b69e0/phalanet.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45FSPPWI6X%2F20250114%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20250114T124517Z&X-Amz-Expires=3600&X-Amz-Signature=606b7bf36a8398d3aca50d010a52f02545b788fa9deb1e9932e3225cc7764010&X-Amz-SignedHeaders=host&x-id=GetObject", "localImage": "images/phala-network-606b8f89-7375-4bba-b9ea-2594a190ea55.jpg", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "To Be Announced", "website": "https://phala.network/"}, {"id": "640ce707-dfe3-49d3-99c3-c8fd000b26c9", "name": "Renascence", "description": "Security audits made simple", "longDescription": "", "image": "https://prod-files-secure.s3.us-west-2.amazonaws.com/3a01387c-361e-45b8-93e6-cdb5818741cc/c3a527ce-69fa-4e70-894e-255168d9ede4/renacenselabs.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45FSPPWI6X%2F20250114%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20250114T124517Z&X-Amz-Expires=3600&X-Amz-Signature=6caff21c14c22017656bc5803ce3593eb38e76e1ff4b2d465de51f95c661d0e5&X-Amz-SignedHeaders=host&x-id=GetObject", "localImage": "images/renascence.png", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "To Be Announced", "website": "https://renascence-labs.xyz/"}, {"id": "638c5b90-4142-4eda-96ae-2794324869f5", "name": "<PERSON><PERSON>", "description": "AI Life coach", "longDescription": "", "image": "https://prod-files-secure.s3.us-west-2.amazonaws.com/3a01387c-361e-45b8-93e6-cdb5818741cc/b3c4dcf1-f9a2-41ab-9ec4-2eb3f791fc7b/rosey.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45FSPPWI6X%2F20250114%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20250114T124517Z&X-Amz-Expires=3600&X-Amz-Signature=68ddbeac069d745c12b50de1108f18482fef856c97c0ce246b0d85a5f41f5cd7&X-Amz-SignedHeaders=host&x-id=GetObject", "localImage": "images/rosey-ai-638c5b90-4142-4eda-96ae-2794324869f5.jpg", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "To Be Announced", "website": ""}, {"id": "6b915c51-d356-425d-b3c4-e9c6d9821238", "name": "Sapien AI", "description": "Train AI with Expert Human Feedback", "longDescription": "", "image": "https://prod-files-secure.s3.us-west-2.amazonaws.com/3a01387c-361e-45b8-93e6-cdb5818741cc/44e93986-3a73-4139-afa8-803881e488fc/sapien.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45FSPPWI6X%2F20250114%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20250114T124517Z&X-Amz-Expires=3600&X-Amz-Signature=7e8a293ba978afe1ebd939d9a0d52a69fcbae9c24c1b5dc31f9a22f7753d9edf&X-Amz-SignedHeaders=host&x-id=GetObject", "localImage": "images/sapien-ai-6b915c51-d356-425d-b3c4-e9c6d9821238.jpg", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "To Be Announced", "website": "https://www.sapien.io/"}, {"id": "1d5c71eb-918a-4c70-b287-85fc3592c171", "name": "Shapeshift", "description": "Multichain Crypto Exchange", "longDescription": "", "image": "https://prod-files-secure.s3.us-west-2.amazonaws.com/3a01387c-361e-45b8-93e6-cdb5818741cc/501d8265-83d5-4987-be24-70e4ab91022d/shapeshift.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45FSPPWI6X%2F20250114%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20250114T124517Z&X-Amz-Expires=3600&X-Amz-Signature=3e0049531fe00322e4ad249599d3a48e3ba506450214aa6447062cbc5d2d791b&X-Amz-SignedHeaders=host&x-id=GetObject", "localImage": "images/shapeshift-1d5c71eb-918a-4c70-b287-85fc3592c171.jpg", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "To Be Announced", "website": "https://shapeshift.com/"}, {"id": "b66fc9c6-6f87-4dd0-9d9d-5dc1745e0c1f", "name": "<PERSON><PERSON><PERSON>", "description": "The Future of Agentic Finance Powered by Swarms", "longDescription": "", "image": "https://prod-files-secure.s3.us-west-2.amazonaws.com/3a01387c-361e-45b8-93e6-cdb5818741cc/65b87a4d-6ca1-4879-b008-b5ba7310d8e7/Frame_427321695.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45FSPPWI6X%2F20250114%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20250114T124517Z&X-Amz-Expires=3600&X-Amz-Signature=538be632aa2c8ebecd339e9e4efab091b75e392a59ead16cb653289c8324048e&X-Amz-SignedHeaders=host&x-id=GetObject", "localImage": "images/theoriq-b66fc9c6-6f87-4dd0-9d9d-5dc1745e0c1f.png", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "To Be Announced", "website": "https://www.theoriq.ai/"}, {"id": "0e998b04-53b2-41ad-9d66-2932dbe23e35", "name": "Topology", "description": "An early-stage frontier tech firm.", "longDescription": "", "image": "https://prod-files-secure.s3.us-west-2.amazonaws.com/3a01387c-361e-45b8-93e6-cdb5818741cc/02c45b96-d61f-463f-bdae-be028af80b9d/topology.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45FSPPWI6X%2F20250114%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20250114T124517Z&X-Amz-Expires=3600&X-Amz-Signature=49479a2e94a7a9121625decad0881cd8ad8d2aa4060fe086e5955ef332d7caad&X-Amz-SignedHeaders=host&x-id=GetObject", "localImage": "images/topology-0e998b04-53b2-41ad-9d66-2932dbe23e35.jpg", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "To Be Announced", "website": ""}, {"id": "f9df1f1f-e7ff-4c33-b2f9-ad6dcd0a1721", "name": "Venice", "description": "Venice.AI is a private, uncensored AI platform prioritizing user data privacy.", "longDescription": "", "image": "https://prod-files-secure.s3.us-west-2.amazonaws.com/3a01387c-361e-45b8-93e6-cdb5818741cc/959ed682-8c4b-48bd-96f8-bf811fca0bb7/Frame_427321694.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45FSPPWI6X%2F20250114%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20250114T124517Z&X-Amz-Expires=3600&X-Amz-Signature=20d1f504a9c9168e7287f8164c6148957ecab48aa14b8c9200e11a7da0d8dcee&X-Amz-SignedHeaders=host&x-id=GetObject", "localImage": "images/venice-f9df1f1f-e7ff-4c33-b2f9-ad6dcd0a1721.png", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "To Be Announced", "website": "https://venice.ai/chat"}, {"id": "02f63cdd-2bf8-4853-9f6c-5710d86e2d80", "name": "Wire Network", "description": "The Blockchain for the AI Economy", "longDescription": "", "image": "https://prod-files-secure.s3.us-west-2.amazonaws.com/3a01387c-361e-45b8-93e6-cdb5818741cc/610e4cc4-1d00-4e09-80b8-dad415bb1eab/wire-net.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45FSPPWI6X%2F20250114%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20250114T124517Z&X-Amz-Expires=3600&X-Amz-Signature=1a6e13f861f35a84e30b0fa5222458a16ab292c568f3e7e72e438d56aa5c7c8b&X-Amz-SignedHeaders=host&x-id=GetObject", "localImage": "images/wire-network-02f63cdd-2bf8-4853-9f6c-5710d86e2d80.jpg", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "To Be Announced", "website": "https://www.wire.network/"}, {"id": "d9bc2735-4579-4f73-a0c9-f4b1afa076cb", "name": "ZO.ME", "description": "Chat with friends & AI", "longDescription": "", "image": "https://prod-files-secure.s3.us-west-2.amazonaws.com/3a01387c-361e-45b8-93e6-cdb5818741cc/e6f6b8ce-82b5-4390-9055-af20ea02cc20/Frame_427321695.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45FSPPWI6X%2F20250114%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20250114T124517Z&X-Amz-Expires=3600&X-Amz-Signature=e4f49a63cfe2712f785daa89c0391fe0d16efedbd69ae2e5dcab4e7f52d6928f&X-Amz-SignedHeaders=host&x-id=GetObject", "localImage": "images/zome.jpeg", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "To Be Announced", "website": "https://zo.me/"}, {"id": "d9bc2735-4579-4f73-a0c9-f4b1afa076cb", "name": "Rainfall", "description": "Empower of people via self-sovereign Personal AI", "longDescription": "", "image": "https://prod-files-secure.s3.us-west-2.amazonaws.com/3a01387c-361e-45b8-93e6-cdb5818741cc/e6f6b8ce-82b5-4390-9055-af20ea02cc20/Frame_427321695.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45FSPPWI6X%2F20250114%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20250114T124517Z&X-Amz-Expires=3600&X-Amz-Signature=e4f49a63cfe2712f785daa89c0391fe0d16efedbd69ae2e5dcab4e7f52d6928f&X-Amz-SignedHeaders=host&x-id=GetObject", "localImage": "images/Rainfalllogo.png", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "To Be Announced", "website": "https://rainfall.one/"}, {"id": "d9bc2735-4579-4f73-a0c9-f4b1afa076cb", "name": "Bloq", "description": "Infrastructure & Applications for Web3", "longDescription": "", "image": "https://prod-files-secure.s3.us-west-2.amazonaws.com/3a01387c-361e-45b8-93e6-cdb5818741cc/e6f6b8ce-82b5-4390-9055-af20ea02cc20/Frame_427321695.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45FSPPWI6X%2F20250114%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20250114T124517Z&X-Amz-Expires=3600&X-Amz-Signature=e4f49a63cfe2712f785daa89c0391fe0d16efedbd69ae2e5dcab4e7f52d6928f&X-Amz-SignedHeaders=host&x-id=GetObject", "localImage": "images/bloq.jpeg", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "To Be Announced", "website": "https://bloq.com/"}, {"id": "d9bc2735-4579-4f73-a0c9-f4b1afa076cb", "name": "<PERSON><PERSON>", "description": "The Hemi Network is a modular Layer-2 protocol for Bitcoin and Ethereum", "longDescription": "", "image": "https://prod-files-secure.s3.us-west-2.amazonaws.com/3a01387c-361e-45b8-93e6-cdb5818741cc/e6f6b8ce-82b5-4390-9055-af20ea02cc20/Frame_427321695.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45FSPPWI6X%2F20250114%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20250114T124517Z&X-Amz-Expires=3600&X-Amz-Signature=e4f49a63cfe2712f785daa89c0391fe0d16efedbd69ae2e5dcab4e7f52d6928f&X-Amz-SignedHeaders=host&x-id=GetObject", "localImage": "images/Hemilogo.png", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "To Be Announced", "website": "https://hemi.xyz/"}, {"id": "d9bc2735-4579-4f73-a0c9-f4b1afa076cb", "name": "Virtuals", "description": "Launchpad for AI Agents", "longDescription": "", "image": "https://prod-files-secure.s3.us-west-2.amazonaws.com/3a01387c-361e-45b8-93e6-cdb5818741cc/e6f6b8ce-82b5-4390-9055-af20ea02cc20/Frame_427321695.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45FSPPWI6X%2F20250114%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20250114T124517Z&X-Amz-Expires=3600&X-Amz-Signature=e4f49a63cfe2712f785daa89c0391fe0d16efedbd69ae2e5dcab4e7f52d6928f&X-Amz-SignedHeaders=host&x-id=GetObject", "localImage": "images/Virtuals.jpeg", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "To Be Announced", "website": "https://app.virtuals.io/"}, {"id": "d9bc2735-4579-4f73-a0c9-f4b1afa076cb", "name": "AI16Z", "description": "Operating System for AI Agents", "longDescription": "", "image": "https://prod-files-secure.s3.us-west-2.amazonaws.com/3a01387c-361e-45b8-93e6-cdb5818741cc/e6f6b8ce-82b5-4390-9055-af20ea02cc20/Frame_427321695.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45FSPPWI6X%2F20250114%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20250114T124517Z&X-Amz-Expires=3600&X-Amz-Signature=e4f49a63cfe2712f785daa89c0391fe0d16efedbd69ae2e5dcab4e7f52d6928f&X-Amz-SignedHeaders=host&x-id=GetObject", "localImage": "images/ai16zlogo.jpeg", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "To Be Announced", "website": "https://www.elizaos.ai/"}, {"id": "d9bc2735-4579-4f73-a0c9-f4b1afa076cb", "name": "Gif Studios", "description": "Gif Studios", "longDescription": "", "image": "https://prod-files-secure.s3.us-west-2.amazonaws.com/3a01387c-361e-45b8-93e6-cdb5818741cc/e6f6b8ce-82b5-4390-9055-af20ea02cc20/Frame_427321695.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45FSPPWI6X%2F20250114%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20250114T124517Z&X-Amz-Expires=3600&X-Amz-Signature=e4f49a63cfe2712f785daa89c0391fe0d16efedbd69ae2e5dcab4e7f52d6928f&X-Amz-SignedHeaders=host&x-id=GetObject", "localImage": "images/signal-2025-01-24-13-14-17-231-1.png", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "To Be Announced", "website": "https://gifstudios.com/"}, {"id": "d9bc2735-4579-4f73-a0c9-f4b1afa076cb", "name": "IamAI-Core", "description": "I am AI Core", "longDescription": "", "image": "https://prod-files-secure.s3.us-west-2.amazonaws.com/3a01387c-361e-45b8-93e6-cdb5818741cc/e6f6b8ce-82b5-4390-9055-af20ea02cc20/Frame_427321695.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45FSPPWI6X%2F20250114%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20250114T124517Z&X-Amz-Expires=3600&X-Amz-Signature=e4f49a63cfe2712f785daa89c0391fe0d16efedbd69ae2e5dcab4e7f52d6928f&X-Amz-SignedHeaders=host&x-id=GetObject", "localImage": "images/iamai-core-icon-circle-512x512.png", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "To Be Announced", "website": "https://github.com/iamai-core"}, {"id": "d9bc2735-4579-4f73-a0c9-f4b1afa076cb", "name": "Lumerin MOR Staking", "description": "Bitcoin Hashpower, AI, RWAs & DePIN", "longDescription": "", "image": "https://prod-files-secure.s3.us-west-2.amazonaws.com/3a01387c-361e-45b8-93e6-cdb5818741cc/e6f6b8ce-82b5-4390-9055-af20ea02cc20/Frame_427321695.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45FSPPWI6X%2F20250114%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20250114T124517Z&X-Amz-Expires=3600&X-Amz-Signature=e4f49a63cfe2712f785daa89c0391fe0d16efedbd69ae2e5dcab4e7f52d6928f&X-Amz-SignedHeaders=host&x-id=GetObject", "localImage": "images/lumerin-6fd5736d-11a2-4fb1-986e-68bea06af328.png", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "To Be Announced", "website": "https://www.lumerin.io/"}, {"id": "d9bc2735-4579-4f73-a0c9-f4b1afa076cb", "name": "Morlord MOR Staking", "description": "Stats and tools for the Morpheus Community", "longDescription": "", "image": "https://prod-files-secure.s3.us-west-2.amazonaws.com/3a01387c-361e-45b8-93e6-cdb5818741cc/e6f6b8ce-82b5-4390-9055-af20ea02cc20/Frame_427321695.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45FSPPWI6X%2F20250114%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20250114T124517Z&X-Amz-Expires=3600&X-Amz-Signature=e4f49a63cfe2712f785daa89c0391fe0d16efedbd69ae2e5dcab4e7f52d6928f&X-Amz-SignedHeaders=host&x-id=GetObject", "localImage": "images/morlord-faba9889-7550-41fe-8af3-81bf696bd412.jpg", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "To Be Announced", "website": "https://morlord.com/"}, {"id": "", "name": "Aiora Agency", "description": "Aiora Agency", "longDescription": "", "image": "", "localImage": "images/LogoforAiora.jpeg", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "To Be Announced", "website": ""}, {"id": "f9df1f1f-e7ff-4c33-b2f9-ad6dcd0a1721", "name": "Venice Pro", "description": "Venice.AI is a private, uncensored AI platform prioritizing user data privacy.", "longDescription": "", "image": "https://prod-files-secure.s3.us-west-2.amazonaws.com/3a01387c-361e-45b8-93e6-cdb5818741cc/959ed682-8c4b-48bd-96f8-bf811fca0bb7/Frame_427321694.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAT73L2G45FSPPWI6X%2F20250114%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20250114T124517Z&X-Amz-Expires=3600&X-Amz-Signature=20d1f504a9c9168e7287f8164c6148957ecab48aa14b8c9200e11a7da0d8dcee&X-Amz-SignedHeaders=host&x-id=GetObject", "localImage": "images/VeniceAIlogo.webp", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "To Be Announced", "website": "https://venice.ai/chat"}, {"id": "", "name": "brainpower", "description": "Brainpower Podcast", "longDescription": "", "image": "", "localImage": "images/brainpower.png", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "To Be Announced", "website": "https://open.spotify.com/show/56eTDgUARPiaUntXLnxIGg"}, {"id": "", "name": "MOR Builders", "description": "Supporting, curating and accelerating Morpheus builders.", "longDescription": "", "image": "", "localImage": "images/mor-builders-white-bkrd-square.png", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "To Be Announced", "website": "https://morbuilders.xyz/"}, {"id": "", "name": "Aigent Z", "description": "Aigent Z", "longDescription": "", "image": "", "localImage": "images/aigentz.jpg", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "To Be Announced", "website": ""}, {"id": "", "name": "PALcapital", "description": "Creating a launchpad, dealmaking and introduction platform for the Morpheus ecosystem hosted by PALcapital.com.", "longDescription": "", "image": "", "localImage": "images/palcapital.png", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "To Be Announced", "website": "https://www.palcapital.com/"}, {"id": "", "name": "Pattern", "description": "Agentic RAG protocol", "longDescription": "", "image": "", "localImage": "images/Pattern.png", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "To Be Announced", "website": "https://pattern.global/"}, {"id": "", "name": "Agent Access", "description": "Access to Agents", "longDescription": "", "image": "", "localImage": "images/AgentAccess.png", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "Agent Access", "website": ""}, {"id": "", "name": "Morpheus Node", "description": "Morpheus Node Written in Node JS Programming Language", "longDescription": "", "image": "", "localImage": "images/MorpheusNode.png", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "Ready for White List", "website": "https://github.com/domsteil/morpheus-node.git"}, {"id": "", "name": "Panorama", "description": "Panorama Block is an initiative being developed in collaboration between faculty and students from UCLA and other leading universities worldwide. We're building an AI-powered data hub infrastructure designed to automate DeFi plays through the deployment, commercialization, and aggregation of AI agents.", "longDescription": "", "image": "", "localImage": "images/logo-panorama.png", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "To Be Announced", "website": "https://panoramablock.com/"}, {"id": "", "name": "Tales & Conquest", "description": "An AI agent-driven roleplaying game powered by Morpheus' decentralized inference router.", "longDescription": "", "image": "", "localImage": "images/tales-and-conquests.jpg", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "Early Access / MOR Yield", "website": "https://talesgame.com "}, {"id": "", "name": "<PERSON><PERSON>", "description": "<PERSON><PERSON> is more than just an AI agent; he's the embodiment of mischief, wit, and razor-sharp market insights. Developed by the DappLooker team, <PERSON><PERSON> brings data from <PERSON><PERSON><PERSON><PERSON><PERSON> to life through playful banter and keen market analysis.", "longDescription": "", "image": "", "localImage": "images/loky.png", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "To Be Announced", "website": "https://0xloky.com/"}, {"id": "", "name": "MySuperAgent", "description": "Super Agent for everyone", "longDescription": "", "image": "", "localImage": "images/MySuperAgent.png", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "To Be Announced", "website": "https://github.com/DavidAJohnston/MySuperAgent"}, {"id": "", "name": "Morpheus Asia", "description": "Morpheus community events and tools for Asia", "longDescription": "", "image": "", "localImage": "images/morpheusasia225-06.png", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "To Be Announced", "website": "https://morpheus.asia/"}, {"id": "", "name": "NounspaceTom", "description": "The First Smart Agent on Morpheus / Venice / Nounspace", "longDescription": "", "image": "", "localImage": "images/Tom.jpg", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "To Be Announced", "website": "https://x.com/nounspacetom"}, {"id": "", "name": "Katara", "description": "Katara is a cutting-edge agentic workflow automation platform for DevX Teams", "longDescription": "", "image": "", "localImage": "images/new_katara_icon_128x128.png", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "Access to Agents", "website": "https://katara.ai"}, {"id": "", "name": "MorpheusAI", "description": "Morpheus AI is the first agent powered by the Z10N framework that makes gamifying agents simple. Z10N will make creating an agentic economy a few clicks and prompts while creating an entire ecosystem for their communities to engage.", "longDescription": "", "image": "", "localImage": "images/morpheus_ais_logo.jpeg", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "Airdrops", "website": "https://www.z10n.ai"}, {"id": "", "name": "DAIS", "description": "Decentralized AI Society", "longDescription": "", "image": "", "localImage": "images/dais_logo.png", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "To Be Announced", "website": "https://dais.global/"}, {"id": "", "name": "Frostbyte", "description": "Decentralized, local, peer-to-peer password management—your data, your control.", "longDescription": "", "image": "", "localImage": "images/FrostByte.png", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "To Be Announced", "website": "https://www.frostbyte.app/"}, {"id": "", "name": "Unocoin", "description": "India's Trusted Bitcoin & Crypto Trading Platform", "longDescription": "", "image": "", "localImage": "images/unocoin-logo-vector.png", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "To Be Announced", "website": "https://unocoin.com/in/"}, {"id": "", "name": "GrowMOR", "description": "Independent organisation dedicated to contribute to growth of MOR token and Morpheus ecosystem.", "longDescription": "", "image": "", "localImage": "images/grow-mor-logo.png", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "MOR development", "website": "https://growmor.org/"}, {"id": "", "name": "<PERSON><PERSON>", "description": "Decentralized AI prediction of live realworld data.", "longDescription": "", "image": "", "localImage": "images/satori-logo.png", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "API Access", "website": "https://www.satorinet.io/"}, {"id": "", "name": "Airvey Advisory", "description": "Supporting Contributors in Building on MOR's Open-Source AI Infrastructure.", "longDescription": "", "image": "", "localImage": "images/AirVeyBlackLogo.jpg", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "To Be Announced", "website": "http://www.airvey.io"}, {"id": "", "name": "TacoBytes", "description": "Media and Content, helping give a voice to the builders and talking about what they are doing from a different perspective.", "longDescription": "", "image": "", "localImage": "images/tacobytes.jpg", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "Marketing", "website": "https://x.com/Player1Taco"}, {"id": "", "name": "MOR API Access", "description": "Access Morpheus compute with a Rest API key and a few lines of code.", "longDescription": "", "image": "", "localImage": "images/morlord-faba9889-7550-41fe-8af3-81bf696bd412.jpg", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "API Access", "website": "https://x.com/Player1Taco"}, {"id": "", "name": "4kGpL8", "description": "", "longDescription": "", "image": "", "localImage": "", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "", "website": ""}, {"id": "", "name": "aB3dH7", "description": "", "longDescription": "", "image": "", "localImage": "", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "", "website": ""}, {"id": "", "name": "9nRt5e", "description": "", "longDescription": "", "image": "", "localImage": "", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "", "website": ""}, {"id": "", "name": "LpM2cA", "description": "", "longDescription": "", "image": "", "localImage": "", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "", "website": ""}, {"id": "", "name": "pR6tG2", "description": "", "longDescription": "", "image": "", "localImage": "", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "", "website": ""}, {"id": "", "name": "nH7lM5", "description": "", "longDescription": "", "image": "", "localImage": "", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "", "website": ""}, {"id": "", "name": "fT6aM1", "description": "", "longDescription": "", "image": "", "localImage": "", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "", "website": ""}, {"id": "", "name": "8lP2kH", "description": "", "longDescription": "", "image": "", "localImage": "", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "", "website": ""}, {"id": "", "name": "rE5dN7", "description": "", "longDescription": "", "image": "", "localImage": "", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "", "website": ""}, {"id": "", "name": "pL1cM6", "description": "", "longDescription": "", "image": "", "localImage": "", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "", "website": ""}, {"id": "", "name": "2hG4yB", "description": "", "longDescription": "", "image": "", "localImage": "", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "", "website": ""}, {"id": "", "name": "eK7nR8", "description": "", "longDescription": "", "image": "", "localImage": "", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "", "website": ""}, {"id": "", "name": "cD3fT9", "description": "", "longDescription": "", "image": "", "localImage": "", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "", "website": ""}, {"id": "", "name": "1mB2pH", "description": "", "longDescription": "", "image": "", "localImage": "", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "", "website": ""}, {"id": "", "name": "Web3 Cities Network", "description": "", "longDescription": "", "image": "", "localImage": "", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "", "website": ""}, {"id": "", "name": "FreeOpsDAO", "description": "", "longDescription": "", "image": "", "localImage": "", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "", "website": ""}, {"id": "", "name": "7yB1gF", "description": "", "longDescription": "", "image": "", "localImage": "", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "", "website": ""}, {"id": "", "name": "Decentranet", "description": "", "longDescription": "", "image": "", "localImage": "", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "", "website": ""}, {"id": "", "name": "eA8cT3", "description": "", "longDescription": "", "image": "", "localImage": "", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "", "website": ""}, {"id": "", "name": "6sN8e4", "description": "", "longDescription": "", "image": "", "localImage": "", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "", "website": ""}, {"id": "", "name": "gF2yM5", "description": "", "longDescription": "", "image": "", "localImage": "", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "", "website": ""}, {"id": "", "name": "6eB1cH", "description": "", "longDescription": "", "image": "", "localImage": "", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "", "website": ""}, {"id": "", "name": "5kP7lT", "description": "", "longDescription": "", "image": "", "localImage": "", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "", "website": ""}, {"id": "", "name": "pR9fL8", "description": "", "longDescription": "", "image": "", "localImage": "", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "", "website": ""}, {"id": "", "name": "<PERSON><PERSON>", "description": "", "longDescription": "", "image": "", "localImage": "", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "", "website": ""}, {"id": "", "name": "Distro Media", "description": "Blockchain-powered, AI-enabled news delivery platform", "longDescription": "", "image": "", "localImage": "images/distro-logo.png", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "Airdrop, Product Access", "website": "https://distro.media"}, {"id": "", "name": "ccovibe.com", "description": "Project Management and Community for AI Engineers", "longDescription": "", "image": "", "localImage": "images/ccovibe.png", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "To Be Announced", "website": "http://ccovibe.com"}, {"id": "", "name": "AI Art by Venice", "description": "A decentralized platform where your Venice AI-generated masterpieces live forever and earn rewards when your art enters the top10", "longDescription": "", "image": "", "localImage": "images/AIARTbyVenice.png", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "To Be Announced", "website": "https://aiartbyvenice_cavey65.arweave.net/"}, {"id": "", "name": "SUPERCYCLE", "description": "Crypto AI Media Network", "longDescription": "", "image": "", "localImage": "images/supercycle.png", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "To Be Announced", "website": "https://sup3rcycl3.com/"}, {"id": "", "name": "Grakl", "description": "Crypto Protocol Research Assistant", "longDescription": "", "image": "", "localImage": "images/grokl.png", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "To Be Announced", "website": "https://grakl.ai/"}, {"id": "", "name": "Mere Morpheus Podcast", "description": "Educational podcast & agent", "longDescription": "", "image": "", "localImage": "images/meremorpheus.png", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "Product Access", "website": "https://www.youtube.com/playlist?list=PL3s-NHnCxOO6NoCuMJik2Jm0_p2FJzFBh"}, {"id": "", "name": "Only Cats Fight Club", "description": "Have your NFT warrior cat fight in the OnlyCats arena!", "longDescription": "", "image": "", "localImage": "images/onlycats.png", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "To Be Announced", "website": "https://cats.pr/"}, {"id": "", "name": "Snowball Money", "description": "Hybrid smart agent that blends on-chain behavioral analysis and portfolio intelligence", "longDescription": "", "image": "", "localImage": "images/snowball.png", "tags": [], "githubUrl": "", "twitterUrl": "", "discordUrl": "", "contributors": 0, "githubStars": 0, "totalStaked": 0, "rewardType": "To Be Announced", "website": "https://snowball.money"}]