import { ethers } from 'hardhat';

import { wei } from './utils/utils';

import { Distribution } from '@/generated-types/ethers';

// import { setTime } from '@/test/helpers/block-helper';

async function main() {
  //   await setTime(1715000272);

  const owner = await ethers.getImpersonatedSigner('******************************************');

  const signer = await ethers.getImpersonatedSigner('******************************************');

  await owner.sendTransaction({
    to: signer,
    value: wei('0.1'),
  });

  const distributionFactory = await ethers.getContractFactory('Distribution', {
    libraries: {
      LinearDistributionIntervalDecrease: '******************************************',
    },
    signer: signer,
  });

  const distribution = distributionFactory.attach('******************************************') as Distribution;

  await distribution.claim(4, '******************************************', { value: wei('0.01') });

  console.log(')');
}

main().catch((error) => {
  console.error(error);
  process.exitCode = 1;
});

// npx hardhat run scripts/claim.ts --network localhost
