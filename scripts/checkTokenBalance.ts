import { ethers } from 'hardhat';

import { ERC20 } from '@/generated-types/ethers';

async function main() {
  // const user = '******************************************'; // me
  const user = '******************************************'; // token receiver
  // const tokenAddress = '******************************************'; // MOR
  const tokenAddress = '******************************************'; // wStETH
  const ERC20Factory = await ethers.getContractFactory('ERC20');
  const token = ERC20Factory.attach(tokenAddress) as ERC20;

  const balance = await token.balanceOf(user);

  console.log(`token ${tokenAddress} balance of ${user}: ${balance}`);
}

main().catch((error) => {
  console.error(error);
  process.exitCode = 1;
});

// npx hardhat run scripts/checkTokenBalance.ts --network arbitrum_goerli
