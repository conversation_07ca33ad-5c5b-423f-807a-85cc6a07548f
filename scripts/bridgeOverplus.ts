import { ethers } from 'hardhat';

import { wei } from './utils/utils';

import { Distribution } from '@/generated-types/ethers';

async function main() {
  const owner = await ethers.getImpersonatedSigner('******************************************');

  const signer = await ethers.getImpersonatedSigner('******************************************');

  await owner.sendTransaction({
    to: signer,
    value: wei('0.1'),
  });

  const distributionFactory = await ethers.getContractFactory('Distribution', {
    libraries: {
      LinearDistributionIntervalDecrease: '******************************************',
    },
    signer: signer,
  });

  const distribution = distributionFactory.attach('******************************************') as Distribution;

  // await distribution.transferOwnership(owner);

  // console.log('transferred Ownership');

  console.log(await distribution.overplus());

  await distribution.connect(owner).bridgeOverplus(100000, 60000000, 100000000000000, { value: 200000000000000 });

  console.log(await distribution.overplus());

  console.log(')');
}

main().catch((error) => {
  console.error(error);
  process.exitCode = 1;
});

// npx hardhat run scripts/bridgeOverplus.ts --network localhost
