import { ethers } from 'hardhat';

import { IL2Message<PERSON><PERSON><PERSON><PERSON>, L2M<PERSON>age<PERSON><PERSON><PERSON><PERSON>, L2TokenReceiver } from '@/generated-types/ethers';

async function main() {
  const weth = '******************************************';

  const signer = await ethers.getImpersonatedSigner('******************************************');

  const l2MessageReceiverFactory = await ethers.getContractFactory('L2MessageReceiver', signer);

  const l2MessageReceiver = l2MessageReceiverFactory.attach(
    '******************************************',
  ) as L2MessageReceiver;

  const config: IL2MessageReceiver.ConfigStruct = {
    gateway: '******************************************',
    sender: '******************************************',
    senderChainId: 101n,
  };

  console.log('config', config);

  await l2MessageReceiver.setParams('******************************************', config);

  console.log(')');
}

main().catch((error) => {
  console.error(error);
  process.exitCode = 1;
});

// npx hardhat run scripts/upgradeMor.ts --network localhost
