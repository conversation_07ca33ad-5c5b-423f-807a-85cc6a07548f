import { ethers } from 'hardhat';

import { IDistribution } from '@/generated-types/ethers';

async function main() {
  const signer = await ethers.getImpersonatedSigner('******************************************');

  const distributionFactory = await ethers.getContractFactory('Distribution', {
    libraries: {
      LinearDistributionIntervalDecrease: '******************************************',
    },
    signer: signer,
  });

  const distribution = distributionFactory.attach('******************************************') as IDistribution;

  await distribution.stake(0, 1000000000000000000n);

  console.log(')');
}

main().catch((error) => {
  console.error(error);
  process.exitCode = 1;
});

// npx hardhat run scripts/stake.ts --network localhost
