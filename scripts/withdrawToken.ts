import { ethers } from 'hardhat';

import { L2TokenReceiverV2 } from '@/generated-types/ethers';

async function main() {
  const wstEth = '******************************************';

  const signer = await ethers.getImpersonatedSigner('******************************************');

  const L2TokenReceiverV2Factory = await ethers.getContractFactory('L2TokenReceiverV2', signer);

  const l2TokenReceiver = L2TokenReceiverV2Factory.attach(
    '******************************************',
  ) as L2TokenReceiverV2;

  await l2TokenReceiver.withdrawToken('******************************************', wstEth, 729987493896647493703n);
  console.log(')');
}

main().catch((error) => {
  console.error(error);
  process.exitCode = 1;
});

// npx hardhat run scripts/withdrawToken.ts --network localhost
